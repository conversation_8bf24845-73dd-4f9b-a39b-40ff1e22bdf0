# ==============================================
# A Good Man's View - Environment Variables
# ==============================================

# ==============================================
# GENERAL CONFIGURATION
# ==============================================
NODE_ENV=development
APP_NAME="A Good Man's View"
APP_URL=http://localhost:3000
API_URL=http://localhost:4000
PORT=4000

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
DATABASE_URL=postgresql://postgres:password@localhost:5432/agoodmansview_db
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_NAME=agoodmansview_db
DATABASE_SCHEMA=public
DATABASE_SYNCHRONIZE=false
DATABASE_LOGGING=true

# ==============================================
# REDIS CONFIGURATION
# ==============================================
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ==============================================
# JWT AUTHENTICATION
# ==============================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=30d

# ==============================================
# OAUTH CONFIGURATION
# ==============================================
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret

# ==============================================
# PAYMENT GATEWAY (VISA)
# ==============================================
VISA_API_KEY=your-visa-api-key
VISA_SECRET_KEY=your-visa-secret-key
VISA_WEBHOOK_SECRET=your-visa-webhook-secret
VISA_ENVIRONMENT=sandbox

# ==============================================
# BLOCKCHAIN CONFIGURATION
# ==============================================
BLOCKCHAIN_NETWORK_ID=1337
BLOCKCHAIN_RPC_URL=http://localhost:8545
BLOCKCHAIN_WS_URL=ws://localhost:8546
BLOCKCHAIN_CONSENSUS=pos
BLOCKCHAIN_GENESIS_ACCOUNT=0x0000000000000000000000000000000000000000
BLOCKCHAIN_PRIVATE_KEY=your-blockchain-private-key

# ==============================================
# FILE STORAGE
# ==============================================
STORAGE_TYPE=local
STORAGE_LOCAL_PATH=./uploads
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=agoodmansview-uploads

# ==============================================
# EMAIL CONFIGURATION
# ==============================================
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_FROM=<EMAIL>
MAIL_FROM_NAME="A Good Man's View"

# ==============================================
# SMS CONFIGURATION
# ==============================================
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# ==============================================
# LOGGING
# ==============================================
LOG_LEVEL=debug
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs
LOG_MAX_FILES=10
LOG_MAX_SIZE=10m

# ==============================================
# RATE LIMITING
# ==============================================
RATE_LIMIT_TTL=60
RATE_LIMIT_MAX=100
RATE_LIMIT_SKIP_IF=false

# ==============================================
# CORS CONFIGURATION
# ==============================================
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS
CORS_CREDENTIALS=true

# ==============================================
# GRAPHQL CONFIGURATION
# ==============================================
GRAPHQL_PLAYGROUND=true
GRAPHQL_INTROSPECTION=true
GRAPHQL_DEBUG=true
GRAPHQL_SCHEMA_DESTINATION=./src/graphql/schema.gql



# ==============================================
# SECURITY
# ==============================================
BCRYPT_ROUNDS=12
ENCRYPTION_KEY=your-32-character-encryption-key
SESSION_SECRET=your-session-secret-key

# ==============================================
# FEATURE FLAGS
# ==============================================
FEATURE_BLOCKCHAIN_ENABLED=true
FEATURE_NOTIFICATIONS_ENABLED=true
FEATURE_ANALYTICS_ENABLED=true
FEATURE_SUBSCRIPTIONS_ENABLED=true

# ==============================================
# DEVELOPMENT TOOLS
# ==============================================
SWAGGER_ENABLED=true
SWAGGER_PATH=api/docs
DEBUG_MODE=true
SEED_DATABASE=false
