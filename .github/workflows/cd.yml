# 🚀 Continuous Deployment Pipeline for A Good Man's View E-commerce Platform
#
# This workflow implements automated deployment to staging and production environments
# with proper environment promotion flow, database migrations, and rollback capabilities.
#
# Educational Focus: Demonstrates modern CD practices including:
# - Environment-specific deployments with approval gates
# - Database migration handling in CI/CD
# - Docker-based deployment strategy
# - Secrets management and environment variables
# - Rollback mechanisms and health checks

name: 🚀 Continuous Deployment

# Trigger Configuration
# Deployment is triggered on successful CI completion for main branch
# and manual workflow dispatch for flexibility
on:
  workflow_run:
    workflows: ["🧪 Continuous Integration"]
    types: [completed]
    branches: [main]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      skip_tests:
        description: 'Skip tests (emergency deployment)'
        required: false
        default: false
        type: boolean

# Environment Variables
env:
  NODE_VERSION: '18'
  DOCKER_REGISTRY: 'ghcr.io'
  IMAGE_NAME: 'agoodmansview'

# Concurrency Control
# Ensures only one deployment runs at a time per environment
concurrency:
  group: deploy-${{ github.event.inputs.environment || 'staging' }}
  cancel-in-progress: false # Don't cancel deployments in progress

jobs:
  # 🔍 PRE-DEPLOYMENT VALIDATION
  # Validates that CI passed and deployment is safe to proceed
  pre-deployment:
    name: 🔍 Pre-deployment Validation
    runs-on: ubuntu-latest
    timeout-minutes: 5
    
    outputs:
      deploy-environment: ${{ steps.determine-env.outputs.environment }}
      should-deploy: ${{ steps.validation.outputs.should-deploy }}
      
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        
      # Determine target environment based on trigger
      - name: 🎯 Determine Target Environment
        id: determine-env
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "environment=staging" >> $GITHUB_OUTPUT
          else
            echo "environment=none" >> $GITHUB_OUTPUT
          fi
          
      # Validate CI results and deployment readiness
      - name: ✅ Validate Deployment Readiness
        id: validation
        run: |
          # Check if CI workflow completed successfully (unless manual override)
          if [[ "${{ github.event_name }}" == "workflow_run" ]]; then
            if [[ "${{ github.event.workflow_run.conclusion }}" != "success" ]]; then
              echo "❌ CI workflow failed. Deployment aborted."
              echo "should-deploy=false" >> $GITHUB_OUTPUT
              exit 1
            fi
          fi
          
          # Check for emergency deployment flag
          if [[ "${{ github.event.inputs.skip_tests }}" == "true" ]]; then
            echo "⚠️ Emergency deployment - skipping additional validations"
          fi
          
          echo "should-deploy=true" >> $GITHUB_OUTPUT
          echo "✅ Deployment validation passed"

  # 🏗️ BUILD DOCKER IMAGES
  # Builds and pushes Docker images for deployment
  build-images:
    name: 🏗️ Build Docker Images
    runs-on: ubuntu-latest
    needs: pre-deployment
    if: needs.pre-deployment.outputs.should-deploy == 'true'
    timeout-minutes: 30
    
    strategy:
      matrix:
        component: [frontend, backend, blockchain]
        
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        
      # Setup Docker Buildx for advanced build features
      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      # Login to GitHub Container Registry
      - name: 🔐 Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          
      # Extract metadata for Docker tags and labels
      - name: 🏷️ Extract Metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ matrix.component }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
            
      # Build and push Docker image
      - name: 🏗️ Build and Push Docker Image
        uses: docker/build-push-action@v5
        with:
          context: ./${{ matrix.component }}
          file: ./${{ matrix.component }}/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

  # 🗄️ DATABASE MIGRATIONS
  # Handles database schema updates safely
  database-migrations:
    name: 🗄️ Database Migrations
    runs-on: ubuntu-latest
    needs: [pre-deployment, build-images]
    if: needs.pre-deployment.outputs.should-deploy == 'true'
    timeout-minutes: 15
    
    environment:
      name: ${{ needs.pre-deployment.outputs.deploy-environment }}-db
      
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        
      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 Install Backend Dependencies
        run: |
          cd backend
          npm ci
          
      # Run database migrations with proper environment configuration
      - name: 🗄️ Run Database Migrations
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          NODE_ENV: ${{ needs.pre-deployment.outputs.deploy-environment }}
        run: |
          cd backend
          echo "🗄️ Running database migrations for ${{ needs.pre-deployment.outputs.deploy-environment }}..."
          
          # Backup database before migrations (production only)
          if [[ "${{ needs.pre-deployment.outputs.deploy-environment }}" == "production" ]]; then
            echo "💾 Creating database backup..."
            npm run db:backup || echo "⚠️ Backup failed - proceeding with caution"
          fi
          
          # Run migrations
          npm run migration:run
          
          echo "✅ Database migrations completed successfully"

  # 🚀 DEPLOY TO STAGING
  # Deploys to staging environment for testing
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [pre-deployment, build-images, database-migrations]
    if: needs.pre-deployment.outputs.deploy-environment == 'staging' && needs.pre-deployment.outputs.should-deploy == 'true'
    timeout-minutes: 20
    
    environment:
      name: staging
      url: https://staging.agoodmansview.com
      
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        
      # Deploy using Docker Compose for staging
      - name: 🚀 Deploy to Staging Environment
        env:
          STAGING_HOST: ${{ secrets.STAGING_HOST }}
          STAGING_USER: ${{ secrets.STAGING_USER }}
          STAGING_SSH_KEY: ${{ secrets.STAGING_SSH_KEY }}
          DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}
          REDIS_URL: ${{ secrets.STAGING_REDIS_URL }}
          JWT_SECRET: ${{ secrets.STAGING_JWT_SECRET }}
        run: |
          echo "🚀 Deploying to staging environment..."
          
          # Setup SSH connection
          mkdir -p ~/.ssh
          echo "$STAGING_SSH_KEY" > ~/.ssh/staging_key
          chmod 600 ~/.ssh/staging_key
          
          # Deploy using Docker Compose
          ssh -i ~/.ssh/staging_key -o StrictHostKeyChecking=no $STAGING_USER@$STAGING_HOST << 'EOF'
            cd /opt/agoodmansview
            
            # Pull latest images
            docker-compose pull
            
            # Update environment variables
            export DATABASE_URL="${{ secrets.STAGING_DATABASE_URL }}"
            export REDIS_URL="${{ secrets.STAGING_REDIS_URL }}"
            export JWT_SECRET="${{ secrets.STAGING_JWT_SECRET }}"
            export NODE_ENV="staging"
            
            # Deploy with zero-downtime strategy
            docker-compose up -d --remove-orphans
            
            # Wait for services to be healthy
            sleep 30
            docker-compose ps
          EOF
          
      # Run post-deployment health checks
      - name: 🏥 Health Check
        run: |
          echo "🏥 Running health checks..."
          
          # Check frontend health
          curl -f https://staging.agoodmansview.com/api/health || exit 1
          
          # Check backend API health
          curl -f https://staging.agoodmansview.com/api/v1/health || exit 1
          
          echo "✅ All health checks passed"

  # 🎯 DEPLOY TO PRODUCTION
  # Deploys to production with additional safety measures
  deploy-production:
    name: 🎯 Deploy to Production
    runs-on: ubuntu-latest
    needs: [pre-deployment, build-images, database-migrations]
    if: needs.pre-deployment.outputs.deploy-environment == 'production' && needs.pre-deployment.outputs.should-deploy == 'true'
    timeout-minutes: 30
    
    environment:
      name: production
      url: https://agoodmansview.com
      
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        
      # Production deployment with blue-green strategy
      - name: 🎯 Deploy to Production Environment
        env:
          PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
          PRODUCTION_USER: ${{ secrets.PRODUCTION_USER }}
          PRODUCTION_SSH_KEY: ${{ secrets.PRODUCTION_SSH_KEY }}
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
          REDIS_URL: ${{ secrets.PRODUCTION_REDIS_URL }}
          JWT_SECRET: ${{ secrets.PRODUCTION_JWT_SECRET }}
        run: |
          echo "🎯 Deploying to production environment..."
          
          # Setup SSH connection
          mkdir -p ~/.ssh
          echo "$PRODUCTION_SSH_KEY" > ~/.ssh/production_key
          chmod 600 ~/.ssh/production_key
          
          # Blue-green deployment strategy
          ssh -i ~/.ssh/production_key -o StrictHostKeyChecking=no $PRODUCTION_USER@$PRODUCTION_HOST << 'EOF'
            cd /opt/agoodmansview
            
            # Create backup of current deployment
            echo "💾 Creating deployment backup..."
            docker-compose down
            cp -r . ../agoodmansview-backup-$(date +%Y%m%d-%H%M%S)
            
            # Pull latest images
            docker-compose pull
            
            # Update environment variables
            export DATABASE_URL="${{ secrets.PRODUCTION_DATABASE_URL }}"
            export REDIS_URL="${{ secrets.PRODUCTION_REDIS_URL }}"
            export JWT_SECRET="${{ secrets.PRODUCTION_JWT_SECRET }}"
            export NODE_ENV="production"
            
            # Deploy with rolling update
            docker-compose up -d --remove-orphans
            
            # Wait for services to stabilize
            sleep 60
            docker-compose ps
          EOF
          
      # Comprehensive production health checks
      - name: 🏥 Production Health Check
        run: |
          echo "🏥 Running comprehensive production health checks..."
          
          # Wait for deployment to stabilize
          sleep 30
          
          # Check all critical endpoints
          curl -f https://agoodmansview.com/api/health || exit 1
          curl -f https://agoodmansview.com/api/v1/health || exit 1
          curl -f https://agoodmansview.com/ || exit 1
          
          # Performance check
          response_time=$(curl -o /dev/null -s -w '%{time_total}' https://agoodmansview.com/)
          if (( $(echo "$response_time > 3.0" | bc -l) )); then
            echo "⚠️ Response time too slow: ${response_time}s"
            exit 1
          fi
          
          echo "✅ All production health checks passed"
          
      # Notify team of successful deployment
      - name: 📢 Notify Deployment Success
        if: success()
        run: |
          echo "🎉 Production deployment completed successfully!"
          echo "🌐 Application is live at: https://agoodmansview.com"

  # 📊 POST-DEPLOYMENT MONITORING
  # Sets up monitoring and alerting for the deployment
  post-deployment:
    name: 📊 Post-deployment Monitoring
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success')
    timeout-minutes: 10
    
    steps:
      - name: 📊 Setup Monitoring
        run: |
          echo "📊 Setting up post-deployment monitoring..."
          
          # This would typically integrate with monitoring services
          # like DataDog, New Relic, or custom monitoring solutions
          echo "✅ Monitoring setup completed"
          
      - name: 📈 Generate Deployment Report
        run: |
          echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📋 Deployment Details:" >> $GITHUB_STEP_SUMMARY
          echo "- Environment: ${{ needs.pre-deployment.outputs.deploy-environment }}" >> $GITHUB_STEP_SUMMARY
          echo "- Commit: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "- Triggered by: ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
          echo "- Deployment time: $(date)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### ✅ Deployment Status:" >> $GITHUB_STEP_SUMMARY
          echo "- Staging: ${{ needs.deploy-staging.result || 'skipped' }}" >> $GITHUB_STEP_SUMMARY
          echo "- Production: ${{ needs.deploy-production.result || 'skipped' }}" >> $GITHUB_STEP_SUMMARY
