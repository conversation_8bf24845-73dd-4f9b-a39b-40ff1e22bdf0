# 🚀 Comprehensive CI Pipeline for A Good Man's View E-commerce Platform
# 
# This workflow implements a multi-layered testing strategy for our monorepo:
# - Frontend: Next.js with TypeScript, Vitest unit tests, Playwright E2E
# - Backend: NestJS with Jest unit tests, integration tests
# - Blockchain: Custom implementation testing
# - Shared: Common utilities testing
#
# Educational Focus: Each step is documented to serve as a learning resource
# for understanding modern CI/CD practices in a TypeScript monorepo.

name: 🧪 Continuous Integration

# Trigger Configuration
# The pipeline runs on pull requests and main branch pushes to ensure
# code quality before merging and after integration
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened, ready_for_review]

# Environment Variables
# Centralized configuration for consistent behavior across jobs
env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'
  # Test environment configuration
  CI: true
  NODE_ENV: test
  # Database configuration for testing
  DATABASE_URL: postgresql://postgres:postgres@localhost:5432/agoodmansview_test
  REDIS_URL: redis://localhost:6379

# Concurrency Control
# Prevents multiple CI runs for the same branch, saving resources
# and providing faster feedback on the latest changes
concurrency:
  group: ci-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # 📋 SETUP & VALIDATION JOB
  # Validates the environment and prepares the workspace for testing
  setup:
    name: 🔧 Setup & Validation
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    outputs:
      # These outputs help other jobs determine what to test
      frontend-changed: ${{ steps.changes.outputs.frontend }}
      backend-changed: ${{ steps.changes.outputs.backend }}
      blockchain-changed: ${{ steps.changes.outputs.blockchain }}
      shared-changed: ${{ steps.changes.outputs.shared }}
      
    steps:
      # Step 1: Checkout code with full history for change detection
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Full history needed for change detection
          
      # Step 2: Detect which parts of the monorepo have changed
      # This optimization allows us to skip unnecessary tests
      - name: 🔍 Detect Changes
        uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            frontend:
              - 'frontend/**'
              - 'shared/**'
              - 'package.json'
            backend:
              - 'backend/**'
              - 'shared/**'
              - 'package.json'
            blockchain:
              - 'blockchain/**'
              - 'shared/**'
              - 'package.json'
            shared:
              - 'shared/**'
              - 'package.json'
            
      # Step 3: Setup Node.js with caching for faster subsequent runs
      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: |
            package-lock.json
            frontend/package-lock.json
            backend/package-lock.json
            blockchain/package-lock.json
            shared/package-lock.json
            
      # Step 4: Install dependencies for the entire monorepo
      # Using npm ci for faster, reliable, reproducible builds
      - name: 📦 Install Dependencies
        run: |
          echo "🔧 Installing root dependencies..."
          npm ci
          
          echo "🎨 Installing frontend dependencies..."
          cd frontend && npm ci
          
          echo "🔙 Installing backend dependencies..."
          cd ../backend && npm ci
          
          echo "⛓️ Installing blockchain dependencies..."
          cd ../blockchain && npm ci
          
          echo "📚 Installing shared dependencies..."
          cd ../shared && npm ci
          
      # Step 5: Validate project structure and dependencies
      - name: ✅ Validate Project Structure
        run: |
          echo "🔍 Validating workspace structure..."
          npm run test:structure || echo "⚠️ Structure validation script not found"
          
          echo "🔍 Checking for security vulnerabilities..."
          npm audit --audit-level=high
          
  # 🧪 UNIT TESTING JOB
  # Runs unit tests for all workspaces in parallel for speed
  unit-tests:
    name: 🧪 Unit Tests
    runs-on: ubuntu-latest
    needs: setup
    timeout-minutes: 15
    
    strategy:
      # Parallel execution matrix for different workspaces
      matrix:
        workspace: [frontend, backend, blockchain, shared]
        include:
          - workspace: frontend
            test-command: 'npm run test:coverage'
            coverage-path: 'frontend/coverage'
          - workspace: backend
            test-command: 'npm run test:cov'
            coverage-path: 'backend/coverage'
          - workspace: blockchain
            test-command: 'npm test'
            coverage-path: 'blockchain/coverage'
          - workspace: shared
            test-command: 'npm test'
            coverage-path: 'shared/coverage'
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        
      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 Install Dependencies
        run: |
          npm ci
          cd ${{ matrix.workspace }} && npm ci
          
      # Conditional execution based on changes detected in setup job
      - name: 🧪 Run Unit Tests - ${{ matrix.workspace }}
        if: needs.setup.outputs[format('{0}-changed', matrix.workspace)] == 'true' || needs.setup.outputs.shared-changed == 'true'
        run: |
          cd ${{ matrix.workspace }}
          echo "🧪 Running unit tests for ${{ matrix.workspace }}..."
          ${{ matrix.test-command }}
          
      # Upload coverage reports for analysis
      - name: 📊 Upload Coverage Reports
        if: needs.setup.outputs[format('{0}-changed', matrix.workspace)] == 'true' || needs.setup.outputs.shared-changed == 'true'
        uses: codecov/codecov-action@v3
        with:
          directory: ${{ matrix.coverage-path }}
          flags: ${{ matrix.workspace }}
          name: ${{ matrix.workspace }}-coverage
          fail_ci_if_error: false # Don't fail CI if coverage upload fails

  # 🔗 INTEGRATION TESTING JOB
  # Tests the interaction between different components
  integration-tests:
    name: 🔗 Integration Tests
    runs-on: ubuntu-latest
    needs: [setup, unit-tests]
    timeout-minutes: 20
    
    # Service containers for integration testing
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: agoodmansview_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        
      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📦 Install Dependencies
        run: |
          npm ci
          cd frontend && npm ci
          cd ../backend && npm ci
          
      # Database setup for integration tests
      - name: 🗄️ Setup Test Database
        if: needs.setup.outputs.backend-changed == 'true'
        run: |
          cd backend
          echo "🗄️ Running database migrations..."
          npm run migration:run || echo "⚠️ No migrations to run"
          
      # Frontend integration tests (API calls, data flow)
      - name: 🔗 Frontend Integration Tests
        if: needs.setup.outputs.frontend-changed == 'true' || needs.setup.outputs.backend-changed == 'true'
        run: |
          cd frontend
          echo "🔗 Running frontend integration tests..."
          npm run test -- --run tests/integration/
          
      # Backend integration tests (database, external services)
      - name: 🔗 Backend Integration Tests
        if: needs.setup.outputs.backend-changed == 'true'
        run: |
          cd backend
          echo "🔗 Running backend integration tests..."
          npm run test:e2e

  # 🎭 END-TO-END TESTING JOB
  # Tests complete user workflows using Playwright
  e2e-tests:
    name: 🎭 End-to-End Tests
    runs-on: ubuntu-latest
    needs: [setup, unit-tests]
    timeout-minutes: 30

    strategy:
      matrix:
        # Test across different browsers for comprehensive coverage
        browser: [chromium, firefox, webkit]

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: |
          npm ci
          cd frontend && npm ci

      # Install Playwright browsers
      - name: 🎭 Install Playwright Browsers
        if: needs.setup.outputs.frontend-changed == 'true'
        run: |
          cd frontend
          npx playwright install ${{ matrix.browser }} --with-deps

      # Build the application for E2E testing
      - name: 🏗️ Build Application
        if: needs.setup.outputs.frontend-changed == 'true'
        run: |
          cd frontend
          npm run build

      # Run E2E tests with specific browser
      - name: 🎭 Run E2E Tests - ${{ matrix.browser }}
        if: needs.setup.outputs.frontend-changed == 'true'
        run: |
          cd frontend
          echo "🎭 Running E2E tests on ${{ matrix.browser }}..."
          npx playwright test --project=${{ matrix.browser }}

      # Upload test results and artifacts
      - name: 📊 Upload E2E Test Results
        if: always() && needs.setup.outputs.frontend-changed == 'true'
        uses: actions/upload-artifact@v3
        with:
          name: e2e-results-${{ matrix.browser }}
          path: |
            frontend/test-results/
            frontend/playwright-report/
          retention-days: 7

  # ⚡ PERFORMANCE TESTING JOB
  # Tests frontend bundle size and load times
  performance-tests:
    name: ⚡ Performance Tests
    runs-on: ubuntu-latest
    needs: [setup, unit-tests]
    timeout-minutes: 15

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: |
          npm ci
          cd frontend && npm ci

      # Build and analyze bundle size
      - name: 📦 Bundle Size Analysis
        if: needs.setup.outputs.frontend-changed == 'true'
        run: |
          cd frontend
          echo "📦 Building and analyzing bundle size..."
          npm run build:analyze

      # Performance testing with Lighthouse CI
      - name: ⚡ Lighthouse Performance Audit
        if: needs.setup.outputs.frontend-changed == 'true'
        uses: treosh/lighthouse-ci-action@v10
        with:
          configPath: './frontend/.lighthouserc.json'
          uploadArtifacts: true
          temporaryPublicStorage: true

  # ♿ ACCESSIBILITY TESTING JOB
  # Ensures WCAG compliance and accessibility standards
  accessibility-tests:
    name: ♿ Accessibility Tests
    runs-on: ubuntu-latest
    needs: [setup, unit-tests]
    timeout-minutes: 15

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: |
          npm ci
          cd frontend && npm ci

      # Install accessibility testing tools
      - name: ♿ Install Accessibility Tools
        if: needs.setup.outputs.frontend-changed == 'true'
        run: |
          npm install -g @axe-core/cli pa11y

      # Build application for accessibility testing
      - name: 🏗️ Build Application
        if: needs.setup.outputs.frontend-changed == 'true'
        run: |
          cd frontend
          npm run build
          npm run start &
          sleep 10 # Wait for server to start

      # Run accessibility audits
      - name: ♿ Run Accessibility Audits
        if: needs.setup.outputs.frontend-changed == 'true'
        run: |
          echo "♿ Running axe-core accessibility tests..."
          axe http://localhost:3000 --exit

          echo "♿ Running pa11y accessibility tests..."
          pa11y http://localhost:3000 --standard WCAG2AA

  # 🔒 SECURITY TESTING JOB
  # Scans for security vulnerabilities and code quality issues
  security-tests:
    name: 🔒 Security Tests
    runs-on: ubuntu-latest
    needs: setup
    timeout-minutes: 15

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: npm ci

      # Dependency vulnerability scanning
      - name: 🔍 Dependency Vulnerability Scan
        run: |
          echo "🔍 Scanning for dependency vulnerabilities..."
          npm audit --audit-level=moderate

      # CodeQL security analysis
      - name: 🔒 CodeQL Security Analysis
        uses: github/codeql-action/init@v2
        with:
          languages: typescript, javascript

      - name: 🔒 Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2

  # 🏗️ BUILD VERIFICATION JOB
  # Ensures all components build successfully
  build-verification:
    name: 🏗️ Build Verification
    runs-on: ubuntu-latest
    needs: [setup, unit-tests]
    timeout-minutes: 20

    strategy:
      matrix:
        workspace: [frontend, backend, blockchain, shared]

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: |
          npm ci
          cd ${{ matrix.workspace }} && npm ci

      # Build each workspace
      - name: 🏗️ Build ${{ matrix.workspace }}
        if: needs.setup.outputs[format('{0}-changed', matrix.workspace)] == 'true' || needs.setup.outputs.shared-changed == 'true'
        run: |
          cd ${{ matrix.workspace }}
          echo "🏗️ Building ${{ matrix.workspace }}..."
          npm run build

      # Upload build artifacts for deployment
      - name: 📦 Upload Build Artifacts
        if: needs.setup.outputs[format('{0}-changed', matrix.workspace)] == 'true' || needs.setup.outputs.shared-changed == 'true'
        uses: actions/upload-artifact@v3
        with:
          name: build-${{ matrix.workspace }}
          path: |
            ${{ matrix.workspace }}/dist/
            ${{ matrix.workspace }}/.next/
            ${{ matrix.workspace }}/build/
          retention-days: 7

  # ✅ CI SUMMARY JOB
  # Provides a summary of all CI results
  ci-summary:
    name: ✅ CI Summary
    runs-on: ubuntu-latest
    needs: [setup, unit-tests, integration-tests, e2e-tests, performance-tests, accessibility-tests, security-tests, build-verification]
    if: always()
    timeout-minutes: 5

    steps:
      - name: 📊 Generate CI Summary
        run: |
          echo "## 🚀 CI Pipeline Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📋 Job Results:" >> $GITHUB_STEP_SUMMARY
          echo "- Setup: ${{ needs.setup.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Unit Tests: ${{ needs.unit-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Integration Tests: ${{ needs.integration-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- E2E Tests: ${{ needs.e2e-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Performance Tests: ${{ needs.performance-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Accessibility Tests: ${{ needs.accessibility-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Security Tests: ${{ needs.security-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Build Verification: ${{ needs.build-verification.result }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔍 Changed Components:" >> $GITHUB_STEP_SUMMARY
          echo "- Frontend: ${{ needs.setup.outputs.frontend-changed }}" >> $GITHUB_STEP_SUMMARY
          echo "- Backend: ${{ needs.setup.outputs.backend-changed }}" >> $GITHUB_STEP_SUMMARY
          echo "- Blockchain: ${{ needs.setup.outputs.blockchain-changed }}" >> $GITHUB_STEP_SUMMARY
          echo "- Shared: ${{ needs.setup.outputs.shared-changed }}" >> $GITHUB_STEP_SUMMARY

      # Fail the job if any critical tests failed
      - name: ❌ Check Critical Test Results
        if: needs.unit-tests.result == 'failure' || needs.security-tests.result == 'failure' || needs.build-verification.result == 'failure'
        run: |
          echo "❌ Critical tests failed. Please review the results above."
          exit 1
