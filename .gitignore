# Dependencies
node_modules/
/.pnp
.pnp.js
.pnp.loader.mjs

# Build outputs
/build
/dist
/out
**/dist/
**/build/
*.d.ts
*.js.map

# Next.js build outputs and cache
.next/
out/
.vercel/
.turbo/

# Testing
/coverage
coverage/
*.lcov
.nyc_output/

# Environment variables
.env
.env.local
.env.development
.env.test.local
.env.production
.env.*.local

# Editor directories and files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# TypeScript cache
*.tsbuildinfo
.tscache/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Webpack cache
.cache/
.webpack/

# Parcel cache
.parcel-cache/

# Storybook build outputs
storybook-static/

# Temporary folders
tmp/
temp/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# Snowpack dependency directory
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production

# parcel-bundler cache
.cache
.parcel-cache



# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Database files
*.db
*.sqlite
*.sqlite3

# Docker
.dockerignore
docker-compose.override.yml

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Backup files
*.backup
*.bak
*.tmp

# Package manager lock files (optional - uncomment if you want to ignore them)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml