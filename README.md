# A Good Man's View - Multi-Vendor E-commerce Platform

<p align="center">
  <img src="https://img.shields.io/badge/Status-In%20Development-yellow" alt="Project Status">
  <img src="https://img.shields.io/badge/Frontend-Next.js%20TS-blue" alt="Frontend">
  <img src="https://img.shields.io/badge/Backend-NestJS-red" alt="Backend">
  <img src="https://img.shields.io/badge/Blockchain-PoS-purple" alt="Blockchain">
  <img src="https://img.shields.io/badge/License-MIT-blue" alt="License">
</p>

<p align="center">
  <em>A secure, transparent, and modern multi-vendor e-commerce platform for the South African market</em>
</p>

<p align="center">
  <a href="#-features">Features</a> •
  <a href="#-tech-stack">Tech Stack</a> •
  <a href="#-getting-started">Getting Started</a> •
  <a href="#-project-structure">Project Structure</a> •
  <a href="#-development-roadmap">Roadmap</a>
</p>

---

## 🌍 Overview

**A Good Man's View** is a comprehensive multi-vendor e-commerce platform specifically designed for the South African market. The platform combines traditional e-commerce functionality with cutting-edge blockchain technology to provide transparency, security, and trust for both buyers and vendors.

### 🎯 Key Highlights
- **🔐 Blockchain-Powered**: Private blockchain with Proof of Stake consensus
- **💳 Integrated Payments**: Custom digital wallets with Visa gateway integration
- **🇿🇦 South African Focus**: ZAR currency support and local market optimization
- **📊 Vendor Subscriptions**: Tiered plans with flexible commission structures
- **⚡ Modern Stack**: Next.js TypeScript frontend with NestJS backend

---

## ✨ Features

### 🛒 **For Buyers**
- **Product Discovery**: Advanced search, filtering, and categorization
- **Secure Shopping**: Blockchain-verified transactions and order tracking
- **Digital Wallet**: Integrated wallet with Visa deposit/withdrawal support
- **User Management**: Profile, address book, and transaction history
- **Mobile Responsive**: Optimized experience across all devices

### 🏪 **For Vendors**
- **Store Management**: Complete product listing and inventory control
- **Subscription Plans**: Flexible tiers with varying features and commissions
- **Analytics Dashboard**: Sales reports and blockchain transaction logs
- **Order Processing**: Streamlined order management and fulfillment
- **Payout System**: Automated payouts through digital wallet integration

### 🔗 **Blockchain Features**
- **Proof of Stake**: Energy-efficient consensus mechanism
- **Transaction Transparency**: All purchases and payouts recorded on-chain
- **Digital Wallets**: Secure, blockchain-based wallet for each user
- **Smart Contracts**: Automated vendor payouts and subscription management

---

## 🛠️ Tech Stack

### **Frontend**
- **Framework**: Next.js 14+ with TypeScript
- **Styling**: Tailwind CSS 3.3.0
- **UI Components**: Custom component library
- **State Management**: Zustand/Redux Toolkit
- **Testing**: Jest + React Testing Library

### **Backend** *(Planned)*
- **Framework**: NestJS with TypeScript
- **API Architecture**: Hybrid GraphQL + RESTful APIs
- **GraphQL**: Apollo Server integration with NestJS
- **Database**: PostgreSQL with TypeORM/Prisma
- **Authentication**: JWT + OAuth2 implementation
- **API Documentation**: Swagger/OpenAPI + GraphQL Playground
- **Validation**: Class-validator + Class-transformer

### **Blockchain** *(Planned)*
- **Type**: Private blockchain with Proof of Stake
- **Smart Contracts**: Custom implementation for transactions
- **Wallet Integration**: Custom digital wallet service

### **Payments & Infrastructure**
- **Payment Gateway**: Visa integration
- **Currency**: South African Rand (ZAR)
- **Deployment**: TBD (AWS/Azure/GCP)

---

## 🚀 Getting Started

### Prerequisites
- Node.js (latest LTS version recommended)
- npm or yarn package manager
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/agoodmansview_website.git
   cd agoodmansview_website
   ```

2. **Install frontend dependencies**
   ```bash
   cd frontend
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Development Workflow
```bash
# Start frontend development server (Next.js)
cd frontend && npm run dev

# Start backend development server (NestJS with GraphQL + REST) - when available
cd backend && npm run start:dev

# Access GraphQL Playground (when backend is running)
# Navigate to http://localhost:4000/graphql

# Access REST API documentation (Swagger)
# Navigate to http://localhost:4000/api/docs

# Run tests
cd frontend && npm test
cd backend && npm run test

# Build for production
cd frontend && npm run build
cd backend && npm run build
```

---

## 📁 Project Structure

### Current Structure
```
agoodmansview_website/
├── frontend/                    # Next.js TypeScript application
│   ├── public/                 # Static assets
│   ├── src/                    # Source code
│   │   ├── app/               # App Router (Next.js 13+)
│   │   ├── components/        # Reusable UI components
│   │   ├── lib/              # Utility libraries and configurations
│   │   ├── hooks/            # Custom React hooks
│   │   ├── services/         # API services
│   │   ├── types/            # TypeScript type definitions
│   │   └── styles/           # Global styles and themes
│   ├── package.json          # Frontend dependencies
│   ├── next.config.js        # Next.js configuration
│   └── tailwind.config.js    # Tailwind CSS configuration
├── README.md                  # Project documentation
└── .gitignore                # Git ignore rules
```

### Planned Full Structure
```
agoodmansview_website/
├── frontend/                   # Next.js TypeScript application (✅ Implemented)
├── backend/                    # NestJS Hybrid API server (🚧 Planned)
│   ├── src/
│   │   ├── modules/           # Feature modules (users, products, orders)
│   │   │   ├── auth/         # Authentication module (REST + GraphQL)
│   │   │   ├── users/        # User management module (GraphQL)
│   │   │   ├── products/     # Product catalog module (GraphQL)
│   │   │   ├── orders/       # Order processing module (REST)
│   │   │   ├── payments/     # Payment processing (REST)
│   │   │   └── wallet/       # Digital wallet module (REST + GraphQL)
│   │   ├── graphql/          # GraphQL schema, resolvers, and types
│   │   ├── common/           # Shared decorators, guards, filters
│   │   ├── config/           # Configuration files and schemas
│   │   ├── database/         # Database entities and migrations
│   │   └── shared/           # Shared services and utilities
│   ├── test/                 # E2E and integration tests
│   ├── docs/                 # API documentation (REST + GraphQL)
│   └── package.json          # Backend dependencies
├── blockchain/                # Private blockchain implementation (🚧 Planned)
│   ├── consensus/            # Proof of Stake implementation
│   ├── contracts/            # Smart contracts
│   ├── wallet/               # Digital wallet service
│   └── network/              # P2P network layer
├── shared/                    # Shared types and utilities (🚧 Planned)
│   ├── types/                # Common TypeScript definitions
│   └── constants/            # Shared constants
├── docs/                      # Project documentation (✅ Architecture Complete)
│   ├── architecture/         # Database architecture documentation
│   │   ├── README.md        # Architecture overview and navigation
│   │   ├── business-requirements.md  # Business requirements analysis
│   │   ├── data-flow-analysis.md     # Data flow patterns and dependencies
│   │   ├── database-schema.md        # Complete normalized schema design
│   │   ├── schema-design.sql         # PostgreSQL schema definition
│   │   ├── erd.md                    # Entity Relationship Diagram documentation
│   │   ├── indexing-strategy.md      # Database performance optimization
│   │   ├── validation-rules.md       # Data validation and business rules
│   │   └── schema-review.md          # Review process and stakeholder approval
│   ├── diagram-export-19-06-2025-10_57_58.png  # Visual ERD diagram
│   ├── api/                  # API documentation (🚧 Planned)
│   └── deployment/           # Deployment guides (🚧 Planned)
└── docker-compose.yml        # Development environment setup (🚧 Planned)
```

---

## 🗓️ Development Roadmap

### Phase 1: Foundation (Weeks 1-4) ✅ *In Progress*
- [x] Project initialization and setup
- [x] Frontend Next.js application with TypeScript
- [x] Tailwind CSS integration
- [x] Backend NestJS API server setup
- [x] Database schema design with TypeORM/Prisma ✅ **COMPLETE**
- [ ] JWT authentication system

### Phase 2: Core Backend (Weeks 5-8) 🚧 *Planned*
- [ ] NestJS server setup with GraphQL + REST hybrid
- [ ] User management and JWT authentication (REST)
- [ ] Product catalog system (GraphQL)
- [ ] Vendor registration and management (GraphQL)
- [ ] Basic order processing (REST)
- [ ] Payment gateway integration (REST)

### Phase 3: Blockchain Integration (Weeks 9-12) 🚧 *Planned*
- [ ] Private blockchain implementation
- [ ] Proof of Stake consensus mechanism
- [ ] Digital wallet service
- [ ] Smart contracts for transactions
- [ ] Blockchain transaction recording

### Phase 4: Frontend Development (Weeks 13-16) 🚧 *Planned*
- [ ] Buyer interface (product browsing, cart, checkout)
- [ ] Vendor dashboard (product management, orders)
- [ ] Wallet interface and transaction history
- [ ] Responsive design and mobile optimization

### Phase 5: Advanced Features (Weeks 17-20) 🚧 *Planned*
- [ ] Subscription management system
- [ ] Advanced analytics and reporting
- [ ] Search and recommendation engine
- [ ] Multi-language support (English/Afrikaans)

### Phase 6: Testing & Deployment (Weeks 21-24) 🚧 *Planned*
- [ ] Comprehensive testing suite
- [ ] Security audit and penetration testing
- [ ] Performance optimization
- [ ] Production deployment and monitoring

---

## 🎯 Current Sprint Status

### Active Development Focus
- **Phase 1**: Foundation setup and frontend development
- **Current Priority**: Next.js application structure and core components
- **Next Milestone**: Backend NestJS server setup with hybrid GraphQL + REST API

### Recent Completed Tasks
- ✅ Project initialization and repository setup
- ✅ Next.js TypeScript application with App Router
- ✅ Tailwind CSS integration and configuration
- ✅ Basic project structure and documentation

### Upcoming Tasks
- 🚧 Backend NestJS server setup
- 🚧 Database schema design with PostgreSQL
- 🚧 JWT authentication implementation
- 🚧 Core API endpoints (REST + GraphQL hybrid)

---

## 🎨 Design System

### Color Palette
- **Primary**: South African inspired colors (green, gold, blue)
- **Secondary**: Modern e-commerce palette
- **Accent**: Trust and security focused colors

### Typography
- **Headings**: Modern, clean sans-serif
- **Body**: Readable, accessible font stack
- **Code**: Monospace for technical elements

### Components
- Responsive design principles
- Accessibility-first approach
- Consistent spacing and layout
- Mobile-optimized interactions

---

## 🔧 API Documentation

### Hybrid API Architecture *(Planned)*

Our platform uses a **hybrid approach** combining GraphQL and RESTful APIs to optimize for different use cases:

- **GraphQL**: Complex queries, real-time data, flexible client requirements
- **REST**: Simple operations, file uploads, third-party integrations, caching

### GraphQL Endpoints *(Planned)*
```
# GraphQL Playground available at /graphql
```
# User Management Queries
query GetUser($id: ID!) {
  user(id: $id) {
    id
    email
    profile {
      firstName
      lastName
      avatar
    }
    wallet {
      balance
      transactions {
        id
        amount
        type
        createdAt
      }
    }
  }
}

# Product Catalog Queries
```
query GetProducts($filter: ProductFilter, $pagination: PaginationInput) {
  products(filter: $filter, pagination: $pagination) {
    edges {
      node {
        id
        name
        description
        price
        vendor {
          id
          name
          rating
        }
        reviews {
          rating
          comment
          user {
            firstName
          }
        }
      }
    }
    pageInfo {
      hasNextPage
      hasPreviousPage
      totalCount
    }
  }
}

# Real-time Subscriptions
subscription OrderUpdates($userId: ID!) {
  orderStatusChanged(userId: $userId) {
    id
    status
    trackingNumber
    estimatedDelivery
  }
}
```
bash
```
### RESTful Endpoints *(Planned)*
```bash
# Authentication & Session Management
POST   /api/v1/auth/register     # User registration
POST   /api/v1/auth/login        # User login
POST   /api/v1/auth/logout       # User logout
POST   /api/v1/auth/refresh      # Refresh JWT token
POST   /api/v1/auth/forgot       # Password reset

# Order Processing (REST for simplicity)
POST   /api/v1/orders            # Create new order
PUT    /api/v1/orders/:id/status # Update order status
GET    /api/v1/orders/:id/track  # Track order
POST   /api/v1/orders/:id/cancel # Cancel order

# Payment Processing (REST for security)
POST   /api/v1/payments/process  # Process payment
POST   /api/v1/payments/refund   # Process refund
GET    /api/v1/payments/:id      # Get payment details
POST   /api/v1/payments/webhook  # Payment gateway webhook

# File Uploads (REST for file handling)
POST   /api/v1/upload/product    # Upload product images
POST   /api/v1/upload/avatar     # Upload user avatar
DELETE /api/v1/upload/:id        # Delete uploaded file

# Wallet Operations (REST for transactions)
POST   /api/v1/wallet/deposit    # Deposit funds
POST   /api/v1/wallet/withdraw   # Withdraw funds
POST   /api/v1/wallet/transfer   # Transfer between wallets

# Blockchain Integration (REST for external calls)
POST   /api/v1/blockchain/record # Record transaction on blockchain
GET    /api/v1/blockchain/verify # Verify blockchain transaction
```

### API Usage Guidelines *(Planned)*

**Use GraphQL for:**
- Complex data fetching with nested relationships
- Real-time updates and subscriptions
- Mobile apps requiring flexible queries
- Dashboard analytics with custom data requirements

**Use REST for:**
- Authentication and session management
- File uploads and downloads
- Payment processing and webhooks
- Simple CRUD operations
- Third-party service integrations
- Operations requiring specific HTTP status codes

---

## 🚀 CI/CD Pipeline

### 📋 Pipeline Overview

Our comprehensive CI/CD pipeline implements modern DevOps practices with a focus on quality, security, and educational value. The pipeline is designed as a learning resource while maintaining production-grade standards.

```mermaid
graph TD
    A[Code Push/PR] --> B[🔧 Setup & Validation]
    B --> C[🧪 Unit Tests]
    B --> D[🔗 Integration Tests]
    B --> E[🎭 E2E Tests]
    B --> F[⚡ Performance Tests]
    B --> G[♿ Accessibility Tests]
    B --> H[🔒 Security Tests]

    C --> I[🏗️ Build Verification]
    D --> I
    E --> I
    F --> I
    G --> I
    H --> I

    I --> J[✅ CI Summary]
    J --> K{Main Branch?}
    K -->|Yes| L[🚀 Deploy to Staging]
    K -->|No| M[End]

    L --> N[🏥 Health Checks]
    N --> O{Manual Approval}
    O -->|Approved| P[🎯 Deploy to Production]
    O -->|Rejected| Q[End]

    P --> R[📊 Post-deployment Monitoring]
```

### 🧪 Multi-Layered Testing Strategy

#### **Continuous Integration (CI)**
- **Triggers**: Pull requests and main branch pushes
- **Duration**: ~45 minutes for complete pipeline
- **Parallel Execution**: Optimized for speed with workspace-specific testing
- **Change Detection**: Smart testing based on modified components

#### **Testing Layers**

**1. 🧪 Unit Tests**
- **Frontend**: Vitest + React Testing Library
- **Backend**: Jest + NestJS Testing utilities
- **Blockchain**: Custom test suite for consensus and contracts
- **Shared**: Jest for utility functions
- **Coverage**: Minimum 80% code coverage required

**2. 🔗 Integration Tests**
- **API Integration**: REST and GraphQL endpoint testing
- **Database Operations**: PostgreSQL integration with test containers
- **External Services**: Mocked third-party service interactions
- **Cross-workspace Communication**: Shared package integration

**3. 🎭 End-to-End Tests**
- **Tool**: Playwright with multi-browser support
- **Browsers**: Chromium, Firefox, WebKit, Mobile Chrome/Safari
- **Scenarios**: Complete user workflows from registration to checkout
- **Visual Testing**: Screenshot comparison and responsive design verification

**4. ⚡ Performance Tests**
- **Tool**: Lighthouse CI + Bundle Analyzer
- **Metrics**: Core Web Vitals, bundle size, load times
- **Thresholds**: Performance score >90, FCP <2s, LCP <3s, CLS <0.1
- **Monitoring**: Automated performance regression detection

**5. ♿ Accessibility Tests**
- **Tools**: axe-core + pa11y
- **Standards**: WCAG 2.1 Level AA compliance
- **Coverage**: Keyboard navigation, screen readers, color contrast
- **Score**: Target >95% accessibility score

**6. 🔒 Security Tests**
- **Tools**: CodeQL + npm audit + OWASP ZAP
- **Scans**: Dependency vulnerabilities, static code analysis, secret detection
- **Standards**: Zero high/critical vulnerabilities allowed
- **Compliance**: Security best practices enforcement

### 🚀 Continuous Deployment (CD)

#### **Environment Strategy**
```
Development → Staging → Production
     ↓           ↓         ↓
   Feature    Integration  Live
   Testing     Testing    Users
```

#### **Deployment Features**
- **Staging**: Automatic deployment from main branch
- **Production**: Manual approval with blue-green strategy
- **Database Migrations**: Zero-downtime with automatic rollback
- **Health Checks**: Comprehensive post-deployment validation
- **Monitoring**: Real-time performance and error tracking

### 📊 Local Development Testing

#### **Quick Setup**
```bash
# Run complete local pipeline
./scripts/test-pipeline.sh --full

# Quick tests only (unit + lint)
./scripts/test-pipeline.sh --quick

# With coverage reports
./scripts/test-pipeline.sh --coverage

# Auto-fix issues
./scripts/test-pipeline.sh --fix
```

#### **Individual Test Commands**
```bash
# Unit tests
npm run test:unit
npm run test:frontend
npm run test:backend

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e
npm run test:e2e:ui

# Performance tests
npm run lighthouse
npm run bundle-analyzer

# Accessibility tests
npm run a11y:test
```

### 📚 Educational Features

Our pipeline serves as a comprehensive learning resource:

- **Detailed Comments**: Every workflow step is documented
- **Progressive Enhancement**: From basic CI to advanced CD features
- **Best Practices**: Industry-standard DevOps patterns
- **Troubleshooting**: Common issues and solutions documented
- **Metrics**: Performance and quality tracking

### 📖 Documentation

- **[Complete Pipeline Guide](docs/PIPELINE.md)**: Comprehensive documentation
- **[Troubleshooting Guide](docs/PIPELINE.md#troubleshooting)**: Common issues and solutions
- **[Best Practices](docs/PIPELINE.md#best-practices)**: Development and deployment guidelines
- **[Configuration Reference](docs/PIPELINE.md#configuration)**: Environment and tool setup

## 🧪 Testing Strategy (Legacy Documentation)

### Frontend Testing (Next.js)
- **Unit Tests**: Vitest + React Testing Library
- **Integration Tests**: Component interaction testing
- **E2E Tests**: Playwright for user journey testing
- **Visual Tests**: Storybook for component documentation
- **Performance Tests**: Lighthouse CI for Core Web Vitals

### Backend Testing *(NestJS Hybrid)*
- **Unit Tests**: Jest with NestJS testing utilities
- **GraphQL Tests**: Apollo Server testing for queries/mutations
- **REST API Tests**: Supertest for RESTful endpoint testing
- **Integration Tests**: Combined GraphQL + REST workflow testing
- **Database Tests**: PostgreSQL test containers
- **Security Tests**: Authentication and authorization guards

### Blockchain Testing *(In Development)*
- **Consensus Tests**: PoS mechanism validation
- **Smart Contract Tests**: Contract functionality
- **Network Tests**: P2P communication testing
- **Performance Tests**: Transaction throughput

---

## 🚀 Deployment

### Development Environment
```bash
# Frontend development (Next.js)
cd frontend && npm run dev

# Backend development (NestJS - when available)
cd backend && npm run start:dev

# Full stack development (when available)
docker-compose up -d
```

### Production Deployment *(Planned)*
- **Frontend**: Vercel (optimized for Next.js) or Netlify
- **Backend**: Docker containers with NestJS (AWS ECS/GCP Cloud Run)
- **Database**: Managed PostgreSQL (AWS RDS/GCP Cloud SQL)
- **Blockchain**: Private network deployment
- **CDN**: CloudFront/CloudFlare for static assets

---

## 🤝 Contributing

We welcome contributions to the A Good Man's View platform! Please follow these guidelines:

### Development Process
1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Code Standards
- **TypeScript**: Strict type checking enabled
- **ESLint**: Follow the configured linting rules
- **Prettier**: Code formatting consistency
- **Testing**: Write tests for new features
- **Documentation**: Update docs for API changes

### Commit Convention
```
feat: add new feature
fix: bug fix
docs: documentation changes
style: formatting changes
refactor: code refactoring
test: adding tests
chore: maintenance tasks
```

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 👥 Team

- **Project Lead**: [Your Name]
- **Frontend Developer**: [Developer Name]
- **Backend Developer**: [Developer Name]
- **Blockchain Developer**: [Developer Name]

---

## 📞 Support

For support and questions:
- **Email**: <EMAIL>
- **Documentation**: [Project Wiki](link-to-wiki)
- **Issues**: [GitHub Issues](link-to-issues)

---

## 🙏 Acknowledgments

- South African e-commerce community
- Open source blockchain projects
- React and Node.js communities
- All contributors and supporters

---

<p align="center">
  <strong><a href="#a-good-mans-view---multi-vendor-e-commerce-platform">⬆ Back to Top</a></strong>
</p>

<p align="center">
  Made with ❤️ for the South African market
</p>
