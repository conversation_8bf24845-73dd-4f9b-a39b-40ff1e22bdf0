# Backend Environment Variables
# Copy this file to .env and update the values

# Application
NODE_ENV=development
PORT=4000
APP_NAME="A Good Man's View API"

# Database Configuration
# Set SKIP_DATABASE=true to run without database for development
SKIP_DATABASE=false
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_NAME=agoodmansview_db
DATABASE_SYNCHRONIZE=false
DATABASE_LOGGING=true

# Database Connection Pooling
DATABASE_MAX_CONNECTIONS=10
DATABASE_MIN_CONNECTIONS=2
DATABASE_ACQUIRE_TIMEOUT=60000
DATABASE_IDLE_TIMEOUT=10000
DATABASE_EVICT_TIMEOUT=1000

# Test Environment Database Configuration
# Use smaller connection pool for testing
# DATABASE_MAX_CONNECTIONS=5
# DATABASE_MIN_CONNECTIONS=1
# DATABASE_NAME=agoodmansview_test_db

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# CORS
CORS_ORIGIN=http://localhost:3000

# GraphQL
GRAPHQL_PLAYGROUND=true
GRAPHQL_INTROSPECTION=true

# Rate Limiting
RATE_LIMIT_TTL=60000
RATE_LIMIT_MAX=100
