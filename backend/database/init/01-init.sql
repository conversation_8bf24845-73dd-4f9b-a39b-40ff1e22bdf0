-- Initialize A Good Man's View Database
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions if they don't exist
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create additional schemas if needed
-- CREATE SCHEMA IF NOT EXISTS analytics;
-- CREATE SCHEMA IF NOT EXISTS audit;

-- Set timezone
SET timezone = 'UTC';

-- Create a read-only user for reporting (optional)
-- CREATE USER agoodmansview_readonly WITH PASSWORD 'readonly_password';
-- GRANT CONNECT ON DATABASE agoodmansview_db TO agoodmansview_readonly;
-- GRANT USAGE ON SCHEMA public TO agoodmansview_readonly;
-- GRANT SELECT ON ALL TABLES IN SCHEMA public TO agoodmansview_readonly;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO agoodmansview_readonly;

-- Log initialization
SELECT 'A Good Mans View database initialized successfully' AS status;
