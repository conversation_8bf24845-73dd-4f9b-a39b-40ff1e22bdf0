# Authentication Setup Guide

This guide explains the authentication fields and indexes that have been added to your User entity for implementing secure user registration and login functionality.

## 🔧 Database Changes

### New Fields Added

The following fields have been added to the `users` table:

| Field | Type | Default | Description |
|-------|------|---------|-------------|
| `password_reset_token` | varchar(255) | NULL | Token for password reset functionality |
| `password_reset_expires` | timestamptz | NULL | Expiration time for password reset token |
| `login_attempts` | integer | 0 | Counter for failed login attempts |
| `locked_until` | timestamptz | NULL | Account lockout expiration time |
| `two_factor_secret` | varchar(255) | NULL | Secret key for 2FA (future use) |
| `two_factor_enabled` | boolean | false | Whether 2FA is enabled for the user |

### New Indexes Added

The following indexes have been created for optimal performance:

| Index Name | Columns | Purpose |
|------------|---------|---------|
| `idx_users_login_attempts` | `login_attempts` | Quick lookup for accounts with failed attempts |
| `idx_users_locked_until` | `locked_until` | Efficient queries for locked accounts |
| `idx_users_password_reset_token` | `password_reset_token` | Fast password reset token validation |
| `idx_users_active_email` | `is_active`, `email` | Composite index for active user lookups |

## 🚀 Running the Migration

### Prerequisites

1. Ensure PostgreSQL is running:
   ```bash
   npm run db:docker:up
   ```

2. Test database connection:
   ```bash
   npm run test:db:connection
   ```

### Execute Migration

Run the authentication migration script:

```bash
# From the backend directory
./scripts/run-auth-migration.sh
```

Or manually run the migration:

```bash
npm run migration:run
```

### Verify Migration

Check that the migration was applied successfully:

```bash
npm run migration:show
```

## 🔒 Security Features Enabled

### Account Lockout Protection

- **Failed Login Tracking**: `login_attempts` field tracks consecutive failed logins
- **Account Locking**: `locked_until` field implements temporary account lockout
- **Automatic Reset**: Successful login resets the attempt counter

### Password Reset Security

- **Secure Tokens**: `password_reset_token` stores cryptographically secure reset tokens
- **Token Expiration**: `password_reset_expires` ensures tokens have limited lifetime
- **Single Use**: Tokens are invalidated after use

### Future 2FA Support

- **2FA Secret**: `two_factor_secret` ready for TOTP implementation
- **2FA Status**: `two_factor_enabled` tracks 2FA activation per user

## 📊 Virtual Properties

The User entity now includes helpful virtual properties:

```typescript
// Check if account is currently locked
user.isAccountLocked // boolean

// Check if user can attempt login
user.canAttemptLogin // boolean

// Check if user has valid password reset token
user.hasPasswordResetToken // boolean
```

## 🔍 Usage Examples

### Check Account Status

```typescript
const user = await userRepository.findOne({ where: { email } });

if (!user.canAttemptLogin) {
  if (user.isAccountLocked) {
    throw new Error('Account is temporarily locked');
  }
  if (!user.isActive) {
    throw new Error('Account is deactivated');
  }
}
```

### Handle Failed Login

```typescript
// Increment login attempts
user.loginAttempts += 1;

// Lock account after 5 failed attempts
if (user.loginAttempts >= 5) {
  user.lockedUntil = new Date(Date.now() + 2 * 60 * 60 * 1000); // 2 hours
}

await userRepository.save(user);
```

### Reset Login Attempts

```typescript
// On successful login
if (user.loginAttempts > 0 || user.lockedUntil) {
  user.loginAttempts = 0;
  user.lockedUntil = null;
  await userRepository.save(user);
}
```

### Password Reset Flow

```typescript
// Generate reset token
const resetToken = crypto.randomBytes(32).toString('hex');
user.passwordResetToken = resetToken;
user.passwordResetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

await userRepository.save(user);

// Validate reset token
if (!user.hasPasswordResetToken) {
  throw new Error('Invalid or expired reset token');
}
```

## 🛠️ Next Steps

1. **Implement Authentication Service**: Use these fields in your AuthService
2. **Add Rate Limiting**: Implement request throttling on auth endpoints
3. **Email Verification**: Extend for email verification workflows
4. **2FA Implementation**: Use the 2FA fields for enhanced security
5. **Audit Logging**: Track authentication events for security monitoring

## 📝 Migration Rollback

If you need to rollback the migration:

```bash
npm run migration:revert
```

This will remove all the authentication fields and indexes.

## 🔧 Troubleshooting

### Migration Fails

1. Check database connection:
   ```bash
   npm run test:db:connection
   ```

2. Ensure no conflicting migrations:
   ```bash
   npm run migration:show
   ```

3. Check for existing columns:
   ```sql
   \d users -- In PostgreSQL
   ```

### Performance Issues

The new indexes should improve query performance. Monitor these queries:

- Login attempts by email
- Locked account checks
- Password reset token validation
- Active user lookups

## 📚 Related Documentation

- [User Entity Documentation](../src/database/entities/user.entity.ts)
- [Migration Files](../src/database/migrations/)
- [Authentication Implementation Guide](../../docs/AUTHENTICATION_IMPLEMENTATION.md)
