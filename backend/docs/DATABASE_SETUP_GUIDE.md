# Complete Database Setup Guide

This guide documents the complete process for setting up a PostgreSQL database with TypeORM in a NestJS application.

## Overview

This setup process configures:
- PostgreSQL database connection
- TypeORM with entity synchronization
- Database migrations and seeding
- Environment configuration
- Docker support (optional)

## Prerequisites

- Node.js and npm installed
- PostgreSQL installed locally OR Docker for containerized setup
- Basic understanding of TypeORM and NestJS

## Quick Setup (Automated)

Run the automated setup script:

```bash
# Make the script executable
chmod +x scripts/setup-database.sh

# Run the setup
./scripts/setup-database.sh
```

This script will:
1. Check prerequisites
2. Set up database configuration
3. Create database and user
4. Run migrations and seeds
5. Test the connection

## Manual Setup Process

### Step 1: Install Dependencies

```bash
npm install @nestjs/typeorm typeorm pg
npm install -D @types/pg
```

### Step 2: Environment Configuration

Create environment files:

**`.env` (Production/Default)**
```env
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=your_username
DATABASE_PASSWORD=your_password
DATABASE_NAME=your_database
DATABASE_SYNCHRONIZE=false
DATABASE_LOGGING=false
SKIP_DATABASE=false

# Application
NODE_ENV=production
PORT=4000

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
```

**`.env.development` (Development)**
```env
# Development Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=your_username
DATABASE_PASSWORD=your_password
DATABASE_NAME=your_database
DATABASE_SYNCHRONIZE=true
DATABASE_LOGGING=true
SKIP_DATABASE=false

# Development Settings
NODE_ENV=development
PORT=4000

# Security (Development only)
JWT_SECRET=dev-jwt-secret-key
```

### Step 3: TypeORM Configuration

Create `src/database/data-source.ts`:

```typescript
import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { config } from 'dotenv';

// Load environment variables
config();

const configService = new ConfigService();

const AppDataSource = new DataSource({
  type: 'postgres',
  url: configService.get('DATABASE_URL'),
  host: configService.get('DATABASE_HOST', 'localhost'),
  port: configService.get('DATABASE_PORT', 5432),
  username: configService.get('DATABASE_USERNAME'),
  password: configService.get('DATABASE_PASSWORD'),
  database: configService.get('DATABASE_NAME'),
  synchronize: configService.get('DATABASE_SYNCHRONIZE', 'false') === 'true',
  logging: configService.get('DATABASE_LOGGING', 'false') === 'true',
  entities: [__dirname + '/entities/*.entity{.ts,.js}'],
  migrations: [__dirname + '/migrations/*{.ts,.js}'],
  subscribers: [__dirname + '/subscribers/*{.ts,.js}'],
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

export default AppDataSource;
```

### Step 4: Database Module Setup

Update `src/app.module.ts`:

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.development', '.env'],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        url: configService.get('DATABASE_URL'),
        host: configService.get('DATABASE_HOST', 'localhost'),
        port: configService.get('DATABASE_PORT', 5432),
        username: configService.get('DATABASE_USERNAME'),
        password: configService.get('DATABASE_PASSWORD'),
        database: configService.get('DATABASE_NAME'),
        synchronize: configService.get('DATABASE_SYNCHRONIZE', 'false') === 'true',
        logging: configService.get('DATABASE_LOGGING', 'false') === 'true',
        entities: [__dirname + '/database/entities/*.entity{.ts,.js}'],
        autoLoadEntities: true,
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

### Step 5: Create Database and User

**For existing PostgreSQL installation:**

```bash
# Connect as postgres user
sudo -u postgres psql

# Create database
CREATE DATABASE your_database_name;

# Create user
CREATE USER your_username WITH PASSWORD 'your_password';

# Grant permissions
GRANT ALL PRIVILEGES ON DATABASE your_database_name TO your_username;
ALTER USER your_username CREATEDB;

# Exit
\q
```

**For Docker setup:**

```bash
# Start PostgreSQL container
docker-compose up -d postgres

# Connect to container
docker exec -it postgres_container psql -U postgres

# Follow same SQL commands as above
```

### Step 6: Package.json Scripts

Add these scripts to `package.json`:

```json
{
  "scripts": {
    "migration:generate": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:generate -d src/database/data-source.ts",
    "migration:run": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:run -d src/database/data-source.ts",
    "migration:revert": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:revert -d src/database/data-source.ts",
    "migration:show": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:show -d src/database/data-source.ts",
    "seed": "ts-node src/database/seeds/run-seeds.ts",
    "db:setup": "./scripts/setup-database.sh",
    "db:reset": "./scripts/reset-database.sh",
    "db:test": "ts-node scripts/test-connection.ts"
  }
}
```

### Step 7: TypeScript Configuration

Update `tsconfig.json` to handle entity properties:

```json
{
  "compilerOptions": {
    "strictPropertyInitialization": false,
    // ... other options
  }
}
```

## Common Issues and Solutions

### Issue 1: Authentication Failed
**Problem**: `FATAL: password authentication failed`
**Solution**: 
- Check username/password in `.env`
- Verify user exists in PostgreSQL
- Check pg_hba.conf authentication method

### Issue 2: Database Does Not Exist
**Problem**: `database "dbname" does not exist`
**Solution**:
- Create database manually
- Run setup script
- Check database name in configuration

### Issue 3: TypeORM Synchronization Issues
**Problem**: Entity property initialization errors
**Solution**:
- Add `strictPropertyInitialization: false` to tsconfig.json
- Use definite assignment assertion (`!`) on entity properties
- Ensure proper entity decorators

### Issue 4: Migration CLI Not Found
**Problem**: `Cannot find module 'typeorm/cli'`
**Solution**:
- Check TypeORM installation
- Verify CLI path in package.json scripts
- Use correct relative path to node_modules

## Testing the Setup

### 1. Test Database Connection

```bash
npm run db:test
```

### 2. Check Tables Created

```bash
psql -h localhost -U your_username -d your_database -c "\dt"
```

### 3. Verify Seeded Data

```bash
psql -h localhost -U your_username -d your_database -c "SELECT * FROM users;"
```

### 4. Test API Endpoints

```bash
curl http://localhost:4000/api/v1/users
```

## Next Steps

1. **Connect Controllers**: Update controllers to use actual entities instead of mock data
2. **Add Authentication**: Implement JWT authentication and guards
3. **Add Validation**: Set up class-validator for request validation
4. **Error Handling**: Implement global exception filters
5. **Testing**: Set up unit and integration tests
6. **Documentation**: Generate API documentation with Swagger

## File Structure

```
backend/
├── src/
│   ├── database/
│   │   ├── entities/          # TypeORM entities
│   │   ├── migrations/        # Database migrations
│   │   ├── seeds/            # Database seeders
│   │   └── data-source.ts    # TypeORM configuration
│   └── app.module.ts         # Main application module
├── scripts/
│   ├── setup-database.sh     # Automated setup script
│   ├── reset-database.sh     # Database reset script
│   └── test-connection.ts    # Connection test script
├── docs/
│   └── DATABASE_SETUP_GUIDE.md
├── .env                      # Production environment
├── .env.development          # Development environment
├── docker-compose.yml        # Docker configuration
└── package.json             # NPM scripts and dependencies
```

This guide provides a complete, reproducible process for setting up PostgreSQL with TypeORM in any NestJS project.

## Automated Setup Scripts

### Quick Start for New Projects

Generate a complete project template:

```bash
# Download the template generator
curl -O https://raw.githubusercontent.com/your-repo/scripts/generate-project-template.sh
chmod +x generate-project-template.sh

# Create new project
./generate-project-template.sh --name my-awesome-app --docker

# Setup database
cd my-awesome-app
npm install
npm run db:setup -- --name my-awesome-app
npm run start:dev
```

### Quick Start for Existing Projects

Add database setup to existing NestJS project:

```bash
# Download setup script
curl -O https://raw.githubusercontent.com/your-repo/scripts/setup-database.sh
chmod +x setup-database.sh

# Run setup
./setup-database.sh --name my-existing-project
```

## Available Scripts

### 1. setup-database.sh
Automated database setup script with full configuration.

```bash
# Interactive setup
./setup-database.sh --name myproject

# With custom configuration
./setup-database.sh --name myproject --user myuser --password mypass --docker

# Skip dependency installation
./setup-database.sh --name myproject --skip-deps
```

**Features:**
- Validates prerequisites
- Installs dependencies
- Creates environment files
- Sets up PostgreSQL (local or Docker)
- Tests connection
- Updates package.json scripts

### 2. reset-database.sh
Completely resets the database and re-runs migrations/seeds.

```bash
# Reset database (will prompt for confirmation)
./reset-database.sh

# Show help
./reset-database.sh --help
```

**Features:**
- Drops and recreates database
- Runs migrations
- Executes seeds
- Works with both local and Docker PostgreSQL

### 3. test-connection.ts
Comprehensive database connection testing.

```bash
# Test database connection
npm run db:test
```

**Features:**
- Validates configuration
- Tests connection and queries
- Shows database information
- Provides troubleshooting guidance

### 4. generate-project-template.sh
Creates a complete NestJS project template.

```bash
# Generate new project
./generate-project-template.sh --name my-app --docker

# Custom directory
./generate-project-template.sh --name my-app --dir /path/to/project
```

**Features:**
- Complete NestJS project structure
- Pre-configured TypeORM setup
- Database scripts included
- Docker configuration (optional)
- Comprehensive README

## Script Integration

Add these scripts to any NestJS project by copying them to a `scripts/` directory and updating `package.json`:

```json
{
  "scripts": {
    "db:setup": "./scripts/setup-database.sh",
    "db:reset": "./scripts/reset-database.sh",
    "db:test": "ts-node scripts/test-connection.ts",
    "migration:generate": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:generate -d src/database/data-source.ts",
    "migration:run": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:run -d src/database/data-source.ts",
    "migration:revert": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:revert -d src/database/data-source.ts",
    "seed": "ts-node src/database/seeds/run-seeds.ts"
  }
}
```
