{"name": "@agoodmansview/backend", "version": "1.0.0", "description": "NestJS backend API with GraphQL and REST hybrid architecture", "author": "A Good Man's View Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:generate": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:generate -d src/database/data-source.ts", "migration:run": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:run -d src/database/data-source.ts", "migration:revert": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:revert -d src/database/data-source.ts", "migration:show": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:show -d src/database/data-source.ts", "seed": "ts-node src/database/seeds/run-seeds.ts", "db:setup": "bash scripts/setup-database.sh", "db:docker:up": "docker compose up -d postgres", "db:docker:down": "docker compose down", "db:docker:logs": "docker compose logs -f postgres", "test:db:connection": "ts-node scripts/test-db-connection.ts", "docs:generate": "npx @nestjs/swagger-cli plugin --config swagger-cli.json", "graphql:schema": "ts-node src/graphql/generate-schema.ts"}, "dependencies": {"@nestjs/apollo": "^13.1.0", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/graphql": "^13.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/cache-manager": "^3.0.1", "@apollo/server": "^4.11.2", "graphql": "^16.11.0", "graphql-tools": "^9.0.18", "class-validator": "^0.14.2", "class-transformer": "^0.5.1", "bcryptjs": "^3.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "typeorm": "^0.3.25", "pg": "^8.16.2", "redis": "^5.5.6", "cache-manager": "^7.0.0", "@keyv/redis": "^4.4.1", "multer": "^2.0.1", "helmet": "^8.1.0", "compression": "^1.8.0", "express-rate-limit": "^7.5.1", "winston": "^3.17.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.0.3", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/multer": "^1.4.13", "@types/compression": "^1.8.1", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.0", "jest": "^30.0.2", "prettier": "^3.5.3", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/test/setup.ts"], "coveragePathIgnorePatterns": ["/node_modules/", "/test/", "main.ts"]}}