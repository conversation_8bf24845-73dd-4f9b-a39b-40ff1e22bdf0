#!/bin/bash

# Project Template Generator for NestJS + TypeORM + PostgreSQL
# This script creates a complete project template with database setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "\n${BLUE}🔧 $1${NC}"
}

# Configuration
PROJECT_NAME=""
PROJECT_DIR=""
USE_DOCKER=false

# Show usage
show_usage() {
    echo "Usage: $0 --name PROJECT_NAME [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -n, --name PROJECT_NAME    Project name (required)"
    echo "  -d, --dir PROJECT_DIR      Project directory (default: PROJECT_NAME)"
    echo "  --docker                   Include Docker configuration"
    echo "  --help                     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --name my-awesome-app"
    echo "  $0 --name my-app --dir /path/to/my-app --docker"
}

# Parse arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--name)
                PROJECT_NAME="$2"
                shift 2
                ;;
            -d|--dir)
                PROJECT_DIR="$2"
                shift 2
                ;;
            --docker)
                USE_DOCKER=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    if [[ -z "$PROJECT_NAME" ]]; then
        log_error "Project name is required"
        show_usage
        exit 1
    fi

    if [[ -z "$PROJECT_DIR" ]]; then
        PROJECT_DIR="$PROJECT_NAME"
    fi
}

# Create project structure
create_project_structure() {
    log_step "Creating project structure"

    # Create main directories
    mkdir -p "$PROJECT_DIR"/{src,scripts,docs}
    mkdir -p "$PROJECT_DIR"/src/{database/{entities,migrations,seeds},modules/{auth,users,common},config}

    log_success "Project directories created"
}

# Generate package.json
generate_package_json() {
    log_step "Generating package.json"

    cat > "$PROJECT_DIR/package.json" << EOF
{
  "name": "$PROJECT_NAME",
  "version": "1.0.0",
  "description": "NestJS application with TypeORM and PostgreSQL",
  "author": "",
  "private": true,
  "license": "MIT",
  "scripts": {
    "build": "nest build",
    "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"",
    "start": "nest start",
    "start:dev": "nest start --watch",
    "start:debug": "nest start --debug --watch",
    "start:prod": "node dist/main",
    "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:cov": "jest --coverage",
    "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand",
    "test:e2e": "jest --config ./test/jest-e2e.json",
    "migration:generate": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:generate -d src/database/data-source.ts",
    "migration:run": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:run -d src/database/data-source.ts",
    "migration:revert": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:revert -d src/database/data-source.ts",
    "migration:show": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:show -d src/database/data-source.ts",
    "seed": "ts-node src/database/seeds/run-seeds.ts",
    "db:setup": "./scripts/setup-database.sh",
    "db:reset": "./scripts/reset-database.sh",
    "db:test": "ts-node scripts/test-connection.ts"
  },
  "dependencies": {
    "@nestjs/common": "^10.0.0",
    "@nestjs/core": "^10.0.0",
    "@nestjs/config": "^3.0.0",
    "@nestjs/typeorm": "^10.0.0",
    "@nestjs/platform-express": "^10.0.0",
    "@nestjs/jwt": "^10.0.0",
    "@nestjs/passport": "^10.0.0",
    "typeorm": "^0.3.17",
    "pg": "^8.11.0",
    "passport": "^0.6.0",
    "passport-jwt": "^4.0.1",
    "passport-local": "^1.0.0",
    "bcrypt": "^5.1.0",
    "class-validator": "^0.14.0",
    "class-transformer": "^0.5.1",
    "reflect-metadata": "^0.1.13",
    "rxjs": "^7.8.1"
  },
  "devDependencies": {
    "@nestjs/cli": "^10.0.0",
    "@nestjs/schematics": "^10.0.0",
    "@nestjs/testing": "^10.0.0",
    "@types/express": "^4.17.17",
    "@types/jest": "^29.5.2",
    "@types/node": "^20.3.1",
    "@types/passport-jwt": "^3.0.9",
    "@types/passport-local": "^1.0.35",
    "@types/bcrypt": "^5.0.0",
    "@types/pg": "^8.10.2",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "eslint": "^8.42.0",
    "eslint-config-prettier": "^9.0.0",
    "eslint-plugin-prettier": "^5.0.0",
    "jest": "^29.5.0",
    "prettier": "^3.0.0",
    "source-map-support": "^0.5.21",
    "supertest": "^6.3.3",
    "ts-jest": "^29.1.0",
    "ts-loader": "^9.4.3",
    "ts-node": "^10.9.1",
    "tsconfig-paths": "^4.2.0",
    "typescript": "^5.1.3"
  },
  "jest": {
    "moduleFileExtensions": [
      "js",
      "json",
      "ts"
    ],
    "rootDir": "src",
    "testRegex": ".*\\\\.spec\\\\.ts$",
    "transform": {
      "^.+\\\\.(t|j)s$": "ts-jest"
    },
    "collectCoverageFrom": [
      "**/*.(t|j)s"
    ],
    "coverageDirectory": "../coverage",
    "testEnvironment": "node"
  }
}
EOF

    log_success "package.json generated"
}

# Generate TypeScript configuration
generate_typescript_config() {
    log_step "Generating TypeScript configuration"

    cat > "$PROJECT_DIR/tsconfig.json" << EOF
{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES2020",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "strictPropertyInitialization": false
  }
}
EOF

    cat > "$PROJECT_DIR/tsconfig.build.json" << EOF
{
  "extends": "./tsconfig.json",
  "exclude": ["node_modules", "test", "dist", "**/*spec.ts"]
}
EOF

    log_success "TypeScript configuration generated"
}

# Generate NestJS configuration files
generate_nestjs_config() {
    log_step "Generating NestJS configuration files"

    # nest-cli.json
    cat > "$PROJECT_DIR/nest-cli.json" << EOF
{
  "\$schema": "https://json.schemastore.org/nest-cli",
  "collection": "@nestjs/schematics",
  "sourceRoot": "src",
  "compilerOptions": {
    "deleteOutDir": true
  }
}
EOF

    # .eslintrc.js
    cat > "$PROJECT_DIR/.eslintrc.js" << EOF
module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: 'tsconfig.json',
    tsconfigRootDir: __dirname,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint/eslint-plugin'],
  extends: [
    '@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: ['.eslintrc.js'],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
  },
};
EOF

    # .prettierrc
    cat > "$PROJECT_DIR/.prettierrc" << EOF
{
  "singleQuote": true,
  "trailingComma": "all"
}
EOF

    log_success "NestJS configuration files generated"
}

# Generate database configuration
generate_database_config() {
    log_step "Generating database configuration"

    # TypeORM data source
    cat > "$PROJECT_DIR/src/database/data-source.ts" << EOF
import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { config } from 'dotenv';

// Load environment variables
config();

const configService = new ConfigService();

const AppDataSource = new DataSource({
  type: 'postgres',
  url: configService.get('DATABASE_URL'),
  host: configService.get('DATABASE_HOST', 'localhost'),
  port: configService.get('DATABASE_PORT', 5432),
  username: configService.get('DATABASE_USERNAME'),
  password: configService.get('DATABASE_PASSWORD'),
  database: configService.get('DATABASE_NAME'),
  synchronize: configService.get('DATABASE_SYNCHRONIZE', 'false') === 'true',
  logging: configService.get('DATABASE_LOGGING', 'false') === 'true',
  entities: [__dirname + '/entities/*.entity{.ts,.js}'],
  migrations: [__dirname + '/migrations/*{.ts,.js}'],
  subscribers: [__dirname + '/subscribers/*{.ts,.js}'],
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

export default AppDataSource;
EOF

    log_success "Database configuration generated"
}

# Copy scripts
copy_scripts() {
    log_step "Copying database scripts"

    # Get the directory where this script is located
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # Copy scripts if they exist
    if [[ -f "$SCRIPT_DIR/setup-database.sh" ]]; then
        cp "$SCRIPT_DIR/setup-database.sh" "$PROJECT_DIR/scripts/"
        chmod +x "$PROJECT_DIR/scripts/setup-database.sh"
    fi
    
    if [[ -f "$SCRIPT_DIR/reset-database.sh" ]]; then
        cp "$SCRIPT_DIR/reset-database.sh" "$PROJECT_DIR/scripts/"
        chmod +x "$PROJECT_DIR/scripts/reset-database.sh"
    fi
    
    if [[ -f "$SCRIPT_DIR/test-connection.ts" ]]; then
        cp "$SCRIPT_DIR/test-connection.ts" "$PROJECT_DIR/scripts/"
    fi

    log_success "Scripts copied"
}

# Generate Docker configuration
generate_docker_config() {
    if [[ "$USE_DOCKER" != true ]]; then
        return
    fi

    log_step "Generating Docker configuration"

    cat > "$PROJECT_DIR/docker-compose.yml" << EOF
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: ${PROJECT_NAME}_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${PROJECT_NAME}_db
      POSTGRES_USER: ${PROJECT_NAME}_user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${PROJECT_NAME}_user -d ${PROJECT_NAME}_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: ${PROJECT_NAME}_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ${PROJECT_NAME}_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: admin@${PROJECT_NAME}.com
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    depends_on:
      - postgres

volumes:
  postgres_data:
  redis_data:
EOF

    # Dockerfile
    cat > "$PROJECT_DIR/Dockerfile" << EOF
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "run", "start:prod"]
EOF

    # .dockerignore
    cat > "$PROJECT_DIR/.dockerignore" << EOF
node_modules
npm-debug.log
dist
.git
.gitignore
README.md
.env
.env.development
.nyc_output
coverage
.coverage
EOF

    log_success "Docker configuration generated"
}

# Generate README
generate_readme() {
    log_step "Generating README"

    cat > "$PROJECT_DIR/README.md" << EOF
# $PROJECT_NAME

A NestJS application with TypeORM and PostgreSQL.

## Features

- 🚀 NestJS framework
- 🗄️ PostgreSQL database with TypeORM
- 🔐 JWT authentication
- 📝 API documentation
- 🧪 Testing setup
- 🐳 Docker support$(if [[ "$USE_DOCKER" == true ]]; then echo " (included)"; fi)

## Quick Start

### Prerequisites

- Node.js (v18 or higher)
- PostgreSQL$(if [[ "$USE_DOCKER" == true ]]; then echo " OR Docker"; fi)

### Installation

1. Clone the repository
2. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

3. Set up the database:
   \`\`\`bash
   npm run db:setup -- --name $PROJECT_NAME
   \`\`\`

4. Start the application:
   \`\`\`bash
   npm run start:dev
   \`\`\`

## Database Scripts

- \`npm run db:setup\` - Set up database with interactive prompts
- \`npm run db:test\` - Test database connection
- \`npm run db:reset\` - Reset database (⚠️ destroys all data)
- \`npm run migration:generate -- MigrationName\` - Generate migration
- \`npm run migration:run\` - Run migrations
- \`npm run seed\` - Run database seeds

## Development

### Running the app

\`\`\`bash
# development
npm run start

# watch mode
npm run start:dev

# production mode
npm run start:prod
\`\`\`

### Testing

\`\`\`bash
# unit tests
npm run test

# e2e tests
npm run test:e2e

# test coverage
npm run test:cov
\`\`\`

$(if [[ "$USE_DOCKER" == true ]]; then cat << 'DOCKER_SECTION'
## Docker

### Start services

\`\`\`bash
docker-compose up -d
\`\`\`

### Access services

- PostgreSQL: localhost:5432
- pgAdmin: http://localhost:5050 (admin@$PROJECT_NAME.com / admin)
- Redis: localhost:6379

DOCKER_SECTION
fi)

## Project Structure

\`\`\`
src/
├── database/
│   ├── entities/          # TypeORM entities
│   ├── migrations/        # Database migrations
│   ├── seeds/            # Database seeders
│   └── data-source.ts    # TypeORM configuration
├── modules/
│   ├── auth/             # Authentication module
│   ├── users/            # Users module
│   └── common/           # Shared modules
├── config/               # Configuration files
└── main.ts              # Application entry point
\`\`\`

## Environment Variables

Copy \`.env.example\` to \`.env\` and configure:

\`\`\`env
DATABASE_URL=postgresql://username:password@localhost:5432/database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=your_username
DATABASE_PASSWORD=your_password
DATABASE_NAME=your_database
JWT_SECRET=your-secret-key
\`\`\`

## License

This project is licensed under the MIT License.
EOF

    log_success "README generated"
}

# Main execution
main() {
    echo -e "${GREEN}"
    echo "🚀 Project Template Generator for NestJS + TypeORM + PostgreSQL"
    echo "=============================================================="
    echo -e "${NC}"

    parse_args "$@"
    
    if [[ -d "$PROJECT_DIR" ]]; then
        log_error "Directory '$PROJECT_DIR' already exists"
        exit 1
    fi

    create_project_structure
    generate_package_json
    generate_typescript_config
    generate_nestjs_config
    generate_database_config
    copy_scripts
    generate_docker_config
    generate_readme

    echo -e "\n${GREEN}🎉 Project template generated successfully!${NC}"
    echo -e "\n${BLUE}📋 Project Details:${NC}"
    echo -e "  Name: $PROJECT_NAME"
    echo -e "  Directory: $PROJECT_DIR"
    echo -e "  Docker: $USE_DOCKER"
    
    echo -e "\n${BLUE}🎯 Next Steps:${NC}"
    echo -e "  1. cd $PROJECT_DIR"
    echo -e "  2. npm install"
    echo -e "  3. npm run db:setup -- --name $PROJECT_NAME"
    echo -e "  4. npm run start:dev"
}

# Run main function
main "$@"
