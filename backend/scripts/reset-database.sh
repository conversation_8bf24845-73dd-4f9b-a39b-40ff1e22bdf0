#!/bin/bash

# Database Reset Script for NestJS + TypeORM + PostgreSQL
# This script resets the database by dropping and recreating it

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "\n${BLUE}🔧 $1${NC}"
}

# Load environment variables
load_env() {
    if [[ -f ".env.development" ]]; then
        source .env.development
        log_info "Loaded .env.development"
    elif [[ -f ".env" ]]; then
        source .env
        log_info "Loaded .env"
    else
        log_error "No environment file found (.env or .env.development)"
        exit 1
    fi
}

# Confirm reset action
confirm_reset() {
    echo -e "${RED}⚠️  WARNING: This will completely reset your database!${NC}"
    echo -e "${RED}   All data will be lost permanently.${NC}"
    echo ""
    echo "Database to reset: ${DATABASE_NAME}"
    echo "Host: ${DATABASE_HOST}:${DATABASE_PORT}"
    echo "User: ${DATABASE_USERNAME}"
    echo ""
    
    read -p "Are you sure you want to continue? (type 'yes' to confirm): " confirmation
    
    if [[ "$confirmation" != "yes" ]]; then
        log_info "Database reset cancelled"
        exit 0
    fi
}

# Check if using Docker
check_docker_setup() {
    if [[ -f "docker-compose.yml" ]] && docker-compose ps | grep -q postgres; then
        USE_DOCKER=true
        log_info "Docker setup detected"
    else
        USE_DOCKER=false
        log_info "Local PostgreSQL setup detected"
    fi
}

# Reset Docker database
reset_docker_database() {
    log_step "Resetting Docker PostgreSQL database"
    
    # Stop and remove containers
    log_info "Stopping PostgreSQL container..."
    docker-compose down postgres
    
    # Remove volumes to completely reset data
    log_info "Removing database volumes..."
    docker volume rm $(docker-compose config --volumes | grep postgres) 2>/dev/null || true
    
    # Start PostgreSQL again
    log_info "Starting fresh PostgreSQL container..."
    docker-compose up -d postgres
    
    # Wait for PostgreSQL to be ready
    log_info "Waiting for PostgreSQL to be ready..."
    sleep 15
    
    # Test connection
    timeout 60 bash -c "until docker-compose exec postgres pg_isready -U ${DATABASE_USERNAME} -d ${DATABASE_NAME}; do sleep 2; done"
    
    log_success "Docker PostgreSQL database reset completed"
}

# Reset local database
reset_local_database() {
    log_step "Resetting local PostgreSQL database"
    
    # Check if PostgreSQL is running
    if ! pg_isready -h ${DATABASE_HOST} -p ${DATABASE_PORT} &> /dev/null; then
        log_error "PostgreSQL is not running on ${DATABASE_HOST}:${DATABASE_PORT}"
        exit 1
    fi
    
    # Terminate active connections to the database
    log_info "Terminating active connections..."
    sudo -u postgres psql -c "
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = '${DATABASE_NAME}' AND pid <> pg_backend_pid();
    " 2>/dev/null || true
    
    # Drop database
    log_info "Dropping database ${DATABASE_NAME}..."
    sudo -u postgres psql -c "DROP DATABASE IF EXISTS ${DATABASE_NAME};" 2>/dev/null || true
    
    # Recreate database
    log_info "Creating database ${DATABASE_NAME}..."
    sudo -u postgres psql -c "CREATE DATABASE ${DATABASE_NAME};"
    
    # Grant permissions
    log_info "Granting permissions to ${DATABASE_USERNAME}..."
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE ${DATABASE_NAME} TO ${DATABASE_USERNAME};"
    
    log_success "Local PostgreSQL database reset completed"
}

# Run migrations
run_migrations() {
    log_step "Running database migrations"
    
    # Check if migration scripts exist
    if ! npm run migration:show &> /dev/null; then
        log_warning "No migration scripts found. Skipping migrations."
        return
    fi
    
    # Run migrations
    log_info "Executing migrations..."
    npm run migration:run
    
    log_success "Migrations completed"
}

# Run seeds
run_seeds() {
    log_step "Running database seeds"
    
    # Check if seed script exists
    if [[ ! -f "src/database/seeds/run-seeds.ts" ]]; then
        log_warning "No seed script found. Skipping seeds."
        return
    fi
    
    # Run seeds
    log_info "Executing seeds..."
    npm run seed
    
    log_success "Seeds completed"
}

# Test database connection
test_connection() {
    log_step "Testing database connection"
    
    # Test connection
    if PGPASSWORD=${DATABASE_PASSWORD} psql -h ${DATABASE_HOST} -U ${DATABASE_USERNAME} -d ${DATABASE_NAME} -c "SELECT version();" &> /dev/null; then
        log_success "Database connection successful"
    else
        log_error "Database connection failed"
        exit 1
    fi
}

# Show database info
show_database_info() {
    log_step "Database Information"
    
    # Get table count
    TABLE_COUNT=$(PGPASSWORD=${DATABASE_PASSWORD} psql -h ${DATABASE_HOST} -U ${DATABASE_USERNAME} -d ${DATABASE_NAME} -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | xargs)
    
    # Get user count (if users table exists)
    USER_COUNT=$(PGPASSWORD=${DATABASE_PASSWORD} psql -h ${DATABASE_HOST} -U ${DATABASE_USERNAME} -d ${DATABASE_NAME} -t -c "SELECT count(*) FROM users;" 2>/dev/null | xargs || echo "N/A")
    
    echo -e "${BLUE}📊 Database Status:${NC}"
    echo -e "  Database: ${DATABASE_NAME}"
    echo -e "  Host: ${DATABASE_HOST}:${DATABASE_PORT}"
    echo -e "  User: ${DATABASE_USERNAME}"
    echo -e "  Tables: ${TABLE_COUNT}"
    echo -e "  Users: ${USER_COUNT}"
}

# Main execution
main() {
    echo -e "${GREEN}"
    echo "🔄 Database Reset Script for NestJS + TypeORM + PostgreSQL"
    echo "=========================================================="
    echo -e "${NC}"
    
    load_env
    confirm_reset
    check_docker_setup
    
    if [[ "$USE_DOCKER" == true ]]; then
        reset_docker_database
    else
        reset_local_database
    fi
    
    test_connection
    run_migrations
    run_seeds
    show_database_info
    
    echo -e "\n${GREEN}🎉 Database reset completed successfully!${NC}"
    echo -e "\n${BLUE}🎯 Next Steps:${NC}"
    echo -e "  1. Start your application: npm run start:dev"
    echo -e "  2. Test your API endpoints"
    echo -e "  3. Verify data in database"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Database Reset Script"
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "This script will:"
        echo "  1. Drop the existing database"
        echo "  2. Create a fresh database"
        echo "  3. Run migrations"
        echo "  4. Run seeds"
        echo ""
        echo "Options:"
        echo "  --help, -h    Show this help message"
        echo ""
        echo "Environment:"
        echo "  The script reads database configuration from .env.development or .env"
        echo ""
        echo "⚠️  WARNING: This will permanently delete all data in your database!"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
