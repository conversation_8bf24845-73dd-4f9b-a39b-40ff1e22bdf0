#!/bin/bash

# <PERSON>ript to run the authentication fields migration
# This script adds the required authentication fields and indexes to the users table

set -e

echo "🔄 Running authentication fields migration..."

# Change to backend directory
cd "$(dirname "$0")/.."

# Check if TypeORM CLI is available
if ! command -v ts-node &> /dev/null; then
    echo "❌ ts-node is required but not installed. Please install it first:"
    echo "   npm install -g ts-node"
    exit 1
fi

# Check if database is running
echo "🔍 Checking database connection..."
npm run test:db:connection 2>/dev/null || {
    echo "❌ Database connection failed. Please ensure PostgreSQL is running."
    echo "   You can start it with: npm run db:docker:up"
    exit 1
}

# Generate migration if it doesn't exist
MIGRATION_FILE="src/database/migrations/AddAuthenticationFieldsToUser.ts"
if [ ! -f "$MIGRATION_FILE" ]; then
    echo "📝 Generating authentication fields migration..."
    npm run migration:generate -- AddAuthenticationFieldsToUser
else
    echo "✅ Migration file already exists: $MIGRATION_FILE"
fi

# Run the migration
echo "🚀 Running migration..."
npm run migration:run

# Verify migration was successful
echo "🔍 Verifying migration..."
npm run migration:show

echo "✅ Authentication fields migration completed successfully!"
echo ""
echo "📋 Added fields:"
echo "   - password_reset_token (varchar, nullable)"
echo "   - password_reset_expires (timestamptz, nullable)"
echo "   - login_attempts (integer, default: 0)"
echo "   - locked_until (timestamptz, nullable)"
echo "   - two_factor_secret (varchar, nullable)"
echo "   - two_factor_enabled (boolean, default: false)"
echo ""
echo "📋 Added indexes:"
echo "   - idx_users_login_attempts"
echo "   - idx_users_locked_until"
echo "   - idx_users_password_reset_token"
echo "   - idx_users_active_email (composite)"
echo ""
echo "🎉 Your user entity is now ready for authentication implementation!"
