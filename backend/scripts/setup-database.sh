#!/bin/bash

# Database Setup Script for NestJS + TypeORM + PostgreSQL
# This script automates the complete database setup process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "\n${BLUE}🔧 $1${NC}"
}

# Configuration variables
PROJECT_NAME=""
DB_NAME=""
DB_USER=""
DB_PASSWORD=""
DB_HOST="localhost"
DB_PORT="5432"
USE_DOCKER=false
SKIP_DEPS=false

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -n, --name PROJECT_NAME     Project name (required)"
    echo "  -d, --database DB_NAME      Database name (default: PROJECT_NAME)"
    echo "  -u, --user DB_USER          Database user (default: PROJECT_NAME_user)"
    echo "  -p, --password DB_PASSWORD  Database password (default: generated)"
    echo "  -h, --host DB_HOST          Database host (default: localhost)"
    echo "  --port DB_PORT              Database port (default: 5432)"
    echo "  --docker                    Use Docker for PostgreSQL"
    echo "  --skip-deps                 Skip dependency installation"
    echo "  --help                      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --name myproject"
    echo "  $0 --name myproject --docker"
    echo "  $0 --name myproject --user myuser --password mypass"
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--name)
                PROJECT_NAME="$2"
                shift 2
                ;;
            -d|--database)
                DB_NAME="$2"
                shift 2
                ;;
            -u|--user)
                DB_USER="$2"
                shift 2
                ;;
            -p|--password)
                DB_PASSWORD="$2"
                shift 2
                ;;
            -h|--host)
                DB_HOST="$2"
                shift 2
                ;;
            --port)
                DB_PORT="$2"
                shift 2
                ;;
            --docker)
                USE_DOCKER=true
                shift
                ;;
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    # Validate required arguments
    if [[ -z "$PROJECT_NAME" ]]; then
        log_error "Project name is required. Use --name PROJECT_NAME"
        show_usage
        exit 1
    fi

    # Set defaults
    if [[ -z "$DB_NAME" ]]; then
        DB_NAME="${PROJECT_NAME}_db"
    fi

    if [[ -z "$DB_USER" ]]; then
        DB_USER="${PROJECT_NAME}_user"
    fi

    if [[ -z "$DB_PASSWORD" ]]; then
        DB_PASSWORD=$(openssl rand -base64 12 | tr -d "=+/" | cut -c1-12)
    fi
}

# Check prerequisites
check_prerequisites() {
    log_step "Checking prerequisites"

    # Check if we're in a Node.js project
    if [[ ! -f "package.json" ]]; then
        log_error "package.json not found. Please run this script from your project root."
        exit 1
    fi

    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    log_success "Node.js found: $(node --version)"

    # Check npm
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    log_success "npm found: $(npm --version)"

    if [[ "$USE_DOCKER" == true ]]; then
        # Check Docker
        if ! command -v docker &> /dev/null; then
            log_error "Docker is not installed"
            exit 1
        fi
        log_success "Docker found: $(docker --version)"

        # Check Docker Compose
        if ! command -v docker-compose &> /dev/null; then
            log_error "Docker Compose is not installed"
            exit 1
        fi
        log_success "Docker Compose found: $(docker-compose --version)"
    else
        # Check PostgreSQL
        if ! command -v psql &> /dev/null; then
            log_error "PostgreSQL client (psql) is not installed"
            exit 1
        fi
        log_success "PostgreSQL client found: $(psql --version)"
    fi
}

# Install dependencies
install_dependencies() {
    if [[ "$SKIP_DEPS" == true ]]; then
        log_info "Skipping dependency installation"
        return
    fi

    log_step "Installing dependencies"

    # Check if dependencies are already installed
    if npm list @nestjs/typeorm typeorm pg &> /dev/null; then
        log_info "Dependencies already installed"
        return
    fi

    log_info "Installing TypeORM and PostgreSQL dependencies..."
    npm install @nestjs/typeorm typeorm pg
    npm install -D @types/pg

    log_success "Dependencies installed"
}

# Create environment files
create_env_files() {
    log_step "Creating environment configuration files"

    # Create .env file
    cat > .env << EOF
# Database Configuration
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}
DATABASE_HOST=${DB_HOST}
DATABASE_PORT=${DB_PORT}
DATABASE_USERNAME=${DB_USER}
DATABASE_PASSWORD=${DB_PASSWORD}
DATABASE_NAME=${DB_NAME}
DATABASE_SYNCHRONIZE=false
DATABASE_LOGGING=false
SKIP_DATABASE=false

# Application
NODE_ENV=production
PORT=4000

# Security
JWT_SECRET=$(openssl rand -base64 32)
EOF

    # Create .env.development file
    cat > .env.development << EOF
# Development Database Configuration
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}
DATABASE_HOST=${DB_HOST}
DATABASE_PORT=${DB_PORT}
DATABASE_USERNAME=${DB_USER}
DATABASE_PASSWORD=${DB_PASSWORD}
DATABASE_NAME=${DB_NAME}
DATABASE_SYNCHRONIZE=true
DATABASE_LOGGING=true
SKIP_DATABASE=false

# Development Settings
NODE_ENV=development
PORT=4000

# Security (Development only)
JWT_SECRET=dev-jwt-secret-key
EOF

    log_success "Environment files created"
    log_info "Database URL: postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"
}

# Setup database structure
setup_database_structure() {
    log_step "Setting up database directory structure"

    # Create directories
    mkdir -p src/database/{entities,migrations,seeds}

    # Create data-source.ts if it doesn't exist
    if [[ ! -f "src/database/data-source.ts" ]]; then
        log_info "Creating TypeORM data source configuration..."
        log_warning "data-source.ts template needs to be created manually"
    fi

    log_success "Database structure created"
}

# Setup PostgreSQL database
setup_postgresql() {
    log_step "Setting up PostgreSQL database"

    if [[ "$USE_DOCKER" == true ]]; then
        setup_docker_postgres
    else
        setup_local_postgres
    fi
}

# Setup Docker PostgreSQL
setup_docker_postgres() {
    log_info "Setting up PostgreSQL with Docker..."

    # Start PostgreSQL container
    log_info "Starting PostgreSQL container..."
    docker-compose up -d postgres

    # Wait for PostgreSQL to be ready
    log_info "Waiting for PostgreSQL to be ready..."
    sleep 10

    # Test connection
    if docker-compose exec postgres pg_isready -U ${DB_USER} -d ${DB_NAME} &> /dev/null; then
        log_success "PostgreSQL container is ready"
    else
        log_error "PostgreSQL container failed to start"
        exit 1
    fi
}

# Setup local PostgreSQL
setup_local_postgres() {
    log_info "Setting up local PostgreSQL database..."

    # Check if PostgreSQL is running
    if ! pg_isready &> /dev/null; then
        log_error "PostgreSQL is not running. Please start PostgreSQL service."
        exit 1
    fi

    # Create database and user
    log_info "Creating database and user..."

    # Create database
    sudo -u postgres psql -c "CREATE DATABASE ${DB_NAME};" 2>/dev/null || log_info "Database ${DB_NAME} already exists"

    # Create user
    sudo -u postgres psql -c "CREATE USER ${DB_USER} WITH PASSWORD '${DB_PASSWORD}';" 2>/dev/null || log_info "User ${DB_USER} already exists"

    # Grant permissions
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME} TO ${DB_USER};"
    sudo -u postgres psql -c "ALTER USER ${DB_USER} CREATEDB;"

    log_success "Database and user created successfully"
}

# Test database connection
test_connection() {
    log_step "Testing database connection"

    # Test with psql
    if PGPASSWORD=${DB_PASSWORD} psql -h ${DB_HOST} -U ${DB_USER} -d ${DB_NAME} -c "SELECT version();" &> /dev/null; then
        log_success "Database connection successful"
    else
        log_error "Database connection failed"
        exit 1
    fi
}

# Main execution
main() {
    echo -e "${GREEN}"
    echo "🚀 Database Setup Script for NestJS + TypeORM + PostgreSQL"
    echo "=========================================================="
    echo -e "${NC}"

    parse_args "$@"
    check_prerequisites
    install_dependencies
    create_env_files
    setup_database_structure
    setup_postgresql
    test_connection

    echo -e "\n${GREEN}🎉 Database setup completed successfully!${NC}"
    echo -e "\n${BLUE}📋 Setup Summary:${NC}"
    echo -e "  Project: ${PROJECT_NAME}"
    echo -e "  Database: ${DB_NAME}"
    echo -e "  User: ${DB_USER}"
    echo -e "  Password: ${DB_PASSWORD}"
    echo -e "  Host: ${DB_HOST}:${DB_PORT}"
    echo -e "  Docker: ${USE_DOCKER}"

    echo -e "\n${BLUE}🎯 Next Steps:${NC}"
    echo -e "  1. Create your TypeORM entities in src/database/entities/"
    echo -e "  2. Run: npm run migration:generate -- InitialMigration"
    echo -e "  3. Run: npm run migration:run"
    echo -e "  4. Create seeders in src/database/seeds/"
    echo -e "  5. Run: npm run seed"
    echo -e "  6. Start your application: npm run start:dev"

    if [[ "$USE_DOCKER" == true ]]; then
        echo -e "\n${BLUE}🐳 Docker Services:${NC}"
        echo -e "  PostgreSQL: localhost:${DB_PORT}"
        echo -e "  pgAdmin: http://localhost:5050 (admin@${PROJECT_NAME}.com / admin)"
        echo -e "  Redis: localhost:6379"
    fi
}

# Run main function with all arguments
main "$@"
