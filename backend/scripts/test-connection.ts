import { DataSource } from 'typeorm';
import { config } from 'dotenv';

// Load environment variables
config({ path: ['.env.development', '.env'] });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Logging functions
const log = {
  info: (msg: string) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg: string) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg: string) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg: string) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  step: (msg: string) => console.log(`\n${colors.cyan}🔧 ${msg}${colors.reset}`),
};

// Database configuration
const dbConfig = {
  type: 'postgres' as const,
  host: process.env.DATABASE_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT || '5432'),
  username: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  url: process.env.DATABASE_URL,
};

// Test database connection
async function testDatabaseConnection(): Promise<void> {
  console.log(`${colors.green}🚀 Database Connection Test${colors.reset}`);
  console.log('='.repeat(50));

  // Validate configuration
  log.step('Validating configuration');
  
  if (!dbConfig.username || !dbConfig.password || !dbConfig.database) {
    log.error('Missing required database configuration');
    console.log('Required environment variables:');
    console.log('  - DATABASE_USERNAME');
    console.log('  - DATABASE_PASSWORD');
    console.log('  - DATABASE_NAME');
    console.log('  - DATABASE_HOST (optional, defaults to localhost)');
    console.log('  - DATABASE_PORT (optional, defaults to 5432)');
    process.exit(1);
  }

  log.success('Configuration validated');
  console.log(`  Host: ${dbConfig.host}:${dbConfig.port}`);
  console.log(`  Database: ${dbConfig.database}`);
  console.log(`  Username: ${dbConfig.username}`);
  console.log(`  URL: ${dbConfig.url ? 'Provided' : 'Not provided'}`);

  // Create data source
  log.step('Creating database connection');
  
  const dataSource = new DataSource({
    type: dbConfig.type,
    host: dbConfig.host,
    port: dbConfig.port,
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    url: dbConfig.url,
    synchronize: false,
    logging: false,
    entities: [],
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {
    // Test connection
    log.info('Attempting to connect to database...');
    await dataSource.initialize();
    log.success('Database connection established');

    // Test basic query
    log.step('Testing database queries');
    
    const versionResult = await dataSource.query('SELECT version() as version');
    log.success('Version query successful');
    console.log(`  PostgreSQL Version: ${versionResult[0].version.split(' ')[1]}`);

    const timeResult = await dataSource.query('SELECT NOW() as current_time');
    log.success('Time query successful');
    console.log(`  Current Time: ${timeResult[0].current_time}`);

    // Test database info
    log.step('Gathering database information');
    
    const dbSizeResult = await dataSource.query(`
      SELECT pg_size_pretty(pg_database_size('${dbConfig.database}')) as size
    `);
    console.log(`  Database Size: ${dbSizeResult[0].size}`);

    const tableCountResult = await dataSource.query(`
      SELECT count(*) as table_count 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    console.log(`  Public Tables: ${tableCountResult[0].table_count}`);

    // List tables if any exist
    if (parseInt(tableCountResult[0].table_count) > 0) {
      const tablesResult = await dataSource.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        ORDER BY table_name
      `);
      
      console.log('  Tables:');
      tablesResult.forEach((table: any) => {
        console.log(`    - ${table.table_name}`);
      });
    }

    // Test extensions
    log.step('Checking PostgreSQL extensions');
    
    const extensionsResult = await dataSource.query(`
      SELECT extname 
      FROM pg_extension 
      WHERE extname IN ('uuid-ossp', 'pgcrypto', 'citext')
      ORDER BY extname
    `);
    
    if (extensionsResult.length > 0) {
      console.log('  Installed Extensions:');
      extensionsResult.forEach((ext: any) => {
        console.log(`    - ${ext.extname}`);
      });
    } else {
      log.warning('No common extensions found (uuid-ossp, pgcrypto, citext)');
    }

    // Test permissions
    log.step('Testing database permissions');
    
    try {
      await dataSource.query('CREATE TEMP TABLE test_permissions (id SERIAL)');
      await dataSource.query('DROP TABLE test_permissions');
      log.success('Create/Drop table permissions verified');
    } catch (error) {
      log.warning('Limited table creation permissions');
    }

    // Close connection
    await dataSource.destroy();
    log.success('Database connection closed');

    // Summary
    console.log(`\n${colors.green}🎉 Database connection test completed successfully!${colors.reset}`);
    console.log(`\n${colors.blue}📋 Connection Summary:${colors.reset}`);
    console.log(`  ✅ Connection: Successful`);
    console.log(`  ✅ Queries: Working`);
    console.log(`  ✅ Permissions: Verified`);
    console.log(`  ✅ Database: ${dbConfig.database} on ${dbConfig.host}:${dbConfig.port}`);

  } catch (error) {
    log.error('Database connection failed');
    console.error(`\n${colors.red}Error Details:${colors.reset}`);
    
    if (error instanceof Error) {
      console.error(`  Message: ${error.message}`);
      
      // Provide specific error guidance
      if (error.message.includes('authentication failed')) {
        console.log(`\n${colors.yellow}💡 Authentication Error Solutions:${colors.reset}`);
        console.log('  1. Check username and password in environment variables');
        console.log('  2. Verify user exists in PostgreSQL');
        console.log('  3. Check pg_hba.conf authentication method');
        console.log('  4. Try connecting manually: psql -h localhost -U username -d database');
      } else if (error.message.includes('does not exist')) {
        console.log(`\n${colors.yellow}💡 Database Not Found Solutions:${colors.reset}`);
        console.log('  1. Create the database manually');
        console.log('  2. Run the setup script: npm run db:setup');
        console.log('  3. Check database name in environment variables');
      } else if (error.message.includes('connection refused')) {
        console.log(`\n${colors.yellow}💡 Connection Refused Solutions:${colors.reset}`);
        console.log('  1. Check if PostgreSQL is running');
        console.log('  2. Verify host and port configuration');
        console.log('  3. Check firewall settings');
        console.log('  4. For Docker: ensure containers are running');
      }
    }

    process.exit(1);
  }
}

// Handle script execution
if (require.main === module) {
  testDatabaseConnection().catch((error) => {
    log.error('Unexpected error occurred');
    console.error(error);
    process.exit(1);
  });
}

export { testDatabaseConnection };
