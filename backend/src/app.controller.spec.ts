import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from './app.controller';
import { AppService } from './app.service';

describe('AppController', () => {
  let appController: AppController;
  let appService: AppService;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [AppService],
    }).compile();

    appController = app.get<AppController>(AppController);
    appService = app.get<AppService>(AppService);
  });

  describe('getApiInfo', () => {
    it('should return API information', () => {
      const result = appController.getApiInfo();
      
      expect(result).toBeDefined();
      expect(result.name).toBe("A Good Man's View API");
      expect(result.version).toBe('1.0.0');
      expect(result.description).toBe('Multi-vendor e-commerce platform with blockchain integration');
      expect(result.environment).toBe('test');
      expect(result.features).toBeDefined();
      expect(result.endpoints).toBeDefined();
    });
  });

  describe('getInfo', () => {
    it('should return the same API information as getApiInfo', () => {
      const apiInfo = appController.getApiInfo();
      const info = appController.getInfo();
      
      expect(info).toEqual(apiInfo);
    });
  });

  describe('API features', () => {
    it('should include all expected features', () => {
      const result = appController.getApiInfo();
      
      expect(result.features.graphql).toBe(true);
      expect(result.features.rest).toBe(true);
      expect(result.features.blockchain).toBe(true);
      expect(result.features.payments).toBe(true);
      expect(result.features.subscriptions).toBe(true);
    });
  });

  describe('API endpoints', () => {
    it('should include all expected endpoints', () => {
      const result = appController.getApiInfo();
      
      expect(result.endpoints.graphql).toBe('/graphql');
      expect(result.endpoints.rest).toBe('/api/v1');
      expect(result.endpoints.docs).toBe('/api/docs');
    });
  });
});
