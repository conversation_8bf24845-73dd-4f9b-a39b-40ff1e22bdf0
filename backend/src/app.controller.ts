import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('app')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: 'Get API information' })
  @ApiResponse({
    status: 200,
    description: 'API information retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        version: { type: 'string' },
        description: { type: 'string' },
        environment: { type: 'string' },
        timestamp: { type: 'string' },
      },
    },
  })
  getApiInfo() {
    return this.appService.getApiInfo();
  }

  @Get('info')
  @ApiOperation({ summary: 'Get API information (alternative endpoint)' })
  @ApiResponse({
    status: 200,
    description: 'API information retrieved successfully',
  })
  getInfo() {
    return this.appService.getApiInfo();
  }


}
