import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { AppModule } from './app.module';
import request from 'supertest';

describe('AppController (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    // Set test environment variables
    process.env.SKIP_DATABASE = 'true';
    process.env.GRAPHQL_ENABLED = 'false';
    process.env.NODE_ENV = 'test';

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    
    // Apply the same configuration as in main.ts for consistency
    app.setGlobalPrefix('api/v1', {
      exclude: ['graphql', 'info', ''],
    });
    
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Root endpoints', () => {
    it('/ (GET) - should return welcome message', () => {
      return request(app.getHttpServer())
        .get('/')
        .expect(200)
        .expect((res) => {
          expect(res.body.message).toBe('Welcome to A Good Man\'s View API');
          expect(res.body.name).toBe('A Good Man\'s View');
          expect(res.body.version).toBe('1.0.0');
          expect(res.body.status).toBe('running');
          expect(res.body.endpoints).toBeDefined();
          expect(res.body.features).toBeDefined();
        });
    });

    it('/info (GET) - should return API information', () => {
      return request(app.getHttpServer())
        .get('/info')
        .expect(200)
        .expect((res) => {
          expect(res.body.name).toBe("A Good Man's View API");
          expect(res.body.version).toBe('1.0.0');
          expect(res.body.environment).toBe('test');
          expect(res.body.features).toBeDefined();
          expect(res.body.endpoints).toBeDefined();
        });
    });
  });

  describe('API endpoints', () => {
    it('/api/v1/users (GET) - should return users endpoint', () => {
      return request(app.getHttpServer())
        .get('/api/v1/users')
        .expect(200);
    });

    it('/api/v1/products (GET) - should return products endpoint', () => {
      return request(app.getHttpServer())
        .get('/api/v1/products')
        .expect(200);
    });

    it('/api/v1/wallet (GET) - should return wallet endpoint', () => {
      return request(app.getHttpServer())
        .get('/api/v1/wallet')
        .expect(200);
    });
  });

  describe('Error handling', () => {
    it('should return 404 for non-existent endpoints', () => {
      return request(app.getHttpServer())
        .get('/non-existent-endpoint')
        .expect(404);
    });

    it('should return 404 for non-existent API endpoints', () => {
      return request(app.getHttpServer())
        .get('/api/v1/non-existent')
        .expect(404);
    });
  });

  describe('Application Health', () => {
    it('should respond to health check requests', () => {
      return request(app.getHttpServer())
        .get('/')
        .expect(200)
        .expect((res) => {
          expect(res.body.status).toBe('running');
          expect(res.body.name).toBe('A Good Man\'s View');
        });
    });
  });
});
