import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { GraphQLModule } from "@nestjs/graphql";
import { ApolloDriver, ApolloDriverConfig } from "@nestjs/apollo";
import { ThrottlerModule } from "@nestjs/throttler";
import { CacheModule } from "@nestjs/cache-manager";
import { join } from "path";

// Import modules
import { AuthModule } from "./modules/auth/auth.module";
import { UsersModule } from "./modules/users/users.module";
import { ProductsModule } from "./modules/products/products.module";
import { OrdersModule } from "./modules/orders/orders.module";
import { WalletModule } from "./modules/wallet/wallet.module";

// Import configuration
import { DatabaseModule } from "./database/database.module";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { AppResolver } from "./app.resolver";
import { RootController } from "./root.controller";

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [".env", ".env.development", ".env.local"],
    }),

    // Database (conditional)
    ...(process.env.SKIP_DATABASE !== "true" ? [DatabaseModule] : []),

    // GraphQL (conditional)
    ...(process.env.GRAPHQL_ENABLED !== "false"
      ? [
          GraphQLModule.forRoot<ApolloDriverConfig>({
            driver: ApolloDriver,
            autoSchemaFile: join(process.cwd(), "src/graphql/schema.gql"),
            playground: process.env.GRAPHQL_PLAYGROUND !== "false",
            introspection: process.env.GRAPHQL_INTROSPECTION !== "false",
            context: ({ req, res }) => ({ req, res }),
            subscriptions: {
              "graphql-ws": true,
              // 'subscriptions-transport-ws' removed - deprecated in favor of graphql-ws
            },
            // Add error handling for schema generation
            buildSchemaOptions: {
              numberScalarMode: "integer",
            },
          }),
        ]
      : []),

    // Rate limiting
    ThrottlerModule.forRoot([
      {
        ttl: parseInt(process.env.RATE_LIMIT_TTL) || 60000,
        limit: parseInt(process.env.RATE_LIMIT_MAX) || 100,
      },
    ]),

    // Caching
    CacheModule.register({
      isGlobal: true,
      ttl: 300, // 5 minutes
    }),

    // Feature modules
    AuthModule,
    UsersModule,
    ProductsModule,
    OrdersModule,
    WalletModule,
  ],
  controllers: [RootController, AppController],
  providers: [AppService, AppResolver],
})
export class AppModule {}
