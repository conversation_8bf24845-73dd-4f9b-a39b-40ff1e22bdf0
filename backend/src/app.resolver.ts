import { Resolver, Query } from '@nestjs/graphql';
import { AppService } from './app.service';
import { AppInfo } from './dto/app-info.dto';

@Resolver()
export class AppResolver {
  constructor(private readonly appService: AppService) {}

  @Query(() => AppInfo, { name: 'appInfo' })
  getAppInfo(): AppInfo {
    const info = this.appService.getApiInfo();
    return {
      name: info.name,
      version: info.version,
      description: info.description,
      environment: info.environment,
      timestamp: info.timestamp,
    };
  }

  @Query(() => String, { name: 'hello' })
  getHello(): string {
    return 'Hello from A Good Man\'s View GraphQL API!';
  }
}
