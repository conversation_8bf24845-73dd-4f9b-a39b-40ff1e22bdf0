import { Test, TestingModule } from '@nestjs/testing';
import { AppService } from './app.service';

describe('AppService', () => {
  let service: AppService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AppService],
    }).compile();

    service = module.get<AppService>(AppService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getApiInfo', () => {
    it('should return complete API information', () => {
      const result = service.getApiInfo();
      
      expect(result).toBeDefined();
      expect(typeof result).toBe('object');
    });

    it('should return correct basic information', () => {
      const result = service.getApiInfo();
      
      expect(result.name).toBe("A Good Man's View API");
      expect(result.version).toBe('1.0.0');
      expect(result.description).toBe('Multi-vendor e-commerce platform with blockchain integration');
      expect(result.environment).toBe('test'); // Should be 'test' in test environment
    });

    it('should include timestamp', () => {
      const result = service.getApiInfo();
      
      expect(result.timestamp).toBeDefined();
      expect(typeof result.timestamp).toBe('string');
      expect(new Date(result.timestamp)).toBeInstanceOf(Date);
    });

    it('should include features object with boolean values', () => {
      const result = service.getApiInfo();
      
      expect(result.features).toBeDefined();
      expect(typeof result.features).toBe('object');
      expect(result.features.graphql).toBe(true);
      expect(result.features.rest).toBe(true);
      expect(result.features.blockchain).toBe(true);
      expect(result.features.payments).toBe(true);
      expect(result.features.subscriptions).toBe(true);
    });

    it('should include endpoints object with correct paths', () => {
      const result = service.getApiInfo();
      
      expect(result.endpoints).toBeDefined();
      expect(typeof result.endpoints).toBe('object');
      expect(result.endpoints.graphql).toBe('/graphql');
      expect(result.endpoints.rest).toBe('/api/v1');
      expect(result.endpoints.docs).toBe('/api/docs');
    });

    it('should return consistent data structure on multiple calls', () => {
      const result1 = service.getApiInfo();
      const result2 = service.getApiInfo();
      
      // All properties should be the same except timestamp
      expect(result1.name).toBe(result2.name);
      expect(result1.version).toBe(result2.version);
      expect(result1.description).toBe(result2.description);
      expect(result1.environment).toBe(result2.environment);
      expect(result1.features).toEqual(result2.features);
      expect(result1.endpoints).toEqual(result2.endpoints);
    });
  });
});
