import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getApiInfo() {
    return {
      name: "A Good Man's View API",
      version: '1.0.0',
      description: 'Multi-vendor e-commerce platform with blockchain integration',
      environment: process.env.NODE_ENV || 'development',
      timestamp: new Date().toISOString(),
      features: {
        graphql: true,
        rest: true,
        blockchain: true,
        payments: true,
        subscriptions: true,
      },
      endpoints: {
        graphql: '/graphql',
        rest: '/api/v1',
        docs: '/api/docs',
      },
    };
  }


}
