import {
  Injectable,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
import { Observable } from 'rxjs';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    // Check if route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    // For protected routes, use JWT authentication
    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    // Check if route is public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return user; // Allow access even without valid token
    }

    // For protected routes, user must be authenticated
    if (err || !user) {
      let message = 'Unauthorized access';

      if (info) {
        switch (info.name) {
          case 'TokenExpiredError':
            message = 'Token has expired';
            break;
          case 'JsonWebTokenError':
            message = 'Invalid token';
            break;
          case 'NotBeforeError':
            message = 'Token not active';
            break;
          default:
            message = info.message || 'Authentication failed';
        }
      }

      throw err || new UnauthorizedException(message);
    }

    return user;
  }
}
