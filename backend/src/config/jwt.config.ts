import { ConfigService } from '@nestjs/config';
import { JwtModuleOptions } from '@nestjs/jwt';

export const jwtConfig = (configService: ConfigService): JwtModuleOptions => ({
  secret: configService.get<string>('JWT_SECRET'),
  signOptions: {
    expiresIn: configService.get<string>('JWT_EXPIRES_IN', '7d'),
    issuer: configService.get<string>('APP_NAME', 'A Good Man\'s View API'),
    audience: configService.get<string>('JWT_AUDIENCE', 'agoodmansview-users'),
  },
});

export const jwtConstants = {
  secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
  expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
  issuer: process.env.APP_NAME || 'A Good Man\'s View API',
  audience: process.env.JWT_AUDIENCE || 'agoodmansview-users',
};

// JWT Strategy configuration
export const jwtStrategyConfig = {
  jwtFromRequest: (req: any) => {
    let token = null;

    // Check Authorization header first
    if (req && req.headers && req.headers.authorization) {
      const authHeader = req.headers.authorization;
      if (authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7);
      }
    }

    // Fallback to cookies if no Authorization header
    if (!token && req && req.cookies) {
      token = req.cookies['access_token'];
    }

    return token;
  },
  secretOrKey: jwtConstants.secret,
  issuer: jwtConstants.issuer,
  audience: jwtConstants.audience,
  ignoreExpiration: false,
  passReqToCallback: false,
};

// Refresh token configuration
export const refreshTokenConfig = {
  secret: process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET || 'your-super-secret-refresh-key',
  expiresIn: jwtConstants.refreshExpiresIn,
  issuer: jwtConstants.issuer,
  audience: jwtConstants.audience,
};

// Token expiration times in seconds
export const tokenExpirationTimes = {
  accessToken: 7 * 24 * 60 * 60, // 7 days in seconds
  refreshToken: 30 * 24 * 60 * 60, // 30 days in seconds
  rememberMeAccessToken: 30 * 24 * 60 * 60, // 30 days for remember me
  rememberMeRefreshToken: 90 * 24 * 60 * 60, // 90 days for remember me
};

// Account lockout configuration
export const accountLockoutConfig = {
  maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5'),
  lockoutDurationMinutes: parseInt(process.env.LOCKOUT_DURATION_MINUTES || '15'),
  resetAttemptsAfterMinutes: parseInt(process.env.RESET_ATTEMPTS_AFTER_MINUTES || '60'),
};
