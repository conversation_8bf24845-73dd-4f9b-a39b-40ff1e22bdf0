import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { config } from 'dotenv';
import { join } from 'path';

// Load environment variables
config({ path: ['.env', '.env.development', '.env.local'] });

const configService = new ConfigService();

// Helper function to parse DATABASE_URL if provided
function parseDatabaseUrl(url: string) {
  if (!url) return {};
  
  try {
    const parsed = new URL(url);
    return {
      host: parsed.hostname,
      port: parseInt(parsed.port) || 5432,
      username: parsed.username,
      password: parsed.password,
      database: parsed.pathname.slice(1), // Remove leading slash
    };
  } catch (error) {
    console.warn('Failed to parse DATABASE_URL, falling back to individual env vars');
    return {};
  }
}

// Parse DATABASE_URL if provided, otherwise use individual env vars
const databaseUrl = configService.get('DATABASE_URL');
const urlConfig = databaseUrl ? parseDatabaseUrl(databaseUrl) : {};

const AppDataSource = new DataSource({
  type: 'postgres',
  host: urlConfig.host || configService.get('DATABASE_HOST', 'localhost'),
  port: urlConfig.port || configService.get('DATABASE_PORT', 5432),
  username: urlConfig.username || configService.get('DATABASE_USERNAME', 'postgres'),
  password: urlConfig.password || configService.get('DATABASE_PASSWORD', 'password'),
  database: urlConfig.database || configService.get('DATABASE_NAME', 'agoodmansview_db'),
  
  // Entity and migration paths
  entities: [join(__dirname, 'entities', '*.entity{.ts,.js}')],
  migrations: [join(__dirname, 'migrations', '*{.ts,.js}')],
  
  // Development settings
  synchronize: configService.get('DATABASE_SYNCHRONIZE', 'false') === 'true',
  logging: configService.get('DATABASE_LOGGING', 'false') === 'true',
  
  // SSL configuration for production
  ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
  
  // Connection settings
  connectTimeoutMS: 60000,
  extra: {
    max: parseInt(configService.get('DATABASE_MAX_CONNECTIONS', '10')),
    min: parseInt(configService.get('DATABASE_MIN_CONNECTIONS', '2')),
    acquire: parseInt(configService.get('DATABASE_ACQUIRE_TIMEOUT', '60000')),
    idle: parseInt(configService.get('DATABASE_IDLE_TIMEOUT', '10000')),
    evict: parseInt(configService.get('DATABASE_EVICT_TIMEOUT', '1000')),
  },
});

// Export as default for TypeORM CLI
export default AppDataSource;
