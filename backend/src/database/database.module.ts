import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    // Make database connection optional for development
    ...(process.env.SKIP_DATABASE !== 'true' ? [
      TypeOrmModule.forRootAsync({
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          type: 'postgres',
          host: configService.get('DATABASE_HOST', 'localhost'),
          port: configService.get('DATABASE_PORT', 5432),
          username: configService.get('DATABASE_USERNAME', 'postgres'),
          password: configService.get('DATABASE_PASSWORD', 'password'),
          database: configService.get('DATABASE_NAME', 'agoodmansview_db'),
          entities: [__dirname + '/entities/*.entity{.ts,.js}'],
          migrations: [__dirname + '/migrations/*{.ts,.js}'],
          synchronize: configService.get('DATABASE_SYNCHRONIZE', false),
          logging: configService.get('DATABASE_LOGGING', false),
          ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
          retryAttempts: 3,
          retryDelay: 3000,
          autoLoadEntities: true,
          // Connection pooling configuration
          extra: {
            max: configService.get('DATABASE_MAX_CONNECTIONS', 10),
            min: configService.get('DATABASE_MIN_CONNECTIONS', 2),
            acquire: configService.get('DATABASE_ACQUIRE_TIMEOUT', 60000),
            idle: configService.get('DATABASE_IDLE_TIMEOUT', 10000),
            evict: configService.get('DATABASE_EVICT_TIMEOUT', 1000),
          },
        }),
        inject: [ConfigService],
      })
    ] : []),
  ],
})
export class DatabaseModule {}
