# 🎨 A Good Man's View - Database Entities

This folder contains all TypeORM entities for the **A Good Man's View** art marketplace platform. Each entity represents a database table and defines the structure, relationships, and business logic for the platform.

## 📋 Table of Contents

- [🏗️ Architecture Overview](#️-architecture-overview)
- [👥 User Management](#-user-management)
- [🛍️ Product Catalog](#️-product-catalog)
- [🛒 Order Management](#-order-management)
- [💰 Financial System](#-financial-system)
- [⛓️ Blockchain Integration](#️-blockchain-integration)
- [🔗 Entity Relationships](#-entity-relationships)
- [🚀 Getting Started](#-getting-started)

## 🏗️ Architecture Overview

The platform uses a **wallet-based economy** with **subscription-based vendor model** and **ZAR-only payments** through South African payment gateways. All artwork images are automatically tokenized as NFTs (invisible to users) while maintaining a traditional e-commerce experience.

### Key Features:
- 💰 **Wallet-based payments** - Buyers use wallet money for purchases
- 🇿🇦 **ZAR-focused** - South African Rand only
- 🎨 **Art marketplace** - Physical artworks (no digital art yet)
- 💳 **Payment gateway for deposits/withdrawals** - PayFast, Ozow, etc.
- 📦 **Subscription packages** - Standard, Premium, Platinum
- 🔄 **Automatic commission splitting** - Premium: 20%, Platinum: 15%
- ⛓️ **Invisible NFT tokenization** - Artwork authenticity
- 🚫 **No refunds policy** - Final sales only
- 📈 **Automatic discounts** - 10% after 3 months, 15% after 6 months
- 📧 **Subscription reminders** - 7-day email alerts before auto-deduction

## 👥 User Management

### [`user.entity.ts`](./user.entity.ts)
**Central user management for customers, vendors, and administrators**

```typescript
// User roles
enum UserRole {
  BUYER = 'buyer',      // Customers who buy art
  VENDOR = 'vendor',    // Artists who sell art  
  ADMIN = 'admin',      // Platform administrators
  SUPER_ADMIN = 'super_admin'
}
```

**Key Features:**
- Multi-role support (buyers, vendors, admins)
- South African address format
- Email verification and password security
- Profile management with avatar support

### [`user-address.entity.ts`](./user-address.entity.ts)
**User address management with South African format**

```typescript
// Address types for South African users
enum AddressType {
  HOME = 'home',
  WORK = 'work', 
  BOTH = 'both'
}
```

**Key Features:**
- Province and postal code validation
- Multiple addresses per user
- Default address selection
- Shipping and billing address support

### [`vendor.entity.ts`](./vendor.entity.ts)
**Artist/vendor profiles with subscription management**

```typescript
// Subscription packages
enum SubscriptionPackage {
  STANDARD = 'standard',   // R99/month - Portfolio only
  PREMIUM = 'premium',     // R250/month - Portfolio + Shop (20% commission)
  PLATINUM = 'platinum'    // R500/month - All features (15% commission)
}
```

**Key Features:**
- Three subscription tiers with different features
- Store customization (Premium/Platinum only)
- Commission rate management
- Verification status tracking
- Store branding and layout options

## 🛍️ Product Catalog

### [`product.entity.ts`](./product.entity.ts)
**Artwork catalog with automatic discount system**

```typescript
// Automatic discount system
get automaticDiscountPercentage(): number {
  const months = this.monthsInShop;
  if (!this.autoDiscountEnabled) return 0;
  if (months >= 6) return 15; // 15% after 6 months
  if (months >= 3) return 10; // 10% after 3 months
  return 0;
}
```

**Key Features:**
- Automatic time-based discounts
- Vendor can disable auto-discounts
- SEO-friendly slugs and metadata
- Image gallery support
- Category associations

### [`category.entity.ts`](./category.entity.ts)
**Hierarchical art categories**

**Key Features:**
- Nested category structure
- SEO optimization
- Active/inactive status
- Category-based filtering

### [`product-image.entity.ts`](./product-image.entity.ts)
**Artwork images with invisible NFT tokenization**

```typescript
// Hidden blockchain fields (invisible to users)
internal_token_id: string;        // NFT token ID
free_mint_platform: string;      // 'polygon', 'opensea'
blockchain_network: string;      // 'polygon', 'ethereum'
tokenization_status: string;     // 'queued', 'processing', 'completed'
```

**Key Features:**
- Multiple images per artwork
- Primary image selection
- Invisible NFT tokenization
- Free minting platform integration
- IPFS metadata storage

## 🛒 Order Management

### [`order.entity.ts`](./order.entity.ts)
**Customer orders with multi-vendor support**

```typescript
// Order lifecycle - No cancellation policy
enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered'
  // No CANCELLED - orders cannot be cancelled
}
```

**Key Features:**
- Multi-vendor order support
- South African VAT calculation (15%)
- ZAR currency with proper formatting
- Shipping address management
- No cancellation or refunds policy

### [`order-item.entity.ts`](./order-item.entity.ts)
**Individual items within orders**

```typescript
// Tracks if item was purchased with discount
get wasPurchasedWithDiscount(): boolean {
  return this.unitPrice < this.product.price;
}
```

**Key Features:**
- Commission calculation per item
- Discount tracking
- Vendor earnings calculation
- Product snapshot at purchase time

## 💰 Financial System

### [`wallet.entity.ts`](./wallet.entity.ts)
**ZAR wallets for platform's internal economy**

```typescript
// ZAR-focused wallet system - Platform's internal currency
static createZARWallet(userId: string): Partial<Wallet> {
  return {
    userId,
    currency: 'ZAR',
    balance: 0,
    isActive: true,
  };
}
```

**Key Features:**
- **Internal platform currency** - All artwork purchases use wallet money
- ZAR-only support (no crypto payments)
- **Payment gateway integration** - For deposits/withdrawals only
- Balance tracking and management
- **Automatic commission deduction** - Platform takes commission from sales
- **Subscription fee automation** - Monthly fees auto-deducted from vendor wallets

### [`payment.entity.ts`](./payment.entity.ts)
**Payment gateway processing for wallet deposits/withdrawals only**

```typescript
// Payment status flow - No refunds allowed
enum PaymentStatus {
  PENDING = 'pending',      // Payment initiated
  PROCESSING = 'processing', // Gateway processing
  COMPLETED = 'completed',   // Payment successful
  FAILED = 'failed'         // Payment failed
  // No REFUNDED - no refunds policy
}

// South African payment methods - Gateway only
enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  EFT = 'eft',              // Electronic Funds Transfer
  INSTANT_EFT = 'instant_eft', // Real-time EFT
  BANK_TRANSFER = 'bank_transfer'
  // No WALLET - artwork purchases use Transaction entity
}
```

**Key Features:**
- **Wallet deposits/withdrawals only** - Payment gateway to/from wallets
- **No artwork purchases** - Those use wallet money via Transaction entity
- South African payment methods (EFT, Instant EFT)
- Payment gateway integration (PayFast, Ozow)
- Unique payment references
- Gateway response tracking
- **No refunds policy** - Final transactions only

### [`transaction.entity.ts`](./transaction.entity.ts)
**Wallet-based financial transaction records**

```typescript
// Transaction types for wallet-based art platform
enum TransactionType {
  DEPOSIT = 'deposit',        // Payment gateway → Wallet
  WITHDRAWAL = 'withdrawal',  // Wallet → Payment gateway
  PAYMENT = 'payment',        // Buyer wallet → Vendor wallet (artwork purchase)
  COMMISSION = 'commission',  // Vendor earnings after platform commission
  SUBSCRIPTION = 'subscription' // Monthly subscription fees to company wallet
}

// Transaction status flow
enum TransactionStatus {
  PENDING = 'pending',      // Transaction initiated
  PROCESSING = 'processing', // Processing wallet transfer
  COMPLETED = 'completed',   // Transaction successful
  FAILED = 'failed'         // Transaction failed
  // No CANCELLED - transactions cannot be cancelled
}
```

**Key Features:**
- **Wallet-based economy** - All purchases use wallet money
- **Automatic commission splitting** - Based on subscription package (Premium: 20%, Platinum: 15%)
- **Subscription fee automation** - 7-day email reminders before auto-deduction
- Complete audit trail for SARS compliance
- No cancellation once processing starts

## ⛓️ Blockchain Integration

### [`blockchain-record.entity.ts`](./blockchain-record.entity.ts)
**Immutable records for payments and NFTs**

```typescript
// Two types of blockchain records
recordType: 'payment' | 'nft_tokenization'

// Payment verification - tamper-proof receipts
static createForPayment(transactionId, txHash): BlockchainRecord

// NFT tokenization - invisible to users  
static createForNFT(transactionId, txHash): BlockchainRecord
```

**Key Features:**
- Payment verification on blockchain
- NFT tokenization tracking
- Polygon network integration (free transactions)
- Immutable audit trail
- Explorer link generation

## 🔗 Entity Relationships

```
User (1) ←→ (1) Vendor
User (1) ←→ (N) UserAddress
User (1) ←→ (N) Order
User (1) ←→ (N) Wallet

Vendor (1) ←→ (N) Product
Vendor (1) ←→ (N) Subscription

Product (1) ←→ (N) ProductImage
Product (N) ←→ (N) Category
Product (1) ←→ (N) OrderItem

Order (1) ←→ (N) OrderItem
Order (1) ←→ (N) Payment
OrderItem (N) ←→ (1) Product
OrderItem (N) ←→ (1) Vendor

Payment (N) ←→ (1) Order

Wallet (1) ←→ (N) Transaction
Transaction (1) ←→ (1) BlockchainRecord
```

## 🚀 Getting Started

### 1. Import Entities
```typescript
import { User } from './user.entity';
import { Vendor } from './vendor.entity';
import { Product } from './product.entity';
// ... other entities
```

### 2. Example Usage
```typescript
// Create new user
const user = new User();
user.email = '<EMAIL>';
user.role = UserRole.VENDOR;

// Create vendor profile
const vendor = new Vendor();
vendor.businessName = 'Amazing Art Studio';
vendor.currentPackage = SubscriptionPackage.PREMIUM;

// Create artwork with auto-discount
const product = new Product();
product.name = 'Beautiful Sunset';
product.price = 1000;
product.autoDiscountEnabled = true;

// Check effective price (with discount)
console.log(product.effectivePrice); // May be R900 if 10% discount applies
```

### 3. Database Migrations
All entities are configured for TypeORM auto-migration. Run:
```bash
npm run migration:generate
npm run migration:run
```

## 💡 Business Logic Examples

### Automatic Discount System
```typescript
// Product automatically gets discounts over time
const product = await productRepo.findOne(productId);

console.log(`Original price: ${product.price}`);           // R1,000
console.log(`Months in shop: ${product.monthsInShop}`);    // 4 months
console.log(`Discount: ${product.discountDisplay}`);       // "10% OFF"
console.log(`Final price: ${product.effectivePrice}`);     // R900

// Vendor can disable auto-discounts
product.disableAutoDiscount();
console.log(`Price after disable: ${product.effectivePrice}`); // R1,000
```

### Commission Calculation
```typescript
// Different commission rates by subscription package
const premiumVendor = { currentPackage: 'premium', commissionRate: 0.20 };
const platinumVendor = { currentPackage: 'platinum', commissionRate: 0.15 };

// Sale of R900 artwork
const orderItem = new OrderItem();
orderItem.totalPrice = 900;

// Premium vendor (20% commission)
orderItem.vendor = premiumVendor;
console.log(orderItem.vendorEarnings);    // R720 (80% of R900)
console.log(orderItem.commissionAmount);  // R180 (20% of R900)

// Platinum vendor (15% commission)
orderItem.vendor = platinumVendor;
console.log(orderItem.vendorEarnings);    // R765 (85% of R900)
console.log(orderItem.commissionAmount);  // R135 (15% of R900)
```

### Store Customization Restrictions
```typescript
// Only Premium/Platinum can customize store
const standardVendor = { currentPackage: 'standard' };
const premiumVendor = { currentPackage: 'premium' };

console.log(standardVendor.canCustomizeStore);  // false
console.log(premiumVendor.canCustomizeStore);   // true

// Try to update store logo
if (vendor.canCustomizeStore) {
  vendor.updateStoreLogo('https://example.com/logo.png'); // ✅ Success
} else {
  console.log('Upgrade to Premium to customize your store'); // ❌ Blocked
}
```

### Wallet-Based Payment Processing
```typescript
// Wallet deposit via payment gateway (Payment entity)
const walletDeposit = Payment.createWalletDeposit(
  500,                        // R500 deposit
  PaymentMethod.INSTANT_EFT,  // South African EFT
  'wallet-123'               // User's wallet ID
);

// Process gateway deposit
walletDeposit.markAsProcessing();
await paymentRepo.save(walletDeposit);

// Gateway completes deposit
walletDeposit.markAsCompleted('PF_12345', gatewayResponse);

// Update wallet balance (Transaction entity)
const depositTransaction = Transaction.createDeposit(
  'wallet-123',
  500,
  'ZAR',
  'Wallet deposit via PayFast'
);

// Artwork purchase using wallet money (Transaction entity only)
const buyerWallet = await walletRepo.findOne('buyer-wallet-123');
const vendorWallet = await walletRepo.findOne('vendor-wallet-456');
const companyWallet = await walletRepo.findOne('company-wallet');

// 1. Buyer payment transaction
const buyerPayment = Transaction.createPayment(
  buyerWallet.id,
  900,                    // R900 artwork
  'ZAR',
  order.id,
  'order'
);

// 2. Platform commission (20% for Premium vendor)
const platformCommission = Transaction.createCommission(
  companyWallet.id,
  180,                    // 20% of R900 = R180
  'ZAR',
  order.id
);

// 3. Vendor earnings (remaining 80%)
const vendorEarnings = Transaction.createCommission(
  vendorWallet.id,
  720,                    // R900 - R180 = R720
  'ZAR',
  order.id
);

console.log(`Buyer paid: ${buyerPayment.amountDisplay}`);      // -R900.00
console.log(`Platform earned: ${platformCommission.amountDisplay}`); // +R180.00
console.log(`Vendor earned: ${vendorEarnings.amountDisplay}`);        // +R720.00
```

## 🔧 Advanced Features

### Multi-Vendor Order Handling
```typescript
// Order with items from different vendors
const order = new Order();
order.addOrderItem(itemFromVendor1); // R500 artwork
order.addOrderItem(itemFromVendor2); // R300 artwork
order.addOrderItem(itemFromVendor1); // R200 artwork

console.log(order.vendorCount);      // 2 vendors
console.log(order.isMultiVendor);    // true
console.log(order.totalAmount);     // R1,000

// Commission split calculation
const vendorSplit = OrderItem.calculateCommissionSplit(order.orderItems);
// Returns: [
//   { vendorId: 'vendor1', totalSales: 700, commission: 105, earnings: 595 },
//   { vendorId: 'vendor2', totalSales: 300, commission: 45, earnings: 255 }
// ]
```

### NFT Tokenization (Invisible to Users)
```typescript
// When artist uploads artwork image (background process)
const productImage = new ProductImage();
productImage.imageUrl = 'https://cdn.example.com/sunset.jpg';

// Automatic NFT creation (artist never sees this)
productImage.internal_token_id = 'NFT_12345';
productImage.free_mint_platform = 'polygon';
productImage.tokenization_status = 'completed';

// Blockchain record for NFT
const nftRecord = BlockchainRecord.createForNFT(
  nftTransactionId,
  '0xnft456...'
);

// Artist only sees: "Your artwork is now live in your shop!"
// No mention of NFT or blockchain
```

## 📊 Database Schema Summary

| Entity | Purpose | Key Features |
|--------|---------|--------------|
| **User** | Central user management | Multi-role, email verification |
| **UserAddress** | Address management | SA format, multiple addresses |
| **Vendor** | Artist profiles | Subscription tiers, store customization |
| **Product** | Artwork catalog | Auto-discounts, SEO optimization |
| **Category** | Art categorization | Hierarchical structure |
| **ProductImage** | Artwork images | NFT tokenization, IPFS storage |
| **Order** | Purchase management | Multi-vendor, VAT calculation |
| **OrderItem** | Order line items | Commission tracking, discount history |
| **Payment** | Payment processing | SA payment methods, gateway integration |
| **Wallet** | ZAR balance management | Payment gateway integration |
| **Transaction** | Financial records | Complete audit trail |
| **Subscription** | Vendor subscriptions | Package management, billing |
| **BlockchainRecord** | Immutable proofs | Payment verification, NFT tracking |

## 🛡️ Security & Compliance

### Data Protection
- **Password hashing** with bcrypt
- **Email verification** required
- **Role-based access control**
- **Input validation** on all fields

### Financial Security
- **Immutable blockchain records** for all payments
- **Complete audit trail** for SARS compliance
- **Balance protection** - failed transactions don't affect wallets
- **Commission automation** - reduces manual errors

### South African Compliance
- **POPIA ready** - data protection compliance
- **VAT calculation** - 15% South African VAT
- **ZAR currency** - proper formatting and validation
- **Local payment gateways** - PayFast, Ozow integration

---

**🎨 Built for A Good Man's View Art Marketplace**
**💰 ZAR-focused • 🇿🇦 South African • ⛓️ Blockchain-verified**

*Last updated: June 2025*
