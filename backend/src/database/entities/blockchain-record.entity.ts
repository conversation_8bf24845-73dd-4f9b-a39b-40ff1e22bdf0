import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  OneToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import {
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsDecimal,
  IsInt,
  Length,
  IsUUID,
  Min,
} from 'class-validator';
// import { Transaction } from './transaction.entity';

// Blockchain status enum
export enum BlockchainStatus {
  PENDING = 'pending',
  SUBMITTED = 'submitted',
  CONFIRMED = 'confirmed',
  FAILED = 'failed',
}

@Entity('blockchain_records')
@Index('idx_blockchain_records_transaction_id', ['transactionId'])
@Index('idx_blockchain_records_status', ['status'])
@Index('idx_blockchain_records_block_number', ['blockNumber'])
@Index('idx_blockchain_records_transaction_hash', ['transactionHash'], { unique: true })
export class BlockchainRecord {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  id: string;

  @Column({ name: 'transaction_id', type: 'uuid' })
  @IsUUID()
  @IsNotEmpty({ message: 'Transaction ID is required' })
  transactionId: string;

  @Column({ name: 'block_number', type: 'bigint', nullable: true })
  @IsOptional()
  @IsInt({ message: 'Block number must be an integer' })
  @Min(0, { message: 'Block number cannot be negative' })
  blockNumber?: number;

  @Column({ name: 'block_hash', type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @Length(0, 255, { message: 'Block hash must not exceed 255 characters' })
  blockHash?: string;

  @Column({ name: 'transaction_hash', type: 'varchar', length: 255, unique: true })
  @IsNotEmpty({ message: 'Transaction hash is required' })
  @Length(1, 255, { message: 'Transaction hash must be between 1 and 255 characters' })
  transactionHash: string;

  @Column({ name: 'record_type', type: 'varchar', length: 50, default: 'payment' })
  @IsNotEmpty({ message: 'Record type is required' })
  @Length(1, 50, { message: 'Record type must be between 1 and 50 characters' })
  recordType: string; // 'payment' or 'nft_tokenization'

  @Column({ name: 'gas_used', type: 'bigint', nullable: true })
  @IsOptional()
  @IsInt({ message: 'Gas used must be an integer' })
  @Min(0, { message: 'Gas used cannot be negative' })
  gasUsed?: number;

  @Column({
    name: 'gas_price',
    type: 'decimal',
    precision: 18,
    scale: 8,
    nullable: true,
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,8' }, { message: 'Gas price must be a valid decimal' })
  @Min(0, { message: 'Gas price cannot be negative' })
  gasPrice?: number;

  @Column({ name: 'confirmation_count', type: 'integer', default: 0 })
  @IsInt({ message: 'Confirmation count must be an integer' })
  @Min(0, { message: 'Confirmation count cannot be negative' })
  confirmationCount: number;

  @Column({
    name: 'status',
    type: 'enum',
    enum: BlockchainStatus,
    default: BlockchainStatus.PENDING,
  })
  @IsEnum(BlockchainStatus, { message: 'Invalid blockchain status' })
  status: BlockchainStatus;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @Column({ name: 'confirmed_at', type: 'timestamptz', nullable: true })
  @IsOptional()
  confirmedAt?: Date;

  // Relationships
  @OneToOne('Transaction', 'blockchainRecord', {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'transaction_id' })
  transaction: any;

  // Virtual properties
  get isPending(): boolean {
    return this.status === BlockchainStatus.PENDING;
  }

  get isSubmitted(): boolean {
    return this.status === BlockchainStatus.SUBMITTED;
  }

  get isConfirmed(): boolean {
    return this.status === BlockchainStatus.CONFIRMED;
  }

  get isFailed(): boolean {
    return this.status === BlockchainStatus.FAILED;
  }

  get statusDisplay(): string {
    switch (this.status) {
      case BlockchainStatus.PENDING:
        return 'Pending';
      case BlockchainStatus.SUBMITTED:
        return 'Submitted';
      case BlockchainStatus.CONFIRMED:
        return 'Confirmed';
      case BlockchainStatus.FAILED:
        return 'Failed';
      default:
        return 'Unknown';
    }
  }

  get gasCostDisplay(): string {
    if (!this.gasUsed || !this.gasPrice) return 'N/A';
    const gasCost = (this.gasUsed * this.gasPrice) / 1e18; // Convert to ETH
    return `${gasCost.toFixed(8)} ETH`;
  }

  get confirmationStatus(): string {
    if (this.confirmationCount === 0) return 'Unconfirmed';
    if (this.confirmationCount < 6) return `${this.confirmationCount} confirmations`;
    return 'Fully confirmed';
  }

  get isFullyConfirmed(): boolean {
    return this.confirmationCount >= 6;
  }

  get isPaymentRecord(): boolean {
    return this.recordType === 'payment';
  }

  get isNFTRecord(): boolean {
    return this.recordType === 'nft_tokenization';
  }

  get recordTypeDisplay(): string {
    switch (this.recordType) {
      case 'payment':
        return 'Payment Verification';
      case 'nft_tokenization':
        return 'NFT Tokenization';
      default:
        return 'Unknown';
    }
  }

  get explorerUrl(): string {
    // Default to Polygon for free NFT minting and payment verification
    return `https://polygonscan.com/tx/${this.transactionHash}`;
  }

  get processingTimeMinutes(): number | undefined {
    if (!this.confirmedAt) return undefined;
    const diffMs = this.confirmedAt.getTime() - this.createdAt.getTime();
    return Math.round(diffMs / (1000 * 60));
  }

  // Business logic methods
  submit(): void {
    if (this.isPending) {
      this.status = BlockchainStatus.SUBMITTED;
    }
  }

  confirm(blockNumber?: number, blockHash?: string): void {
    if (this.isSubmitted || this.isPending) {
      this.status = BlockchainStatus.CONFIRMED;
      this.confirmedAt = new Date();
      if (blockNumber) this.blockNumber = blockNumber;
      if (blockHash) this.blockHash = blockHash;
    }
  }

  fail(): void {
    if (this.isSubmitted || this.isPending) {
      this.status = BlockchainStatus.FAILED;
    }
  }

  updateConfirmations(count: number): void {
    this.confirmationCount = Math.max(0, count);
  }

  setGasDetails(gasUsed: number, gasPrice: number): void {
    this.gasUsed = gasUsed;
    this.gasPrice = gasPrice;
  }

  // Static methods
  static createForPayment(transactionId: string, transactionHash: string): Partial<BlockchainRecord> {
    return {
      transactionId,
      transactionHash,
      recordType: 'payment',
      confirmationCount: 0,
      status: BlockchainStatus.PENDING,
    };
  }

  static createForNFT(transactionId: string, transactionHash: string): Partial<BlockchainRecord> {
    return {
      transactionId,
      transactionHash,
      recordType: 'nft_tokenization',
      confirmationCount: 0,
      status: BlockchainStatus.PENDING,
    };
  }

  static getRequiredConfirmations(): number {
    return 6; // Standard for most blockchains
  }

  static getNetworkExplorerUrl(network: string, txHash: string): string {
    switch (network.toLowerCase()) {
      case 'polygon':
        return `https://polygonscan.com/tx/${txHash}`;
      case 'ethereum':
        return `https://etherscan.io/tx/${txHash}`;
      default:
        return `https://polygonscan.com/tx/${txHash}`; // Default to Polygon
    }
  }
}