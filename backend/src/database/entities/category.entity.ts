import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinColumn,
  Index,
  Tree,
  TreeParent,
  TreeChildren,
} from 'typeorm';
import {
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsInt,
  Length,
  IsUUID,
  Min,
  Matches,
} from 'class-validator';
import { Product } from './product.entity';

@Entity('categories')
@Tree('materialized-path')
@Index('idx_categories_parent_id', ['parentId'])
@Index('idx_categories_slug', ['slug'], { unique: true })
@Index('idx_categories_active', ['isActive'])
export class Category {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  id: string;

  @Column({ name: 'name', type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'Category name is required' })
  @Length(2, 100, { message: 'Category name must be between 2 and 100 characters' })
  name: string;

  @Column({ name: 'slug', type: 'varchar', length: 100, unique: true })
  @IsNotEmpty({ message: 'Category slug is required' })
  @Length(2, 100, { message: 'Category slug must be between 2 and 100 characters' })
  @Matches(/^[a-z0-9\-]+$/, { message: 'Slug must contain only lowercase letters, numbers, and hyphens' })
  slug: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  @IsOptional()
  description?: string;

  @Column({ name: 'parent_id', type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  parentId?: string;

  @Column({ name: 'sort_order', type: 'integer', default: 0 })
  @IsInt({ message: 'Sort order must be an integer' })
  @Min(0, { message: 'Sort order cannot be negative' })
  sortOrder: number;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  @IsBoolean()
  isActive: boolean;

  @Column({ name: 'meta_title', type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @Length(0, 255, { message: 'Meta title must not exceed 255 characters' })
  metaTitle?: string;

  @Column({ name: 'meta_description', type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  @Length(0, 500, { message: 'Meta description must not exceed 500 characters' })
  metaDescription?: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  // Hierarchical relationships
  @TreeParent()
  @ManyToOne(() => Category, (category) => category.children, { nullable: true })
  @JoinColumn({ name: 'parent_id' })
  parent?: Category;

  @TreeChildren()
  @OneToMany(() => Category, (category) => category.parent)
  children: Category[];

  // Product relationships
  @ManyToMany(() => Product, (product) => product.categories)
  products: Product[];

  // Virtual properties
  get isRootCategory(): boolean {
    return !this.parentId;
  }

  get hasChildren(): boolean {
    return this.children && this.children.length > 0;
  }

  get hasProducts(): boolean {
    return this.products && this.products.length > 0;
  }

  get productCount(): number {
    return this.products?.length || 0;
  }

  get activeProductCount(): number {
    return this.products?.filter(product => product.isActive).length || 0;
  }

  get level(): number {
    let level = 0;
    let current = this.parent;
    while (current) {
      level++;
      current = current.parent;
    }
    return level;
  }

  get breadcrumb(): Category[] {
    const breadcrumb: Category[] = [];
    let current: Category | undefined = this;
    while (current) {
      breadcrumb.unshift(current);
      current = current.parent;
    }
    return breadcrumb;
  }

  get breadcrumbNames(): string[] {
    return this.breadcrumb.map(category => category.name);
  }

  get breadcrumbPath(): string {
    return this.breadcrumbNames.join(' > ');
  }

  get categoryUrl(): string {
    return `/categories/${this.slug}`;
  }

  get seoTitle(): string {
    return this.metaTitle || this.name;
  }

  get seoDescription(): string {
    return this.metaDescription || this.description?.substring(0, 160) || `Browse ${this.name} artworks`;
  }

  get displayName(): string {
    return this.name;
  }

  get fullPath(): string {
    return this.breadcrumb.map(cat => cat.slug).join('/');
  }

  // Business logic methods
  activate(): void {
    this.isActive = true;
  }

  deactivate(): void {
    this.isActive = false;
  }

  updateSortOrder(order: number): void {
    this.sortOrder = Math.max(0, order);
  }

  canBeDeleted(): boolean {
    return !this.hasChildren && !this.hasProducts;
  }

  getAllDescendants(): Category[] {
    const descendants: Category[] = [];

    const addDescendants = (category: Category) => {
      if (category.children) {
        category.children.forEach(child => {
          descendants.push(child);
          addDescendants(child);
        });
      }
    };

    addDescendants(this);
    return descendants;
  }

  getAllDescendantIds(): string[] {
    return this.getAllDescendants().map(cat => cat.id);
  }

  isAncestorOf(category: Category): boolean {
    let current = category.parent;
    while (current) {
      if (current.id === this.id) {
        return true;
      }
      current = current.parent;
    }
    return false;
  }

  isDescendantOf(category: Category): boolean {
    return category.isAncestorOf(this);
  }

  getActiveChildren(): Category[] {
    return this.children?.filter(child => child.isActive) || [];
  }

  getActiveProducts(): Product[] {
    return this.products?.filter(product => product.isActive) || [];
  }

  // Static methods
  static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  static createArtCategories(): Partial<Category>[] {
    return [
      // Physical art categories only
      {
        name: 'Paintings',
        slug: 'paintings',
        description: 'Original paintings and painted artworks',
        sortOrder: 1,
      },
      {
        name: 'Photography',
        slug: 'photography',
        description: 'Fine art photography and photographic prints',
        sortOrder: 2,
      },
      {
        name: 'Sculptures',
        slug: 'sculptures',
        description: '3D artworks and sculptural pieces',
        sortOrder: 3,
      },
      {
        name: 'Drawings',
        slug: 'drawings',
        description: 'Hand-drawn artworks and sketches',
        sortOrder: 4,
      },
      {
        name: 'Mixed Media',
        slug: 'mixed-media',
        description: 'Artworks combining multiple physical mediums',
        sortOrder: 5,
      },
      {
        name: 'Prints',
        slug: 'prints',
        description: 'Limited edition prints and art reproductions',
        sortOrder: 6,
      },
      {
        name: 'Textiles',
        slug: 'textiles',
        description: 'Fabric art and textile-based works',
        sortOrder: 7,
      },
    ];
  }

  static createPaintingSubcategories(paintingCategoryId: string): Partial<Category>[] {
    return [
      {
        name: 'Oil Paintings',
        slug: 'oil-paintings',
        description: 'Artworks created with oil paints',
        parentId: paintingCategoryId,
        sortOrder: 1,
      },
      {
        name: 'Acrylic Paintings',
        slug: 'acrylic-paintings',
        description: 'Artworks created with acrylic paints',
        parentId: paintingCategoryId,
        sortOrder: 2,
      },
      {
        name: 'Watercolor',
        slug: 'watercolor',
        description: 'Watercolor paintings and artworks',
        parentId: paintingCategoryId,
        sortOrder: 3,
      },
      {
        name: 'Abstract',
        slug: 'abstract-paintings',
        description: 'Abstract and non-representational paintings',
        parentId: paintingCategoryId,
        sortOrder: 4,
      },
      {
        name: 'Portraits',
        slug: 'portrait-paintings',
        description: 'Portrait paintings and figure studies',
        parentId: paintingCategoryId,
        sortOrder: 5,
      },
      {
        name: 'Landscapes',
        slug: 'landscape-paintings',
        description: 'Landscape and nature paintings',
        parentId: paintingCategoryId,
        sortOrder: 6,
      },
    ];
  }

  static createPhotographySubcategories(photographyCategoryId: string): Partial<Category>[] {
    return [
      {
        name: 'Fine Art Photography',
        slug: 'fine-art-photography',
        description: 'Artistic and creative photography',
        parentId: photographyCategoryId,
        sortOrder: 1,
      },
      {
        name: 'Portrait Photography',
        slug: 'portrait-photography',
        description: 'Portrait and people photography',
        parentId: photographyCategoryId,
        sortOrder: 2,
      },
      {
        name: 'Nature Photography',
        slug: 'nature-photography',
        description: 'Landscape and nature photography',
        parentId: photographyCategoryId,
        sortOrder: 3,
      },
    ];
  }

  static createSculptureSubcategories(sculptureCategoryId: string): Partial<Category>[] {
    return [
      {
        name: 'Clay Sculptures',
        slug: 'clay-sculptures',
        description: 'Ceramic and clay sculptural works',
        parentId: sculptureCategoryId,
        sortOrder: 1,
      },
      {
        name: 'Metal Sculptures',
        slug: 'metal-sculptures',
        description: 'Bronze, steel, and metal sculptural works',
        parentId: sculptureCategoryId,
        sortOrder: 2,
      },
      {
        name: 'Wood Sculptures',
        slug: 'wood-sculptures',
        description: 'Carved and constructed wood sculptures',
        parentId: sculptureCategoryId,
        sortOrder: 3,
      },
      {
        name: 'Stone Sculptures',
        slug: 'stone-sculptures',
        description: 'Marble, granite, and stone sculptural works',
        parentId: sculptureCategoryId,
        sortOrder: 4,
      },
      {
        name: 'Mixed Media Sculptures',
        slug: 'mixed-media-sculptures',
        description: 'Sculptures combining multiple materials',
        parentId: sculptureCategoryId,
        sortOrder: 5,
      },
    ];
  }

  static createDrawingSubcategories(drawingCategoryId: string): Partial<Category>[] {
    return [
      {
        name: 'Pencil Drawings',
        slug: 'pencil-drawings',
        description: 'Graphite and pencil artworks',
        parentId: drawingCategoryId,
        sortOrder: 1,
      },
      {
        name: 'Charcoal Drawings',
        slug: 'charcoal-drawings',
        description: 'Charcoal and carbon-based drawings',
        parentId: drawingCategoryId,
        sortOrder: 2,
      },
      {
        name: 'Ink Drawings',
        slug: 'ink-drawings',
        description: 'Pen and ink artworks',
        parentId: drawingCategoryId,
        sortOrder: 3,
      },
      {
        name: 'Pastel Drawings',
        slug: 'pastel-drawings',
        description: 'Oil and soft pastel artworks',
        parentId: drawingCategoryId,
        sortOrder: 4,
      },
      {
        name: 'Sketches',
        slug: 'sketches',
        description: 'Quick studies and sketch works',
        parentId: drawingCategoryId,
        sortOrder: 5,
      },
    ];
  }

  static createMixedMediaSubcategories(mixedMediaCategoryId: string): Partial<Category>[] {
    return [
      {
        name: 'Collage',
        slug: 'collage',
        description: 'Paper and material collage works',
        parentId: mixedMediaCategoryId,
        sortOrder: 1,
      },
      {
        name: 'Assemblage',
        slug: 'assemblage',
        description: '3D mixed media assemblage works',
        parentId: mixedMediaCategoryId,
        sortOrder: 2,
      },
      {
        name: 'Multi-medium Works',
        slug: 'multi-medium-works',
        description: 'Artworks combining various traditional mediums',
        parentId: mixedMediaCategoryId,
        sortOrder: 3,
      },
    ];
  }

  static createPrintSubcategories(printCategoryId: string): Partial<Category>[] {
    return [
      {
        name: 'Limited Edition Prints',
        slug: 'limited-edition-prints',
        description: 'Numbered and signed limited edition prints',
        parentId: printCategoryId,
        sortOrder: 1,
      },
      {
        name: 'Art Reproductions',
        slug: 'art-reproductions',
        description: 'High-quality reproductions of original works',
        parentId: printCategoryId,
        sortOrder: 2,
      },
      {
        name: 'Lithographs',
        slug: 'lithographs',
        description: 'Stone and plate lithographic prints',
        parentId: printCategoryId,
        sortOrder: 3,
      },
    ];
  }

  static createTextileSubcategories(textileCategoryId: string): Partial<Category>[] {
    return [
      {
        name: 'Fabric Art',
        slug: 'fabric-art',
        description: 'Artistic works created with fabric',
        parentId: textileCategoryId,
        sortOrder: 1,
      },
      {
        name: 'Tapestries',
        slug: 'tapestries',
        description: 'Woven and textile tapestries',
        parentId: textileCategoryId,
        sortOrder: 2,
      },
      {
        name: 'Fiber Art',
        slug: 'fiber-art',
        description: 'Contemporary fiber and textile art',
        parentId: textileCategoryId,
        sortOrder: 3,
      },
    ];
  }

  // Tree operations
  static buildCategoryTree(categories: Category[]): Category[] {
    const categoryMap = new Map<string, Category>();
    const rootCategories: Category[] = [];

    // Create a map of all categories
    categories.forEach(category => {
      categoryMap.set(category.id, category);
      category.children = [];
    });

    // Build the tree structure
    categories.forEach(category => {
      if (category.parentId) {
        const parent = categoryMap.get(category.parentId);
        if (parent) {
          parent.children.push(category);
          category.parent = parent;
        }
      } else {
        rootCategories.push(category);
      }
    });

    // Sort categories by sort order
    const sortCategories = (cats: Category[]) => {
      cats.sort((a, b) => a.sortOrder - b.sortOrder);
      cats.forEach(cat => {
        if (cat.children.length > 0) {
          sortCategories(cat.children);
        }
      });
    };

    sortCategories(rootCategories);
    return rootCategories;
  }

  static flattenCategoryTree(categories: Category[]): Category[] {
    const flattened: Category[] = [];

    const addCategory = (category: Category, level: number = 0) => {
      flattened.push(category);
      if (category.children) {
        category.children.forEach(child => addCategory(child, level + 1));
      }
    };

    categories.forEach(category => addCategory(category));
    return flattened;
  }
}