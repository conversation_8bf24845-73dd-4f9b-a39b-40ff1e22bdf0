// Import all entities for the entities array
import { User, UserRole } from './user.entity';
import { UserAddress, AddressType } from './user-address.entity';
import { Vendor } from './vendor.entity';
import { SubscriptionPackage } from './subscription.entity';
import { Product } from './product.entity';
import { Category } from './category.entity';
import { ProductImage } from './product-image.entity';
import { Order, OrderStatus } from './order.entity';
import { OrderItem } from './order-item.entity';
import { Payment, PaymentStatus, PaymentMethod } from './payment.entity';
import { Wallet } from './wallet.entity';
import { Transaction, TransactionType, TransactionStatus } from './transaction.entity';
import { Subscription, SubscriptionStatus } from './subscription.entity';
import { BlockchainRecord, BlockchainStatus } from './blockchain-record.entity';

// Re-export all entities and enums for easy importing by other modules
export { User, UserRole };
export { UserAddress, AddressType };
export { Vendor };
export { SubscriptionPackage };
export { Product };
export { Category };
export { ProductImage };
export { Order, OrderStatus };
export { OrderItem };
export { Payment, PaymentStatus, PaymentMethod };
export { Wallet };
export { Transaction, TransactionType, TransactionStatus };
export { Subscription, SubscriptionStatus };
export { BlockchainRecord, BlockchainStatus };

// Entity array for TypeORM configuration
export const entities = [
  User,
  UserAddress,
  Vendor,
  Product,
  Category,
  ProductImage,
  Order,
  OrderItem,
  Payment,
  Wallet,
  Transaction,
  Subscription,
  BlockchainRecord,
];
