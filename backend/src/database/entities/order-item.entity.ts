import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import {
  IsNotEmpty,
  IsOptional,
  IsDecimal,
  IsPositive,
  IsInt,
  Length,
  IsUUID,
  Min,
} from 'class-validator';
import { Order } from './order.entity';
import { Product } from './product.entity';
import { Vendor } from './vendor.entity';

@Entity('order_items')
@Index('idx_order_items_order_id', ['orderId'])
@Index('idx_order_items_product_id', ['productId'])
@Index('idx_order_items_vendor_id', ['vendorId'])
export class OrderItem {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  id: string;

  @Column({ name: 'order_id', type: 'uuid' })
  @IsUUID()
  @IsNotEmpty({ message: 'Order ID is required' })
  orderId: string;

  @Column({ name: 'product_id', type: 'uuid' })
  @IsUUID()
  @IsNotEmpty({ message: 'Product ID is required' })
  productId: string;

  @Column({ name: 'vendor_id', type: 'uuid' })
  @IsUUID()
  @IsNotEmpty({ message: 'Vendor ID is required' })
  vendorId: string;

  @Column({ name: 'product_name', type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Product name is required' })
  @Length(1, 255, { message: 'Product name must be between 1 and 255 characters' })
  productName: string;

  @Column({ name: 'product_sku', type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @Length(0, 100, { message: 'Product SKU must not exceed 100 characters' })
  productSku?: string;

  @Column({ name: 'quantity', type: 'integer' })
  @IsInt({ message: 'Quantity must be an integer' })
  @IsPositive({ message: 'Quantity must be positive' })
  quantity: number;

  @Column({
    name: 'unit_price',
    type: 'decimal',
    precision: 12,
    scale: 2,
  })
  @IsDecimal({ decimal_digits: '0,2' }, { message: 'Unit price must be a valid decimal' })
  @Min(0, { message: 'Unit price cannot be negative' })
  unitPrice: number;

  @Column({
    name: 'total_price',
    type: 'decimal',
    precision: 12,
    scale: 2,
  })
  @IsDecimal({ decimal_digits: '0,2' }, { message: 'Total price must be a valid decimal' })
  @Min(0, { message: 'Total price cannot be negative' })
  totalPrice: number;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  // Relationships
  @ManyToOne(() => Order, (order) => order.orderItems, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'order_id' })
  order: Order;

  @ManyToOne(() => Product, (product) => product.orderItems)
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @ManyToOne(() => Vendor, (vendor) => vendor.orderItems)
  @JoinColumn({ name: 'vendor_id' })
  vendor: Vendor;

  // Virtual properties
  get unitPriceDisplay(): string {
    return `${this.order?.currency || 'ZAR'} ${this.unitPrice.toFixed(2)}`;
  }

  get totalPriceDisplay(): string {
    return `${this.order?.currency || 'ZAR'} ${this.totalPrice.toFixed(2)}`;
  }

  get isDigitalArt(): boolean {
    return this.product?.isDigitalArt || false;
  }

  get needsShipping(): boolean {
    return !this.isDigitalArt;
  }

  get vendorName(): string {
    return this.vendor?.businessName || 'Unknown Vendor';
  }

  get productUrl(): string {
    return this.product?.productUrl || `/products/${this.productSku || this.productId}`;
  }

  get vendorStoreUrl(): string {
    return this.vendor?.storeUrl || `/store/${this.vendorId}`;
  }

  get lineItemSummary(): string {
    return `${this.quantity}x ${this.productName} @ ${this.unitPriceDisplay}`;
  }

  get commissionAmount(): number {
    if (!this.vendor?.commissionRate) return 0;
    return this.totalPrice * this.vendor.commissionRate;
  }

  get vendorEarnings(): number {
    return this.totalPrice - this.commissionAmount;
  }

  get commissionDisplay(): string {
    return `${this.order?.currency || 'ZAR'} ${this.commissionAmount.toFixed(2)}`;
  }

  get vendorEarningsDisplay(): string {
    return `${this.order?.currency || 'ZAR'} ${this.vendorEarnings.toFixed(2)}`;
  }

  get wasPurchasedWithDiscount(): boolean {
    if (!this.product) return false;
    // Check if the unit price is less than the original product price
    return this.unitPrice < this.product.price;
  }

  get discountApplied(): number {
    if (!this.wasPurchasedWithDiscount || !this.product) return 0;
    return this.product.price - this.unitPrice;
  }

  get discountPercentageApplied(): number {
    if (!this.wasPurchasedWithDiscount || !this.product) return 0;
    return ((this.product.price - this.unitPrice) / this.product.price) * 100;
  }

  get purchaseDiscountDisplay(): string {
    if (!this.wasPurchasedWithDiscount) return '';
    return `${this.discountPercentageApplied.toFixed(0)}% discount applied`;
  }

  // Business logic methods
  updateQuantity(newQuantity: number): void {
    if (newQuantity > 0) {
      this.quantity = newQuantity;
      this.calculateTotalPrice();
    }
  }

  updateUnitPrice(newPrice: number): void {
    if (newPrice >= 0) {
      this.unitPrice = newPrice;
      this.calculateTotalPrice();
    }
  }

  calculateTotalPrice(): void {
    this.totalPrice = this.quantity * this.unitPrice;
  }

  // Static methods
  static createFromProduct(
    orderId: string,
    product: Product,
    quantity: number,
    unitPrice?: number
  ): Partial<OrderItem> {
    // Use effective price (with automatic discount) if no specific price provided
    const price = unitPrice || product.effectivePrice;
    return {
      orderId,
      productId: product.id,
      vendorId: product.vendorId,
      productName: product.name,
      productSku: product.sku,
      quantity,
      unitPrice: price,
      totalPrice: quantity * price,
    };
  }

  static calculateCommissionSplit(orderItems: OrderItem[]): {
    vendorId: string;
    vendorName: string;
    totalSales: number;
    commission: number;
    earnings: number;
  }[] {
    const vendorSales = new Map<string, {
      vendorName: string;
      totalSales: number;
      commission: number;
      earnings: number;
    }>();

    orderItems.forEach(item => {
      const existing = vendorSales.get(item.vendorId) || {
        vendorName: item.vendorName,
        totalSales: 0,
        commission: 0,
        earnings: 0,
      };

      existing.totalSales += item.totalPrice;
      existing.commission += item.commissionAmount;
      existing.earnings += item.vendorEarnings;

      vendorSales.set(item.vendorId, existing);
    });

    return Array.from(vendorSales.entries()).map(([vendorId, data]) => ({
      vendorId,
      ...data,
    }));
  }
}