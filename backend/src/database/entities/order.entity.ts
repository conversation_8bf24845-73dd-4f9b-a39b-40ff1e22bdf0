import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import {
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsDecimal,
  IsPositive,
  Length,
  IsUUID,
  Min,
} from 'class-validator';
import { User } from './user.entity';
import { OrderItem } from './order-item.entity';
import { Payment } from './payment.entity';

// Order status enum
export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
}

// Payment status enum
export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

@Entity('orders')
@Index('idx_orders_user_id', ['userId'])
@Index('idx_orders_status', ['status'])
@Index('idx_orders_payment_status', ['paymentStatus'])
@Index('idx_orders_created_at', ['createdAt'])
@Index('idx_orders_order_number', ['orderNumber'], { unique: true })
export class Order {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  id: string;

  @Column({ name: 'user_id', type: 'uuid' })
  @IsUUID()
  @IsNotEmpty({ message: 'User ID is required' })
  userId: string;

  @Column({ name: 'order_number', type: 'varchar', length: 50, unique: true })
  @IsNotEmpty({ message: 'Order number is required' })
  @Length(1, 50, { message: 'Order number must be between 1 and 50 characters' })
  orderNumber: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.PENDING,
  })
  @IsEnum(OrderStatus, { message: 'Invalid order status' })
  status: OrderStatus;

  @Column({
    name: 'subtotal',
    type: 'decimal',
    precision: 12,
    scale: 2,
  })
  @IsDecimal({ decimal_digits: '0,2' }, { message: 'Subtotal must be a valid decimal' })
  @Min(0, { message: 'Subtotal cannot be negative' })
  subtotal: number;

  @Column({
    name: 'tax_amount',
    type: 'decimal',
    precision: 12,
    scale: 2,
    default: 0,
  })
  @IsDecimal({ decimal_digits: '0,2' }, { message: 'Tax amount must be a valid decimal' })
  @Min(0, { message: 'Tax amount cannot be negative' })
  taxAmount: number;

  @Column({
    name: 'shipping_amount',
    type: 'decimal',
    precision: 12,
    scale: 2,
    default: 0,
  })
  @IsDecimal({ decimal_digits: '0,2' }, { message: 'Shipping amount must be a valid decimal' })
  @Min(0, { message: 'Shipping amount cannot be negative' })
  shippingAmount: number;

  @Column({
    name: 'discount_amount',
    type: 'decimal',
    precision: 12,
    scale: 2,
    default: 0,
  })
  @IsDecimal({ decimal_digits: '0,2' }, { message: 'Discount amount must be a valid decimal' })
  @Min(0, { message: 'Discount amount cannot be negative' })
  discountAmount: number;

  @Column({
    name: 'total_amount',
    type: 'decimal',
    precision: 12,
    scale: 2,
  })
  @IsDecimal({ decimal_digits: '0,2' }, { message: 'Total amount must be a valid decimal' })
  @Min(0, { message: 'Total amount cannot be negative' })
  totalAmount: number;

  @Column({ name: 'currency', type: 'varchar', length: 3, default: 'ZAR' })
  @Length(3, 3, { message: 'Currency must be exactly 3 characters' })
  currency: string;

  @Column({
    name: 'payment_status',
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  @IsEnum(PaymentStatus, { message: 'Invalid payment status' })
  paymentStatus: PaymentStatus;

  @Column({ name: 'payment_method', type: 'varchar', length: 50, nullable: true })
  @IsOptional()
  @Length(0, 50, { message: 'Payment method must not exceed 50 characters' })
  paymentMethod?: string;

  @Column({ name: 'shipping_address', type: 'jsonb', nullable: true })
  @IsOptional()
  shippingAddress?: {
    firstName: string;
    lastName: string;
    company?: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    province: string;
    postalCode: string;
    country: string;
    phone?: string;
  };

  @Column({ name: 'billing_address', type: 'jsonb', nullable: true })
  @IsOptional()
  billingAddress?: {
    firstName: string;
    lastName: string;
    company?: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    province: string;
    postalCode: string;
    country: string;
    phone?: string;
  };

  @Column({ name: 'notes', type: 'text', nullable: true })
  @IsOptional()
  notes?: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => User, (user) => user.orders)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => OrderItem, (orderItem) => orderItem.order, {
    cascade: true,
  })
  orderItems: OrderItem[];

  @OneToMany(() => Payment, (payment) => payment.order, {
    cascade: true,
  })
  payments: Payment[];

  // Virtual properties
  get isPending(): boolean {
    return this.status === OrderStatus.PENDING;
  }

  get isConfirmed(): boolean {
    return this.status === OrderStatus.CONFIRMED;
  }

  get isProcessing(): boolean {
    return this.status === OrderStatus.PROCESSING;
  }

  get isShipped(): boolean {
    return this.status === OrderStatus.SHIPPED;
  }

  get isDelivered(): boolean {
    return this.status === OrderStatus.DELIVERED;
  }

  get isPaymentPending(): boolean {
    return this.paymentStatus === PaymentStatus.PENDING;
  }

  get isPaymentCompleted(): boolean {
    return this.paymentStatus === PaymentStatus.COMPLETED;
  }

  get isPaymentFailed(): boolean {
    return this.paymentStatus === PaymentStatus.FAILED;
  }

  get statusDisplay(): string {
    switch (this.status) {
      case OrderStatus.PENDING:
        return 'Pending';
      case OrderStatus.CONFIRMED:
        return 'Confirmed';
      case OrderStatus.PROCESSING:
        return 'Processing';
      case OrderStatus.SHIPPED:
        return 'Shipped';
      case OrderStatus.DELIVERED:
        return 'Delivered';
      default:
        return 'Unknown';
    }
  }

  get paymentStatusDisplay(): string {
    switch (this.paymentStatus) {
      case PaymentStatus.PENDING:
        return 'Payment Pending';
      case PaymentStatus.PROCESSING:
        return 'Processing Payment';
      case PaymentStatus.COMPLETED:
        return 'Payment Completed';
      case PaymentStatus.FAILED:
        return 'Payment Failed';
      default:
        return 'Unknown';
    }
  }

  get totalDisplay(): string {
    return `${this.currency} ${this.totalAmount.toFixed(2)}`;
  }

  get subtotalDisplay(): string {
    return `${this.currency} ${this.subtotal.toFixed(2)}`;
  }

  get taxDisplay(): string {
    return `${this.currency} ${this.taxAmount.toFixed(2)}`;
  }

  get shippingDisplay(): string {
    return `${this.currency} ${this.shippingAmount.toFixed(2)}`;
  }

  get discountDisplay(): string {
    return `${this.currency} ${this.discountAmount.toFixed(2)}`;
  }

  get itemCount(): number {
    return this.orderItems?.reduce((total, item) => total + item.quantity, 0) || 0;
  }

  get uniqueItemCount(): number {
    return this.orderItems?.length || 0;
  }

  get vendorCount(): number {
    const vendorIds = new Set(this.orderItems?.map(item => item.vendorId) || []);
    return vendorIds.size;
  }

  get isMultiVendor(): boolean {
    return this.vendorCount > 1;
  }

  get hasDigitalItems(): boolean {
    return this.orderItems?.some(item => item.product?.isDigitalArt) || false;
  }

  get hasPhysicalItems(): boolean {
    return this.orderItems?.some(item => !item.product?.isDigitalArt) || false;
  }

  get needsShipping(): boolean {
    return this.hasPhysicalItems;
  }

  get shippingAddressDisplay(): string {
    if (!this.shippingAddress) return '';
    const addr = this.shippingAddress;
    return `${addr.firstName} ${addr.lastName}, ${addr.addressLine1}, ${addr.city}, ${addr.province} ${addr.postalCode}`;
  }

  // Business logic methods
  confirm(): void {
    if (this.isPending) {
      this.status = OrderStatus.CONFIRMED;
    }
  }

  startProcessing(): void {
    if (this.isConfirmed) {
      this.status = OrderStatus.PROCESSING;
    }
  }

  ship(): void {
    if (this.isProcessing) {
      this.status = OrderStatus.SHIPPED;
    }
  }

  deliver(): void {
    if (this.isShipped) {
      this.status = OrderStatus.DELIVERED;
    }
  }

  markPaymentCompleted(method?: string): void {
    this.paymentStatus = PaymentStatus.COMPLETED;
    if (method) {
      this.paymentMethod = method;
    }
  }

  markPaymentFailed(): void {
    this.paymentStatus = PaymentStatus.FAILED;
  }

  calculateTotals(): void {
    this.subtotal = this.orderItems?.reduce((total, item) => total + item.totalPrice, 0) || 0;
    this.totalAmount = this.subtotal + this.taxAmount + this.shippingAmount - this.discountAmount;
  }

  addOrderItem(item: OrderItem): void {
    if (!this.orderItems) {
      this.orderItems = [];
    }
    this.orderItems.push(item);
    this.calculateTotals();
  }

  removeOrderItem(itemId: string): void {
    if (this.orderItems) {
      this.orderItems = this.orderItems.filter(item => item.id !== itemId);
      this.calculateTotals();
    }
  }

  // Static methods
  static generateOrderNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `AGV-${timestamp.slice(-8)}-${random}`;
  }

  static calculateTax(subtotal: number, taxRate: number = 0.15): number {
    return subtotal * taxRate; // 15% VAT for South Africa
  }

  static calculateShipping(items: OrderItem[], shippingRate: number = 50): number {
    const hasPhysicalItems = items.some(item => !item.product?.isDigitalArt);
    return hasPhysicalItems ? shippingRate : 0;
  }
}