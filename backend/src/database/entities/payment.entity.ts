import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import {
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsDecimal,
  IsUUID,
  Min,
  Max,
  Length,
} from 'class-validator';
import { Order } from './order.entity';
import { Wallet } from './wallet.entity';

// Payment status enum
export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

// Payment method enum - for payment gateway transactions only
export enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  EFT = 'eft',
  INSTANT_EFT = 'instant_eft',
  BANK_TRANSFER = 'bank_transfer',
}

@Entity('payments')
@Index('idx_payments_order_id', ['orderId'])
@Index('idx_payments_wallet_id', ['walletId'])
@Index('idx_payments_status', ['status'])
@Index('idx_payments_method', ['method'])
@Index('idx_payments_created_at', ['createdAt'])
@Index('idx_payments_reference', ['paymentReference'], { unique: true })
export class Payment {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  id: string;

  @Column({ name: 'order_id', type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  orderId?: string;

  @Column({ name: 'wallet_id', type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  walletId?: string;

  @Column({
    name: 'amount',
    type: 'decimal',
    precision: 12,
    scale: 2,
  })
  @IsDecimal({ decimal_digits: '0,2' }, { message: 'Amount must be a valid decimal' })
  @Min(0.01, { message: 'Amount must be greater than 0' })
  @Max(999999.99, { message: 'Amount cannot exceed R999,999.99' })
  amount: number;

  @Column({ name: 'currency', type: 'varchar', length: 3, default: 'ZAR' })
  @IsNotEmpty({ message: 'Currency is required' })
  @Length(3, 3, { message: 'Currency must be exactly 3 characters' })
  currency: string;

  @Column({
    name: 'method',
    type: 'enum',
    enum: PaymentMethod,
  })
  @IsEnum(PaymentMethod, { message: 'Invalid payment method' })
  method: PaymentMethod;

  @Column({
    name: 'status',
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  @IsEnum(PaymentStatus, { message: 'Invalid payment status' })
  status: PaymentStatus;

  @Column({ name: 'payment_reference', type: 'varchar', length: 100, unique: true })
  @IsNotEmpty({ message: 'Payment reference is required' })
  @Length(1, 100, { message: 'Payment reference must be between 1 and 100 characters' })
  paymentReference: string;

  @Column({ name: 'gateway_reference', type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @Length(1, 255, { message: 'Gateway reference must be between 1 and 255 characters' })
  gatewayReference?: string;

  @Column({ name: 'gateway_response', type: 'jsonb', nullable: true })
  @IsOptional()
  gatewayResponse?: {
    transactionId?: string;
    authCode?: string;
    responseCode?: string;
    responseMessage?: string;
    cardMask?: string;
    cardType?: string;
  };

  @Column({ name: 'failure_reason', type: 'text', nullable: true })
  @IsOptional()
  failureReason?: string;

  @Column({ name: 'processed_at', type: 'timestamptz', nullable: true })
  @IsOptional()
  processedAt?: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => Order, (order) => order.payments, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  @JoinColumn({ name: 'order_id' })
  order?: Order;

  @ManyToOne(() => Wallet, (wallet) => wallet.transactions, {
    onDelete: 'SET NULL',
    nullable: true,
  })
  @JoinColumn({ name: 'wallet_id' })
  wallet?: Wallet;

  // Virtual properties
  get isPending(): boolean {
    return this.status === PaymentStatus.PENDING;
  }

  get isProcessing(): boolean {
    return this.status === PaymentStatus.PROCESSING;
  }

  get isCompleted(): boolean {
    return this.status === PaymentStatus.COMPLETED;
  }

  get isFailed(): boolean {
    return this.status === PaymentStatus.FAILED;
  }

  get amountDisplay(): string {
    return `${this.currency} ${this.amount.toFixed(2)}`;
  }

  get methodDisplay(): string {
    return this.method.replace('_', ' ').toUpperCase();
  }

  get statusDisplay(): string {
    return this.status.charAt(0).toUpperCase() + this.status.slice(1);
  }

  get isSuccessful(): boolean {
    return this.status === PaymentStatus.COMPLETED;
  }

  get isFinalStatus(): boolean {
    return this.status === PaymentStatus.COMPLETED || this.status === PaymentStatus.FAILED;
  }

  get isGatewayPayment(): boolean {
    return true; // All payments go through payment gateway
  }

  get amountFormatted(): string {
    if (this.currency === 'ZAR') {
      return `R${this.amount.toFixed(2)}`;
    }
    return `${this.amount.toFixed(2)} ${this.currency}`;
  }

  get isOrderPayment(): boolean {
    return !!this.orderId;
  }

  get isWalletDeposit(): boolean {
    return !this.orderId && !!this.walletId && this.amount > 0;
  }

  get isWalletWithdrawal(): boolean {
    return !this.orderId && !!this.walletId && this.amount < 0;
  }

  // Business logic methods
  markAsProcessing(): void {
    this.status = PaymentStatus.PROCESSING;
  }

  markAsCompleted(gatewayRef?: string, response?: any): void {
    this.status = PaymentStatus.COMPLETED;
    this.processedAt = new Date();
    if (gatewayRef) this.gatewayReference = gatewayRef;
    if (response) this.gatewayResponse = response;
  }

  markAsFailed(reason: string): void {
    this.status = PaymentStatus.FAILED;
    this.failureReason = reason;
    this.processedAt = new Date();
  }

  // Static methods
  static generateReference(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `PAY-${timestamp}-${random}`;
  }

  static createForOrder(orderId: string, amount: number, method: PaymentMethod, walletId?: string): Partial<Payment> {
    return {
      orderId,
      amount,
      method,
      walletId,
      currency: 'ZAR',
      status: PaymentStatus.PENDING,
      paymentReference: Payment.generateReference(),
    };
  }

  static createWalletDeposit(amount: number, method: PaymentMethod, walletId: string): Partial<Payment> {
    return {
      // orderId is undefined for wallet deposits (no associated order)
      amount,
      method,
      walletId,
      currency: 'ZAR',
      status: PaymentStatus.PENDING,
      paymentReference: Payment.generateReference(),
    };
  }

  static createWalletWithdrawal(amount: number, method: PaymentMethod, walletId: string): Partial<Payment> {
    return {
      // orderId is undefined for wallet withdrawals (no associated order)
      amount: -Math.abs(amount),
      method,
      walletId,
      currency: 'ZAR',
      status: PaymentStatus.PENDING,
      paymentReference: Payment.generateReference(),
    };
  }
}
