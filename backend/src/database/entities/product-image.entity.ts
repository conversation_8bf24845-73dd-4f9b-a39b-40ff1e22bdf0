import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import {
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsInt,
  IsEnum,
  Length,
  IsUUID,
  Min,
  Max,
} from 'class-validator';
import { Product } from './product.entity';
import { User } from './user.entity';

// Tokenization status enum
export enum TokenizationStatus {
  QUEUED = 'queued',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  ERROR = 'error',
}

@Entity('product_images')
@Index('idx_product_images_product_id', ['productId'])
@Index('idx_product_images_primary', ['isPrimary'], { where: 'is_primary = TRUE' })
@Index('idx_product_images_internal_token', ['internalTokenId'], { where: 'internal_token_id IS NOT NULL' })
@Index('idx_product_images_free_mint_platform', ['freeMintPlatform'])
@Index('idx_product_images_blockchain_network', ['blockchainNetwork'])
@Index('idx_product_images_collection_id', ['platformCollectionId'], { where: 'platform_collection_id IS NOT NULL' })
@Index('idx_product_images_uploader_ref', ['uploaderReference'])
@Index('idx_product_images_tokenization_status', ['tokenizationStatus'])
@Index('idx_product_images_queued_processing', ['tokenizationStatus'], { where: "tokenization_status IN ('queued', 'processing')" })
@Index('idx_product_images_error_processing', ['tokenizationStatus'], { where: "tokenization_status = 'error'" })
export class ProductImage {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  id: string;

  @Column({ name: 'product_id', type: 'uuid' })
  @IsUUID()
  @IsNotEmpty({ message: 'Product ID is required' })
  productId: string;

  @Column({ name: 'image_url', type: 'varchar', length: 500 })
  @IsNotEmpty({ message: 'Image URL is required' })
  @Length(1, 500, { message: 'Image URL must be between 1 and 500 characters' })
  imageUrl: string;

  @Column({ name: 'alt_text', type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @Length(0, 255, { message: 'Alt text must not exceed 255 characters' })
  altText?: string;

  @Column({ name: 'sort_order', type: 'integer', default: 0 })
  @IsInt({ message: 'Sort order must be an integer' })
  @Min(0, { message: 'Sort order cannot be negative' })
  sortOrder: number;

  @Column({ name: 'is_primary', type: 'boolean', default: false })
  @IsBoolean()
  isPrimary: boolean;

  // Internal blockchain fields (completely hidden from all user interfaces)
  @Column({ name: 'internal_token_id', type: 'varchar', length: 255, unique: true, nullable: true })
  @IsOptional()
  @Length(0, 255, { message: 'Internal token ID must not exceed 255 characters' })
  internalTokenId?: string;

  @Column({ name: 'free_mint_platform', type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @Length(0, 100, { message: 'Free mint platform must not exceed 100 characters' })
  // e.g., 'polygon', 'opensea', 'mintbase'
  freeMintPlatform?: string;

  @Column({ name: 'blockchain_network', type: 'varchar', length: 50, nullable: true })
  @IsOptional()
  @Length(0, 50, { message: 'Blockchain network must not exceed 50 characters' })
  // e.g., 'polygon', 'ethereum', 'near'
  blockchainNetwork?: string;

  @Column({ name: 'blockchain_tx_hash', type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @Length(0, 255, { message: 'Blockchain transaction hash must not exceed 255 characters' })
  blockchainTxHash?: string;

  @Column({ name: 'platform_collection_id', type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @Length(0, 255, { message: 'Platform collection ID must not exceed 255 characters' })
  // Collection ID on free minting platform
  platformCollectionId?: string;

  @Column({ name: 'uploader_reference', type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  uploaderReference?: string;

  @Column({
    name: 'tokenization_status',
    type: 'varchar',
    length: 20,
    default: TokenizationStatus.QUEUED,
  })
  @IsEnum(TokenizationStatus, { message: 'Invalid tokenization status' })
  tokenizationStatus: TokenizationStatus;

  @Column({ name: 'metadata_uri', type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  @Length(0, 500, { message: 'Metadata URI must not exceed 500 characters' })
  // IPFS or platform metadata URL
  metadataUri?: string;

  @Column({ name: 'tokenized_at', type: 'timestamptz', nullable: true })
  @IsOptional()
  tokenizedAt?: Date;

  @Column({ name: 'process_attempts', type: 'integer', default: 0 })
  @IsInt({ message: 'Process attempts must be an integer' })
  @Min(0, { message: 'Process attempts cannot be negative' })
  processAttempts: number;

  @Column({ name: 'process_log', type: 'text', nullable: true })
  @IsOptional()
  processLog?: string;

  @Column({
    name: 'gas_cost',
    type: 'decimal',
    precision: 18,
    scale: 8,
    default: 0,
  })
  @Min(0, { message: 'Gas cost cannot be negative' })
  // Track any minimal gas costs
  gasCost: number;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => Product, (product) => product.images, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'uploader_reference' })
  uploader?: User;

  // Virtual properties
  get isTokenized(): boolean {
    return this.tokenizationStatus === TokenizationStatus.COMPLETED;
  }

  get isProcessing(): boolean {
    return this.tokenizationStatus === TokenizationStatus.PROCESSING;
  }

  get isQueued(): boolean {
    return this.tokenizationStatus === TokenizationStatus.QUEUED;
  }

  get hasError(): boolean {
    return this.tokenizationStatus === TokenizationStatus.ERROR;
  }

  get tokenizationStatusDisplay(): string {
    switch (this.tokenizationStatus) {
      case TokenizationStatus.QUEUED:
        return 'Queued for Processing';
      case TokenizationStatus.PROCESSING:
        return 'Processing NFT';
      case TokenizationStatus.COMPLETED:
        return 'NFT Created';
      case TokenizationStatus.ERROR:
        return 'Processing Failed';
      default:
        return 'Unknown Status';
    }
  }

  get platformDisplayName(): string {
    if (!this.freeMintPlatform) return 'Not Set';

    switch (this.freeMintPlatform.toLowerCase()) {
      case 'polygon':
        return 'Polygon';
      case 'opensea':
        return 'OpenSea';
      case 'mintbase':
        return 'Mintbase';
      case 'rarible':
        return 'Rarible';
      default:
        return this.freeMintPlatform;
    }
  }

  get networkDisplayName(): string {
    if (!this.blockchainNetwork) return 'Not Set';

    switch (this.blockchainNetwork.toLowerCase()) {
      case 'polygon':
        return 'Polygon';
      case 'ethereum':
        return 'Ethereum';
      case 'near':
        return 'NEAR';
      case 'solana':
        return 'Solana';
      default:
        return this.blockchainNetwork;
    }
  }

  get explorerUrl(): string | undefined {
    if (!this.blockchainTxHash || !this.blockchainNetwork) return undefined;

    switch (this.blockchainNetwork.toLowerCase()) {
      case 'polygon':
        return `https://polygonscan.com/tx/${this.blockchainTxHash}`;
      case 'ethereum':
        return `https://etherscan.io/tx/${this.blockchainTxHash}`;
      case 'near':
        return `https://explorer.near.org/transactions/${this.blockchainTxHash}`;
      default:
        return undefined;
    }
  }

  get gasCostDisplay(): string {
    if (this.gasCost === 0) return 'Free';
    return `${this.gasCost.toFixed(8)} ETH`;
  }

  get shouldRetryProcessing(): boolean {
    return this.hasError && this.processAttempts < 3;
  }

  get daysSinceTokenization(): number | undefined {
    if (!this.tokenizedAt) return undefined;
    const now = new Date();
    const diffTime = now.getTime() - this.tokenizedAt.getTime();
    return Math.floor(diffTime / (1000 * 60 * 60 * 24));
  }

  // Business logic methods
  markAsPrimary(): void {
    this.isPrimary = true;
  }

  removePrimary(): void {
    this.isPrimary = false;
  }

  startTokenization(platform: string, network: string, uploaderRef?: string): void {
    this.tokenizationStatus = TokenizationStatus.PROCESSING;
    this.freeMintPlatform = platform;
    this.blockchainNetwork = network;
    this.uploaderReference = uploaderRef;
    this.processAttempts += 1;
  }

  completeTokenization(tokenId: string, txHash: string, metadataUri?: string, collectionId?: string): void {
    this.tokenizationStatus = TokenizationStatus.COMPLETED;
    this.internalTokenId = tokenId;
    this.blockchainTxHash = txHash;
    this.metadataUri = metadataUri;
    this.platformCollectionId = collectionId;
    this.tokenizedAt = new Date();
  }

  failTokenization(errorLog: string): void {
    this.tokenizationStatus = TokenizationStatus.ERROR;
    this.processLog = errorLog;
  }

  resetForRetry(): void {
    if (this.shouldRetryProcessing) {
      this.tokenizationStatus = TokenizationStatus.QUEUED;
      this.processLog = undefined;
    }
  }

  updateSortOrder(order: number): void {
    this.sortOrder = Math.max(0, order);
  }

  // Static methods
  static createForProduct(productId: string, imageUrl: string, altText?: string): Partial<ProductImage> {
    return {
      productId,
      imageUrl,
      altText,
      sortOrder: 0,
      isPrimary: false,
      tokenizationStatus: TokenizationStatus.QUEUED,
      processAttempts: 0,
      gasCost: 0,
    };
  }

  static getDefaultPlatformSettings(): { platform: string; network: string } {
    // Free minting on Polygon
    return {
      platform: 'polygon',
      network: 'polygon',
    };
  }
}