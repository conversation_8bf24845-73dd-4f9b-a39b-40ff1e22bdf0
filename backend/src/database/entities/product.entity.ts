import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinColumn,
  JoinTable,
  Index,
} from 'typeorm';
import {
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsDecimal,
  IsPositive,
  IsInt,
  Length,
  IsUUID,
  Min,
  Max,
  Matches,
} from 'class-validator';
import { Vendor } from './vendor.entity';
import { ProductImage } from './product-image.entity';
import { Category } from './category.entity';
import { OrderItem } from './order-item.entity';

@Entity('products')
@Index('idx_products_vendor_id', ['vendorId'])
@Index('idx_products_slug', ['slug'], { unique: true })
@Index('idx_products_active', ['isActive'])
@Index('idx_products_featured', ['isFeatured'], { where: 'is_featured = TRUE' })
@Index('idx_products_price', ['price'])
@Index('idx_products_created_at', ['createdAt'])
export class Product {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  id: string;

  @Column({ name: 'vendor_id', type: 'uuid' })
  @IsUUID()
  @IsNotEmpty({ message: 'Vendor ID is required' })
  vendorId: string;

  @Column({ name: 'name', type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Product name is required' })
  @Length(2, 255, { message: 'Product name must be between 2 and 255 characters' })
  name: string;

  @Column({ name: 'slug', type: 'varchar', length: 255, unique: true })
  @IsNotEmpty({ message: 'Product slug is required' })
  @Length(2, 255, { message: 'Product slug must be between 2 and 255 characters' })
  @Matches(/^[a-z0-9\-]+$/, { message: 'Slug must contain only lowercase letters, numbers, and hyphens' })
  slug: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  @IsOptional()
  description?: string;

  @Column({ name: 'short_description', type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  @Length(0, 500, { message: 'Short description must not exceed 500 characters' })
  shortDescription?: string;

  @Column({ name: 'sku', type: 'varchar', length: 100, unique: true, nullable: true })
  @IsOptional()
  @Length(0, 100, { message: 'SKU must not exceed 100 characters' })
  sku?: string;

  @Column({
    name: 'price',
    type: 'decimal',
    precision: 12,
    scale: 2,
  })
  @IsDecimal({ decimal_digits: '0,2' }, { message: 'Price must be a valid decimal' })
  @IsPositive({ message: 'Price must be positive' })
  price: number;

  @Column({ name: 'currency', type: 'varchar', length: 3, default: 'ZAR' })
  @Length(3, 3, { message: 'Currency must be exactly 3 characters' })
  currency: string;

  @Column({
    name: 'cost_price',
    type: 'decimal',
    precision: 12,
    scale: 2,
    nullable: true,
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' }, { message: 'Cost price must be a valid decimal' })
  @Min(0, { message: 'Cost price cannot be negative' })
  costPrice?: number;

  @Column({ name: 'stock_quantity', type: 'integer', default: 0 })
  @IsInt({ message: 'Stock quantity must be an integer' })
  @Min(0, { message: 'Stock quantity cannot be negative' })
  stockQuantity: number;

  @Column({ name: 'low_stock_threshold', type: 'integer', default: 10 })
  @IsInt({ message: 'Low stock threshold must be an integer' })
  @Min(0, { message: 'Low stock threshold cannot be negative' })
  lowStockThreshold: number;

  @Column({
    name: 'weight',
    type: 'decimal',
    precision: 8,
    scale: 3,
    nullable: true,
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,3' }, { message: 'Weight must be a valid decimal' })
  @Min(0, { message: 'Weight cannot be negative' })
  weight?: number;

  @Column({ name: 'dimensions', type: 'jsonb', nullable: true })
  @IsOptional()
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
    // cm, inches, etc.
    unit?: string;
  };

  @Column({ name: 'is_active', type: 'boolean', default: true })
  @IsBoolean()
  isActive: boolean;

  @Column({ name: 'is_featured', type: 'boolean', default: false })
  @IsBoolean()
  isFeatured: boolean;

  @Column({ name: 'auto_discount_enabled', type: 'boolean', default: true })
  @IsBoolean()
  autoDiscountEnabled: boolean;

  @Column({ name: 'meta_title', type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @Length(0, 255, { message: 'Meta title must not exceed 255 characters' })
  metaTitle?: string;

  @Column({ name: 'meta_description', type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  @Length(0, 500, { message: 'Meta description must not exceed 500 characters' })
  metaDescription?: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @Column({ name: 'deleted_at', type: 'timestamptz', nullable: true })
  deletedAt?: Date;

  // Relationships
  @ManyToOne(() => Vendor, (vendor) => vendor.products, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'vendor_id' })
  vendor: Vendor;

  @OneToMany(() => ProductImage, (image) => image.product, {
    cascade: true,
  })
  images: ProductImage[];

  @ManyToMany(() => Category, (category) => category.products)
  @JoinTable({
    name: 'product_categories',
    joinColumn: { name: 'product_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'category_id', referencedColumnName: 'id' },
  })
  categories: Category[];

  @OneToMany(() => OrderItem, (orderItem) => orderItem.product)
  orderItems: OrderItem[];

  // Virtual properties
  get isLowStock(): boolean {
    return this.stockQuantity <= this.lowStockThreshold;
  }

  get isOutOfStock(): boolean {
    return this.stockQuantity === 0;
  }

  get isInStock(): boolean {
    return this.stockQuantity > 0;
  }

  get priceDisplay(): string {
    return `${this.currency} ${this.price.toFixed(2)}`;
  }

  get primaryImage(): ProductImage | undefined {
    return this.images?.find(image => image.isPrimary) || this.images?.[0];
  }

  get primaryImageUrl(): string | undefined {
    return this.primaryImage?.imageUrl;
  }

  get stockStatus(): string {
    if (this.isOutOfStock) return 'Out of Stock';
    if (this.isLowStock) return 'Low Stock';
    return 'In Stock';
  }

  get stockStatusColor(): string {
    if (this.isOutOfStock) return 'red';
    if (this.isLowStock) return 'orange';
    return 'green';
  }

  get dimensionsDisplay(): string {
    if (!this.dimensions) return '';
    const { length, width, height, unit = 'cm' } = this.dimensions;
    if (length && width && height) {
      return `${length} × ${width} × ${height} ${unit}`;
    }
    if (length && width) {
      return `${length} × ${width} ${unit}`;
    }
    return '';
  }

  get weightDisplay(): string {
    if (!this.weight) return '';
    return `${this.weight} kg`;
  }

  get isDigitalArt(): boolean {
    return this.weight === null || this.weight === 0;
  }

  get categoryNames(): string[] {
    return this.categories?.map(category => category.name) || [];
  }

  get seoTitle(): string {
    return this.metaTitle || this.name;
  }

  get seoDescription(): string {
    return this.metaDescription || this.shortDescription || this.description?.substring(0, 160) || '';
  }

  get productUrl(): string {
    return `/products/${this.slug}`;
  }

  get vendorStoreUrl(): string {
    return `/store/${this.vendor?.businessName?.toLowerCase().replace(/\s+/g, '-')}`;
  }

  get monthsInShop(): number {
    const now = new Date();
    const diffTime = now.getTime() - this.createdAt.getTime();
    return Math.floor(diffTime / (1000 * 60 * 60 * 24 * 30)); // Approximate months
  }

  get automaticDiscountPercentage(): number {
    if (!this.autoDiscountEnabled) return 0;

    const months = this.monthsInShop;
    if (months >= 6) return 15; // 15% discount after 6 months
    if (months >= 3) return 10; // 10% discount after 3 months
    return 0;
  }

  get hasAutomaticDiscount(): boolean {
    return this.automaticDiscountPercentage > 0;
  }

  get discountedPrice(): number {
    if (!this.hasAutomaticDiscount) return this.price;
    const discountAmount = this.price * (this.automaticDiscountPercentage / 100);
    return this.price - discountAmount;
  }

  get discountAmount(): number {
    return this.price - this.discountedPrice;
  }

  get effectivePrice(): number {
    return this.hasAutomaticDiscount ? this.discountedPrice : this.price;
  }

  get effectivePriceDisplay(): string {
    return `${this.currency} ${this.effectivePrice.toFixed(2)}`;
  }

  get discountDisplay(): string {
    if (!this.hasAutomaticDiscount) return '';
    return `${this.automaticDiscountPercentage}% OFF`;
  }

  get priceWithDiscountDisplay(): string {
    if (!this.hasAutomaticDiscount) {
      return this.priceDisplay;
    }
    return `${this.effectivePriceDisplay} (was ${this.priceDisplay})`;
  }

  // Business logic methods
  updateStock(quantity: number): void {
    this.stockQuantity = Math.max(0, this.stockQuantity + quantity);
  }

  reduceStock(quantity: number): boolean {
    if (this.stockQuantity >= quantity) {
      this.stockQuantity -= quantity;
      return true;
    }
    return false;
  }

  increaseStock(quantity: number): void {
    this.stockQuantity += quantity;
  }

  setFeatured(featured: boolean): void {
    this.isFeatured = featured;
  }

  activate(): void {
    this.isActive = true;
  }

  deactivate(): void {
    this.isActive = false;
  }

  softDelete(): void {
    this.deletedAt = new Date();
    this.isActive = false;
  }

  restore(): void {
    this.deletedAt = undefined;
    this.isActive = true;
  }

  enableAutoDiscount(): void {
    this.autoDiscountEnabled = true;
  }

  disableAutoDiscount(): void {
    this.autoDiscountEnabled = false;
  }

  toggleAutoDiscount(): void {
    this.autoDiscountEnabled = !this.autoDiscountEnabled;
  }

  // Static methods
  static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  static generateSKU(vendorId: string, productName: string): string {
    const vendorPrefix = vendorId.substring(0, 4).toUpperCase();
    const namePrefix = productName.substring(0, 4).toUpperCase().replace(/[^A-Z0-9]/g, '');
    const timestamp = Date.now().toString().slice(-6);
    return `${vendorPrefix}-${namePrefix}-${timestamp}`;
  }
}