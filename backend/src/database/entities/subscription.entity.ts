import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import {
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsDecimal,
  IsDateString,
  IsPositive,
  Length,
  IsUUID,
  Min,
  Max,
} from 'class-validator';
import { Vendor } from './vendor.entity';

// Subscription package enum
export enum SubscriptionPackage {
  STANDARD = 'standard',
  PREMIUM = 'premium',
  PLATINUM = 'platinum',
}

// Subscription status enum
export enum SubscriptionStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
  SUSPENDED = 'suspended',
}

@Entity('subscriptions')
@Index('idx_subscriptions_vendor_id', ['vendorId'])
@Index('idx_subscriptions_status', ['status'])
@Index('idx_subscriptions_package_type', ['packageType'])
@Index('idx_subscriptions_end_date', ['endDate'])
@Index('idx_subscriptions_active', ['status', 'endDate'], { where: "status = 'active'" })
export class Subscription {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  id: string;

  @Column({ name: 'vendor_id', type: 'uuid' })
  @IsUUID()
  @IsNotEmpty({ message: 'Vendor ID is required' })
  vendorId: string;

  @Column({
    name: 'package_type',
    type: 'enum',
    enum: SubscriptionPackage,
  })
  @IsEnum(SubscriptionPackage, { message: 'Invalid subscription package' })
  packageType: SubscriptionPackage;

  @Column({
    name: 'status',
    type: 'varchar',
    length: 20,
    default: SubscriptionStatus.ACTIVE,
  })
  @IsEnum(SubscriptionStatus, { message: 'Invalid subscription status' })
  status: SubscriptionStatus;

  @Column({ name: 'start_date', type: 'timestamptz', default: () => 'NOW()' })
  @IsDateString({}, { message: 'Please provide a valid start date' })
  startDate: Date;

  @Column({ name: 'end_date', type: 'timestamptz' })
  @IsNotEmpty({ message: 'End date is required' })
  @IsDateString({}, { message: 'Please provide a valid end date' })
  endDate: Date;

  @Column({
    name: 'monthly_price',
    type: 'decimal',
    precision: 8,
    scale: 2,
  })
  @IsDecimal({ decimal_digits: '0,2' }, { message: 'Monthly price must be a valid decimal' })
  @IsPositive({ message: 'Monthly price must be positive' })
  monthlyPrice: number;

  @Column({
    name: 'commission_rate',
    type: 'decimal',
    precision: 5,
    scale: 4,
  })
  @IsDecimal({ decimal_digits: '0,4' }, { message: 'Commission rate must be a valid decimal' })
  @Min(0, { message: 'Commission rate cannot be negative' })
  @Max(1, { message: 'Commission rate cannot exceed 100%' })
  commissionRate: number;

  @Column({ name: 'storage_limit', type: 'bigint' })
  @IsPositive({ message: 'Storage limit must be positive' })
  storageLimit: number;

  @Column({ name: 'features', type: 'jsonb', nullable: true })
  @IsOptional()
  features?: {
    canSellProducts: boolean;
    canJoinCompetitions: boolean;
    hasPriorityRegistration: boolean;
    portfolioHosting: boolean;
    maxProducts?: number;
    supportLevel: string;
  };

  @Column({ name: 'payment_reference', type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @Length(0, 255, { message: 'Payment reference must not exceed 255 characters' })
  paymentReference?: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => Vendor, (vendor) => vendor.subscriptions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'vendor_id' })
  vendor: Vendor;

  // Virtual properties
  get isActive(): boolean {
    return this.status === SubscriptionStatus.ACTIVE && this.endDate > new Date();
  }

  get isExpired(): boolean {
    return this.endDate <= new Date();
  }

  get isCancelled(): boolean {
    return this.status === SubscriptionStatus.CANCELLED;
  }

  get isSuspended(): boolean {
    return this.status === SubscriptionStatus.SUSPENDED;
  }

  get daysRemaining(): number {
    if (this.isExpired) return 0;
    const now = new Date();
    const diffTime = this.endDate.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  get packageDisplayName(): string {
    switch (this.packageType) {
      case SubscriptionPackage.STANDARD:
        return 'Standard Package';
      case SubscriptionPackage.PREMIUM:
        return 'Premium Package';
      case SubscriptionPackage.PLATINUM:
        return 'Platinum Package';
      default:
        return 'Unknown Package';
    }
  }

  get packageDescription(): string {
    switch (this.packageType) {
      case SubscriptionPackage.STANDARD:
        return 'Portfolio hosting only - R99/month';
      case SubscriptionPackage.PREMIUM:
        return 'Portfolio + Shop + Competitions - R250/month (20% commission)';
      case SubscriptionPackage.PLATINUM:
        return 'All features + Priority + Extra storage - R500/month (15% commission)';
      default:
        return 'Unknown package';
    }
  }

  get commissionPercentage(): string {
    return `${(this.commissionRate * 100).toFixed(1)}%`;
  }

  get storageLimitDisplay(): string {
    const gb = this.storageLimit / (1024 * 1024 * 1024);
    return `${gb}GB`;
  }

  get priceDisplay(): string {
    return `R${this.monthlyPrice.toFixed(2)}`;
  }

  get statusDisplay(): string {
    switch (this.status) {
      case SubscriptionStatus.ACTIVE:
        return this.isExpired ? 'Expired' : 'Active';
      case SubscriptionStatus.CANCELLED:
        return 'Cancelled';
      case SubscriptionStatus.SUSPENDED:
        return 'Suspended';
      case SubscriptionStatus.EXPIRED:
        return 'Expired';
      default:
        return 'Unknown';
    }
  }

  get renewalDate(): Date {
    const renewal = new Date(this.endDate);
    renewal.setMonth(renewal.getMonth() + 1);
    return renewal;
  }

  // Package-specific feature methods
  canSellProducts(): boolean {
    return this.features?.canSellProducts ||
           this.packageType === SubscriptionPackage.PREMIUM ||
           this.packageType === SubscriptionPackage.PLATINUM;
  }

  canJoinCompetitions(): boolean {
    return this.features?.canJoinCompetitions ||
           this.packageType === SubscriptionPackage.PREMIUM ||
           this.packageType === SubscriptionPackage.PLATINUM;
  }

  hasPriorityRegistration(): boolean {
    return this.features?.hasPriorityRegistration ||
           this.packageType === SubscriptionPackage.PLATINUM;
  }

  // Subscription management methods
  extend(months: number = 1): void {
    const newEndDate = new Date(this.endDate);
    newEndDate.setMonth(newEndDate.getMonth() + months);
    this.endDate = newEndDate;
  }

  cancel(): void {
    this.status = SubscriptionStatus.CANCELLED;
  }

  suspend(): void {
    this.status = SubscriptionStatus.SUSPENDED;
  }

  reactivate(): void {
    if (this.isExpired) {
      this.extend(1); // Extend by 1 month
    }
    this.status = SubscriptionStatus.ACTIVE;
  }

  // Static factory methods
  static createStandardSubscription(vendorId: string): Partial<Subscription> {
    // 1GB storage with no selling or competitions
    return {
      vendorId,
      packageType: SubscriptionPackage.STANDARD,
      monthlyPrice: 99.00,
      commissionRate: 0.0000,
      storageLimit: 1073741824,
      features: {
        canSellProducts: false,
        canJoinCompetitions: false,
        hasPriorityRegistration: false,
        portfolioHosting: true,
        supportLevel: 'basic'
      }
    };
  }

  static createPremiumSubscription(vendorId: string): Partial<Subscription> {
    // 2GB storage with 20% commission
    return {
      vendorId,
      packageType: SubscriptionPackage.PREMIUM,
      monthlyPrice: 250.00,
      commissionRate: 0.2000,
      storageLimit: 2147483648,
      features: {
        canSellProducts: true,
        canJoinCompetitions: true,
        hasPriorityRegistration: false,
        portfolioHosting: true,
        supportLevel: 'standard'
      }
    };
  }

  static createPlatinumSubscription(vendorId: string): Partial<Subscription> {
    // 5GB storage with 15% commission
    return {
      vendorId,
      packageType: SubscriptionPackage.PLATINUM,
      monthlyPrice: 500.00,
      commissionRate: 0.1500,
      storageLimit: 5368709120,
      features: {
        canSellProducts: true,
        canJoinCompetitions: true,
        hasPriorityRegistration: true,
        portfolioHosting: true,
        supportLevel: 'priority'
      }
    };
  }
}