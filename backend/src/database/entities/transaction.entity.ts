import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  OneToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import {
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsDecimal,
  Length,
  IsUUID,
} from 'class-validator';
import { Wallet } from './wallet.entity';

// Transaction type enum
export enum TransactionType {
  DEPOSIT = 'deposit',
  WITHDRAWAL = 'withdrawal',
  PAYMENT = 'payment',
  COMMISSION = 'commission',
  SUBSCRIPTION = 'subscription',
}

// Transaction status enum
export enum TransactionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

@Entity('transactions')
@Index('idx_transactions_wallet_id', ['walletId'])
@Index('idx_transactions_type', ['type'])
@Index('idx_transactions_status', ['status'])
@Index('idx_transactions_created_at', ['createdAt'])
@Index('idx_transactions_reference', ['referenceId', 'referenceType'])
export class Transaction {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  id: string;

  @Column({ name: 'wallet_id', type: 'uuid' })
  @IsUUID()
  @IsNotEmpty({ message: 'Wallet ID is required' })
  walletId: string;

  @Column({
    name: 'type',
    type: 'enum',
    enum: TransactionType,
  })
  @IsEnum(TransactionType, { message: 'Invalid transaction type' })
  type: TransactionType;

  @Column({
    name: 'amount',
    type: 'decimal',
    precision: 18,
    scale: 8,
  })
  @IsDecimal({ decimal_digits: '0,8' }, { message: 'Amount must be a valid decimal' })
  amount: number;

  @Column({ name: 'currency', type: 'varchar', length: 3 })
  @IsNotEmpty({ message: 'Currency is required' })
  @Length(3, 3, { message: 'Currency must be exactly 3 characters' })
  currency: string;

  @Column({ name: 'reference_id', type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  referenceId?: string;

  @Column({ name: 'reference_type', type: 'varchar', length: 50, nullable: true })
  @IsOptional()
  @Length(0, 50, { message: 'Reference type must not exceed 50 characters' })
  referenceType?: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  @IsOptional()
  description?: string;

  @Column({ name: 'blockchain_hash', type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @Length(0, 255, { message: 'Blockchain hash must not exceed 255 characters' })
  blockchainHash?: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
  })
  @IsEnum(TransactionStatus, { message: 'Invalid transaction status' })
  status: TransactionStatus;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @Column({ name: 'confirmed_at', type: 'timestamptz', nullable: true })
  @IsOptional()
  confirmedAt?: Date;

  // Relationships
  @ManyToOne(() => Wallet, (wallet) => wallet.transactions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'wallet_id' })
  wallet: Wallet;

  @OneToOne('BlockchainRecord', 'transaction', {
    cascade: true,
  })
  blockchainRecord?: any;

  // Virtual properties
  get isPending(): boolean {
    return this.status === TransactionStatus.PENDING;
  }

  get isProcessing(): boolean {
    return this.status === TransactionStatus.PROCESSING;
  }

  get isCompleted(): boolean {
    return this.status === TransactionStatus.COMPLETED;
  }

  get isFailed(): boolean {
    return this.status === TransactionStatus.FAILED;
  }

  get isDeposit(): boolean {
    return this.type === TransactionType.DEPOSIT;
  }

  get isWithdrawal(): boolean {
    return this.type === TransactionType.WITHDRAWAL;
  }

  get isPayment(): boolean {
    return this.type === TransactionType.PAYMENT;
  }

  get isCommission(): boolean {
    return this.type === TransactionType.COMMISSION;
  }

  get isSubscription(): boolean {
    return this.type === TransactionType.SUBSCRIPTION;
  }

  get statusDisplay(): string {
    switch (this.status) {
      case TransactionStatus.PENDING:
        return 'Pending';
      case TransactionStatus.PROCESSING:
        return 'Processing';
      case TransactionStatus.COMPLETED:
        return 'Completed';
      case TransactionStatus.FAILED:
        return 'Failed';
      default:
        return 'Unknown';
    }
  }

  get typeDisplay(): string {
    switch (this.type) {
      case TransactionType.DEPOSIT:
        return 'Deposit';
      case TransactionType.WITHDRAWAL:
        return 'Withdrawal';
      case TransactionType.PAYMENT:
        return 'Payment';
      case TransactionType.COMMISSION:
        return 'Commission';
      case TransactionType.SUBSCRIPTION:
        return 'Subscription Fee';
      default:
        return 'Unknown';
    }
  }

  get amountDisplay(): string {
    const sign = this.isWithdrawal ? '-' : '+';
    if (this.currency === 'ZAR') {
      return `${sign}R${Math.abs(this.amount).toFixed(2)}`;
    }
    return `${sign}${Math.abs(this.amount).toFixed(8)} ${this.currency}`;
  }

  get amountFormatted(): string {
    if (this.currency === 'ZAR') {
      return `R${Math.abs(this.amount).toFixed(2)}`;
    }
    return `${Math.abs(this.amount).toFixed(8)} ${this.currency}`;
  }

  get isBlockchainTransaction(): boolean {
    return !!this.blockchainHash;
  }

  get hasBlockchainRecord(): boolean {
    return !!this.blockchainRecord;
  }

  get processingTimeMinutes(): number | undefined {
    if (!this.confirmedAt) return undefined;
    const diffMs = this.confirmedAt.getTime() - this.createdAt.getTime();
    return Math.round(diffMs / (1000 * 60));
  }

  get canBeRetried(): boolean {
    return this.isFailed && !this.isBlockchainTransaction;
  }

  // Business logic methods
  markAsProcessing(): void {
    if (this.isPending) {
      this.status = TransactionStatus.PROCESSING;
    }
  }

  markAsCompleted(): void {
    if (this.isProcessing || this.isPending) {
      this.status = TransactionStatus.COMPLETED;
      this.confirmedAt = new Date();
    }
  }

  markAsFailed(): void {
    if (this.isProcessing || this.isPending) {
      this.status = TransactionStatus.FAILED;
    }
  }

  retry(): void {
    if (this.canBeRetried) {
      this.status = TransactionStatus.PENDING;
    }
  }

  setBlockchainHash(hash: string): void {
    this.blockchainHash = hash;
  }

  // Static methods
  static createDeposit(walletId: string, amount: number, currency: string, description?: string): Partial<Transaction> {
    return {
      walletId,
      type: TransactionType.DEPOSIT,
      amount: Math.abs(amount),
      currency: currency.toUpperCase(),
      description: description || 'Wallet deposit',
      status: TransactionStatus.PENDING,
    };
  }

  static createWithdrawal(walletId: string, amount: number, currency: string, description?: string): Partial<Transaction> {
    return {
      walletId,
      type: TransactionType.WITHDRAWAL,
      amount: -Math.abs(amount),
      currency: currency.toUpperCase(),
      description: description || 'Wallet withdrawal',
      status: TransactionStatus.PENDING,
    };
  }

  static createPayment(walletId: string, amount: number, currency: string, referenceId: string, referenceType: string): Partial<Transaction> {
    return {
      walletId,
      type: TransactionType.PAYMENT,
      amount: -Math.abs(amount),
      currency: currency.toUpperCase(),
      referenceId,
      referenceType,
      description: `Payment for ${referenceType}`,
      status: TransactionStatus.PENDING,
    };
  }

  static createCommission(walletId: string, amount: number, currency: string, referenceId: string): Partial<Transaction> {
    return {
      walletId,
      type: TransactionType.COMMISSION,
      amount: Math.abs(amount),
      currency: currency.toUpperCase(),
      referenceId,
      referenceType: 'order',
      description: 'Vendor commission from artwork sale (after platform commission)',
      status: TransactionStatus.COMPLETED,
    };
  }

  static createSubscriptionPayment(walletId: string, amount: number, currency: string, subscriptionId: string): Partial<Transaction> {
    return {
      walletId,
      type: TransactionType.SUBSCRIPTION,
      amount: -Math.abs(amount),
      currency: currency.toUpperCase(),
      referenceId: subscriptionId,
      referenceType: 'subscription',
      description: 'Monthly subscription fee payment',
      status: TransactionStatus.PENDING,
    };
  }
}