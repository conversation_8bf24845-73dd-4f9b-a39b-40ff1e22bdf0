import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  Index,
  JoinColumn,
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsBoolean, IsEnum, Length, Matches, IsISO31661Alpha2, IsUUID } from 'class-validator';
import { User } from './user.entity';

// Address type enum
export enum AddressType {
    HOME = 'home',
    WORK = 'work/office',
    BOTH = 'both',
}

// South African provinces enum
export enum SouthAfricanProvince {
  EASTERN_CAPE = 'Eastern Cape',
  FREE_STATE = 'Free State',
  GAUTENG = 'Gauteng',
  KWA_ZULU_NATAL = 'KwaZulu-Natal',
  LIMPOPO = 'Limpopo',
  MPUMALANGA = 'Mpumalanga',
  NORTHERN_CAPE = 'Northern Cape',
  NORTH_WEST = 'North West',
  WESTERN_CAPE = 'Western Cape',
}

@Entity('user_addresses')
@Index('idx_user_addresses_user_id', ['userId'])
@Index('idx_user_addresses_type', ['type'])
@Index('idx_user_addresses_default', ['isDefault'], { where: 'is_default = TRUE' })

export class UserAddress {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  id: string;

  @Column({ name: 'user_id', type: 'uuid' })
  @IsUUID()
  @IsNotEmpty({ message: 'User ID is required' })
  userId: string;

  @Column({type: 'enum', enum: AddressType})
  @IsEnum(AddressType, { message: 'Invalid address type' })
  type: AddressType;

  @Column({ name: 'street_address', type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Street address is required' })
  @Length(5, 255, { message: 'Street address must be between 5 and 255 characters' })
  streetAddress: string;

  @Column({ name: 'suburb', type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @Length(2, 100, { message: 'Suburb must be between 2 and 100 characters' })
  suburb?: string;

  @Column({ name: 'city', type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'City is required' })
  @Length(2, 100, { message: 'City must be between 2 and 100 characters' })
  city: string;

  @Column({ name: 'province', type: 'varchar', length: 50 })
  @IsNotEmpty({ message: 'Province is required' })
  @IsEnum(SouthAfricanProvince, { message: 'Invalid South African province' })
  province: SouthAfricanProvince;

  @Column({ name: 'postal_code', type: 'varchar', length: 10 })
  @IsNotEmpty({ message: 'Postal code is required' })
  @Matches(/^[0-9]{4}$/, { message: 'Please provide a valid South African postal code' })
  postalCode: string;

  @Column({ name: 'country', type: 'varchar', length: 2, default: 'ZA' })
  @IsISO31661Alpha2()
  @IsOptional()
  country?: string = 'ZA';

  @Column({ name: 'is_default', type: 'boolean', default: false })
  @IsBoolean()
  isDefault: boolean;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => User, (user) => user.addresses, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'user_id' })
  user: User;

  // Virtual properties
  get fullAddress(): string {
    const parts = [
        this.streetAddress,
        this.suburb,
        this.city,
        this.province,
        this.postalCode,
        this.country,
    ].filter(Boolean)

    return parts.join(', ');
  }

    get isHomeAddress(): boolean {
        return this.type === AddressType.HOME || this.type === AddressType.BOTH;
    }

    get isWorkAddress(): boolean {
        return this.type === AddressType.WORK || this.type === AddressType.BOTH;
    }

    get formattedAddress(): string {
        return `${this.streetAddress}${this.suburb ? ', ' + this.suburb : ''}\n${this.city}, ${this.province}\n${this.postalCode}, ${this.country}`;
    }
}