import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
  OneToOne,
  Index,
} from 'typeorm';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsDateString,
  IsBoolean,
  IsEnum,
  IsInt,
  Length,
  IsUUID,
  Min,
} from 'class-validator';
import { UserAddress } from './user-address.entity';
import { Vendor } from './vendor.entity';
import { Order } from './order.entity';
import { Wallet } from './wallet.entity';

// User role enum
export enum UserRole {
  BUYER = 'buyer',
  VENDOR = 'vendor',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
}

@Entity('users')
@Index('idx_users_email', ['email'], { unique: true })
@Index('idx_users_role', ['role'])
@Index('idx_users_active', ['isActive'])
@Index('idx_users_login_attempts', ['loginAttempts'])
@Index('idx_users_locked_until', ['lockedUntil'])
@Index('idx_users_password_reset_token', ['passwordResetToken'])
@Index('idx_users_active_email', ['isActive', 'email'])
export class User {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  id: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @Column({ name: 'password_hash', type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Password is required' })
  @Length(8, 255, { message: 'Password must be at least 8 characters long' })
  passwordHash: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.BUYER,
  })
  @IsEnum(UserRole, { message: 'Invalid user role' })
  role: UserRole;

  @Column({ name: 'first_name', type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @Length(1, 100, { message: 'First name must be between 1 and 100 characters' })
  firstName?: string;

  @Column({ name: 'last_name', type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @Length(1, 100, { message: 'Last name must be between 1 and 100 characters' })
  lastName?: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  @IsOptional()
  @IsPhoneNumber('ZA', { message: 'Please provide a valid South African phone number' })
  phone?: string;

  @Column({ name: 'date_of_birth', type: 'date', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'Please provide a valid date of birth' })
  dateOfBirth?: Date;

  @Column({ name: 'email_verified', type: 'boolean', default: false })
  @IsBoolean()
  emailVerified: boolean;

  @Column({ name: 'email_verified_at', type: 'timestamptz', nullable: true })
  @IsOptional()
  emailVerifiedAt?: Date;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  @IsBoolean()
  isActive: boolean;

  @Column({ name: 'last_login_at', type: 'timestamptz', nullable: true })
  @IsOptional()
  lastLoginAt?: Date;

  @Column({ name: 'password_reset_token', type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @Length(1, 255, { message: 'Password reset token must be between 1 and 255 characters' })
  passwordResetToken?: string;

  @Column({ name: 'password_reset_expires', type: 'timestamptz', nullable: true })
  @IsOptional()
  passwordResetExpires?: Date;

  @Column({ name: 'login_attempts', type: 'integer', default: 0 })
  @IsInt({ message: 'Login attempts must be an integer' })
  @Min(0, { message: 'Login attempts cannot be negative' })
  loginAttempts: number;

  @Column({ name: 'locked_until', type: 'timestamptz', nullable: true })
  @IsOptional()
  lockedUntil?: Date;

  @Column({ name: 'two_factor_secret', type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @Length(1, 255, { message: 'Two factor secret must be between 1 and 255 characters' })
  twoFactorSecret?: string;

  @Column({ name: 'two_factor_enabled', type: 'boolean', default: false })
  @IsBoolean()
  twoFactorEnabled: boolean;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz', nullable: true })
  deletedAt?: Date;

  // Relationships
  @OneToMany(() => UserAddress, (address) => address.user, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  addresses: UserAddress[];

  @OneToOne(() => Vendor, (vendor) => vendor.user, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  vendor?: Vendor;

  @OneToMany(() => Order, (order) => order.user)
  orders: Order[];

  @OneToMany(() => Wallet, (wallet) => wallet.user, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  wallets: Wallet[];

  // Virtual properties
  get fullName(): string {
    if (this.firstName && this.lastName) {
      return `${this.firstName} ${this.lastName}`;
    }
    return this.firstName || this.lastName || '';
  }

  get isVendor(): boolean {
    return this.role === UserRole.VENDOR;
  }

  get isAdmin(): boolean {
    return this.role === UserRole.ADMIN || this.role === UserRole.SUPER_ADMIN;
  }

  get isSuperAdmin(): boolean {
    return this.role === UserRole.SUPER_ADMIN;
  }

  get isAccountLocked(): boolean {
    return this.lockedUntil ? this.lockedUntil > new Date() : false;
  }

  get canAttemptLogin(): boolean {
    return !this.isAccountLocked && this.isActive;
  }

  get hasPasswordResetToken(): boolean {
    return !!(this.passwordResetToken && this.passwordResetExpires && this.passwordResetExpires > new Date());
  }
}