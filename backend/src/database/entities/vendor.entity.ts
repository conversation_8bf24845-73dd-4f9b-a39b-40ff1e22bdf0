import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import {
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsEnum,
  IsDecimal,
  IsDateString,
  Length,
  IsUUID,
  Min,
  Max,
} from 'class-validator';
import { User } from './user.entity';
import { Product } from './product.entity';
import { Subscription, SubscriptionPackage } from './subscription.entity';
import { OrderItem } from './order-item.entity';

// Vendor verification status enum
export enum VendorStatus {
  PENDING = 'pending',
  VERIFIED = 'verified',
  REJECTED = 'rejected',
  SUSPENDED = 'suspended',
}

@Entity('vendors')
@Index('idx_vendors_user_id', ['userId'], { unique: true })
@Index('idx_vendors_verification_status', ['verificationStatus'])
@Index('idx_vendors_business_name', ['businessName'])
@Index('idx_vendors_active', ['isActive'])
@Index('idx_vendors_package', ['currentPackage'])
export class Vendor {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  id: string;

  @Column({ name: 'user_id', type: 'uuid', unique: true })
  @IsUUID()
  @IsNotEmpty({ message: 'User ID is required' })
  userId: string;

  @Column({ name: 'business_name', type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Business name is required' })
  @Length(2, 255, { message: 'Business name must be between 2 and 255 characters' })
  businessName: string;

  @Column({ name: 'artist_bio', type: 'text', nullable: true })
  @IsOptional()
  @Length(0, 2000, { message: 'Artist bio must not exceed 2000 characters' })
  artistBio?: string;

  @Column({
    name: 'verification_status',
    type: 'enum',
    enum: VendorStatus,
    default: VendorStatus.PENDING,
  })
  @IsEnum(VendorStatus, { message: 'Invalid verification status' })
  verificationStatus: VendorStatus;

  @Column({ name: 'verification_date', type: 'timestamptz', nullable: true })
  @IsOptional()
  verificationDate?: Date;

  @Column({ name: 'verification_notes', type: 'text', nullable: true })
  @IsOptional()
  verificationNotes?: string;

  @Column({ name: 'store_logo_url', type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  @Length(0, 500, { message: 'Store logo URL must not exceed 500 characters' })
  storeLogoUrl?: string;

  @Column({ name: 'store_banner_url', type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  @Length(0, 500, { message: 'Store banner URL must not exceed 500 characters' })
  storeBannerUrl?: string;

  @Column({ name: 'store_layout', type: 'varchar', length: 50, default: 'grid' })
  @IsOptional()
  @Length(0, 50, { message: 'Store layout must not exceed 50 characters' })
  storeLayout: string;

  @Column({ name: 'store_color_scheme', type: 'jsonb', nullable: true })
  @IsOptional()
  storeColorScheme?: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };

  @Column({ name: 'contact_email', type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @Length(0, 255, { message: 'Contact email must not exceed 255 characters' })
  contactEmail?: string;

  @Column({
    name: 'current_package',
    type: 'enum',
    enum: SubscriptionPackage,
    default: SubscriptionPackage.STANDARD,
  })
  @IsEnum(SubscriptionPackage, { message: 'Invalid subscription package' })
  currentPackage: SubscriptionPackage;

  @Column({
    name: 'commission_rate',
    type: 'decimal',
    precision: 5,
    scale: 4,
    default: 0.0000,
  })
  @IsDecimal({ decimal_digits: '0,4' }, { message: 'Commission rate must be a valid decimal' })
  @Min(0, { message: 'Commission rate cannot be negative' })
  @Max(1, { message: 'Commission rate cannot exceed 100%' })
  commissionRate: number;

  @Column({ name: 'portfolio_storage_used', type: 'bigint', default: 0 })
  portfolioStorageUsed: number;

  @Column({ name: 'portfolio_storage_limit', type: 'bigint', default: 1073741824 })
  portfolioStorageLimit: number;

  @Column({ name: 'can_sell_products', type: 'boolean', default: false })
  @IsBoolean()
  canSellProducts: boolean;

  @Column({ name: 'can_join_competitions', type: 'boolean', default: false })
  @IsBoolean()
  canJoinCompetitions: boolean;

  @Column({ name: 'has_priority_registration', type: 'boolean', default: false })
  @IsBoolean()
  hasPriorityRegistration: boolean;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  @IsBoolean()
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  // Relationships
  @OneToOne(() => User, (user) => user.vendor, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => Product, (product) => product.vendor)
  products: Product[];

  @OneToMany(() => Subscription, (subscription) => subscription.vendor)
  subscriptions: Subscription[];

  @OneToMany(() => OrderItem, (orderItem) => orderItem.vendor)
  orderItems: OrderItem[];

  // Virtual properties
  get isVerified(): boolean {
    return this.verificationStatus === VendorStatus.VERIFIED;
  }

  get canSell(): boolean {
    return this.isVerified && this.isActive && this.canSellProducts && this.hasActiveSubscription;
  }

  get hasActiveSubscription(): boolean {
    return this.subscriptions?.some(sub => sub.isActive) || false;
  }

  get packageDisplayName(): string {
    switch (this.currentPackage) {
      case SubscriptionPackage.STANDARD:
        return 'Standard Package (R99/month)';
      case SubscriptionPackage.PREMIUM:
        return 'Premium Package (R250/month)';
      case SubscriptionPackage.PLATINUM:
        return 'Platinum Package (R500/month)';
      default:
        return 'Unknown Package';
    }
  }

  get commissionPercentage(): string {
    return `${(this.commissionRate * 100).toFixed(1)}%`;
  }

  get storageUsedPercentage(): number {
    return (this.portfolioStorageUsed / this.portfolioStorageLimit) * 100;
  }

  get storageUsedDisplay(): string {
    const usedMB = Math.round(this.portfolioStorageUsed / (1024 * 1024));
    const limitMB = Math.round(this.portfolioStorageLimit / (1024 * 1024));
    return `${usedMB}MB / ${limitMB}MB`;
  }

  // Package-specific methods
  updatePackagePermissions(): void {
    switch (this.currentPackage) {
        // 1GB storage with no selling or competitions
      case SubscriptionPackage.STANDARD:
        this.canSellProducts = false;
        this.canJoinCompetitions = false;
        this.hasPriorityRegistration = false;
        this.commissionRate = 0.0000;
        this.portfolioStorageLimit = 1073741824;
        break;

        // 2GB storage with 20% commission
      case SubscriptionPackage.PREMIUM:
        this.canSellProducts = true;
        this.canJoinCompetitions = true;
        this.hasPriorityRegistration = false;
        this.commissionRate = 0.2000;
        this.portfolioStorageLimit = 2147483648;
        break;

        // 5GB storage with 15% commission
      case SubscriptionPackage.PLATINUM:
        this.canSellProducts = true;
        this.canJoinCompetitions = true;
        this.hasPriorityRegistration = true;
        this.commissionRate = 0.1500;
        this.portfolioStorageLimit = 5368709120;
        break;
    }
  }

  // Store customization methods
  updateStoreLogo(logoUrl: string): boolean {
    if (!this.canCustomizeStore) return false;
    this.storeLogoUrl = logoUrl;
    return true;
  }

  updateStoreBanner(bannerUrl: string): boolean {
    if (!this.canCustomizeStore) return false;
    this.storeBannerUrl = bannerUrl;
    return true;
  }

  updateStoreLayout(layout: string): boolean {
    if (!this.canCustomizeStore) return false;
    if (this.availableLayouts.includes(layout)) {
      this.storeLayout = layout;
      return true;
    }
    return false;
  }

  updateStoreColors(colorScheme: object): boolean {
    if (!this.canCustomizeStore) return false;
    this.storeColorScheme = colorScheme as {
      primary: string;
      secondary: string;
      accent: string;
      background: string;
      text: string;
    };
    return true;
  }

  resetStoreCustomization(): void {
    if (this.canCustomizeStore) {
      this.storeLogoUrl = null;
      this.storeBannerUrl = null;
      this.storeLayout = 'grid';
      this.storeColorScheme = null;
    }
  }

  getCustomizationRestrictions(): string[] {
    const restrictions: string[] = [];

    if (!this.canCustomizeStore) {
      restrictions.push('Store customization requires Premium or Platinum package');
      restrictions.push('Upgrade your subscription to customize your store appearance');
    }

    return restrictions;
  }

  get storeUrl(): string {
    return `/store/${this.businessName.toLowerCase().replace(/\s+/g, '-')}`;
  }

  get canCustomizeStore(): boolean {
    return this.currentPackage === SubscriptionPackage.PREMIUM ||
           this.currentPackage === SubscriptionPackage.PLATINUM;
  }

  get hasCustomBranding(): boolean {
    return this.canCustomizeStore && !!(this.storeLogoUrl || this.storeBannerUrl);
  }

  get hasCustomColors(): boolean {
    return this.canCustomizeStore && !!(this.storeColorScheme && Object.keys(this.storeColorScheme).length > 0);
  }

  get defaultColorScheme(): object {
    return {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#f59e0b',
      background: '#ffffff',
      text: '#1f2937'
    };
  }

  get effectiveColorScheme(): object {
    return this.storeColorScheme || this.defaultColorScheme;
  }

  get availableLayouts(): string[] {
    return ['grid', 'list', 'masonry', 'carousel'];
  }

  get storeCustomization(): object {
    if (!this.canCustomizeStore) {
      return {
        layout: 'grid',
        colorScheme: this.defaultColorScheme,
        logoUrl: null,
        bannerUrl: null,
        hasCustomBranding: false,
        canCustomize: false,
        packageRestriction: 'Store customization requires Premium or Platinum package'
      };
    }

    return {
      layout: this.storeLayout,
      colorScheme: this.effectiveColorScheme,
      logoUrl: this.storeLogoUrl,
      bannerUrl: this.storeBannerUrl,
      hasCustomBranding: this.hasCustomBranding,
      canCustomize: true,
      packageRestriction: null
    };
  }
}