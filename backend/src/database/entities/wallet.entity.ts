import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import {
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsDecimal,
  Length,
  IsUUID,
  Min,
} from 'class-validator';
import { User } from './user.entity';

@Entity('wallets')
@Index('idx_wallets_user_id', ['userId'])
@Index('idx_wallets_currency', ['currency'])
@Index('idx_wallets_active', ['isActive'])
@Index('idx_wallets_user_currency', ['userId', 'currency'], { unique: true })
export class Wallet {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  id!: string;

  @Column({ name: 'user_id', type: 'uuid' })
  @IsUUID()
  @IsNotEmpty({ message: 'User ID is required' })
  userId!: string;

  @Column({ name: 'currency', type: 'varchar', length: 3, default: 'ZAR' })
  @IsNotEmpty({ message: 'Currency is required' })
  @Length(3, 3, { message: 'Currency must be exactly 3 characters' })
  currency!: string;

  @Column({
    name: 'balance',
    type: 'decimal',
    precision: 18,
    scale: 8,
    default: 0,
  })
  @IsDecimal({ decimal_digits: '0,8' }, { message: 'Balance must be a valid decimal' })
  @Min(0, { message: 'Balance cannot be negative' })
  balance!: number;

  @Column({ name: 'payment_gateway_reference', type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @Length(0, 255, { message: 'Payment gateway reference must not exceed 255 characters' })
  paymentGatewayReference?: string;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  @IsBoolean()
  isActive!: boolean;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt!: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt!: Date;

  // Relationships
  @ManyToOne(() => User, (user) => user.wallets, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'user_id' })
  user!: User;

  @OneToMany('Transaction', 'wallet')
  transactions!: any[];

  // Virtual properties
  get balanceDisplay(): string {
    return `${this.balance.toFixed(8)} ${this.currency}`;
  }

  get balanceFormatted(): string {
    if (this.currency === 'ZAR') {
      return `R${this.balance.toFixed(2)}`;
    }
    return `${this.balance.toFixed(8)} ${this.currency}`;
  }

  get isZARWallet(): boolean {
    return this.currency === 'ZAR';
  }

  get hasPaymentGatewayReference(): boolean {
    return !!this.paymentGatewayReference;
  }

  get transactionCount(): number {
    return this.transactions?.length || 0;
  }

  get pendingTransactionCount(): number {
    return this.transactions?.filter(tx => tx.isPending).length || 0;
  }

  get totalDeposits(): number {
    return this.transactions?.filter(tx => tx.isDeposit && tx.isCompleted)
      .reduce((sum, tx) => sum + tx.amount, 0) || 0;
  }

  get totalWithdrawals(): number {
    return this.transactions?.filter(tx => tx.isWithdrawal && tx.isCompleted)
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0) || 0;
  }

  get canWithdraw(): boolean {
    return this.isActive && this.balance > 0;
  }

  get canDeposit(): boolean {
    return this.isActive;
  }

  // Business logic methods
  credit(amount: number): void {
    if (amount > 0) {
      this.balance += amount;
    }
  }

  debit(amount: number): boolean {
    if (amount > 0 && this.balance >= amount) {
      this.balance -= amount;
      return true;
    }
    return false;
  }

  activate(): void {
    this.isActive = true;
  }

  deactivate(): void {
    this.isActive = false;
  }

  updatePaymentGatewayReference(reference: string): void {
    this.paymentGatewayReference = reference;
  }

  // Static methods
  static createZARWallet(userId: string): Partial<Wallet> {
    return {
      userId,
      currency: 'ZAR',
      balance: 0,
      isActive: true,
    };
  }

  static getSupportedCurrencies(): string[] {
    return ['ZAR']; // Only ZAR supported for now
  }

  static isSupportedCurrency(currency: string): boolean {
    return currency.toUpperCase() === 'ZAR';
  }
}