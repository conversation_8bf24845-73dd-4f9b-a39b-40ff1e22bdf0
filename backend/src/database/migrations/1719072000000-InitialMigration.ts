import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialMigration1719072000000 implements MigrationInterface {
    name = 'InitialMigration1719072000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Enable UUID extension
        await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);
        
        // Create enum types
        await queryRunner.query(`CREATE TYPE "public"."user_role_enum" AS ENUM('admin', 'vendor', 'customer')`);
        await queryRunner.query(`CREATE TYPE "public"."order_status_enum" AS ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled')`);
        await queryRunner.query(`CREATE TYPE "public"."payment_status_enum" AS ENUM('pending', 'completed', 'failed', 'refunded')`);
        await queryRunner.query(`CREATE TYPE "public"."transaction_type_enum" AS ENUM('deposit', 'withdrawal', 'payment', 'refund')`);
        await queryRunner.query(`CREATE TYPE "public"."transaction_status_enum" AS ENUM('pending', 'completed', 'failed')`);
        await queryRunner.query(`CREATE TYPE "public"."subscription_status_enum" AS ENUM('active', 'inactive', 'cancelled', 'expired')`);
        
        console.log('✅ Database migration completed successfully!');
        console.log('📊 Created enum types for user roles, order status, payment status, etc.');
        console.log('🔧 Enabled UUID extension');
        console.log('');
        console.log('🎯 Next steps:');
        console.log('1. Run: npm run seed (to add initial data)');
        console.log('2. Start your application: npm run start:dev');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop enum types
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."subscription_status_enum"`);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."transaction_status_enum"`);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."transaction_type_enum"`);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."payment_status_enum"`);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."order_status_enum"`);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."user_role_enum"`);
    }
}
