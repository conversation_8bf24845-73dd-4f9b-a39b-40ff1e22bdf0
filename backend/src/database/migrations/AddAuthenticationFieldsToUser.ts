import { MigrationInterface, QueryRunner, TableColumn, TableIndex } from 'typeorm';

export class AddAuthenticationFieldsToUser1703000000000 implements MigrationInterface {
  name = 'AddAuthenticationFieldsToUser1703000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new columns for authentication features
    await queryRunner.addColumns('users', [
      new TableColumn({
        name: 'password_reset_token',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'password_reset_expires',
        type: 'timestamptz',
        isNullable: true,
      }),
      new TableColumn({
        name: 'login_attempts',
        type: 'integer',
        default: 0,
      }),
      new TableColumn({
        name: 'locked_until',
        type: 'timestamptz',
        isNullable: true,
      }),
      new TableColumn({
        name: 'two_factor_secret',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'two_factor_enabled',
        type: 'boolean',
        default: false,
      }),
    ]);

    // Create indexes for performance optimization
    await queryRunner.createIndex('users', new TableIndex({
      name: 'idx_users_login_attempts',
      columnNames: ['login_attempts']
    }));

    await queryRunner.createIndex('users', new TableIndex({
      name: 'idx_users_locked_until',
      columnNames: ['locked_until']
    }));

    await queryRunner.createIndex('users', new TableIndex({
      name: 'idx_users_password_reset_token',
      columnNames: ['password_reset_token']
    }));

    // Create composite index for active users by email
    await queryRunner.createIndex('users', new TableIndex({
      name: 'idx_users_active_email',
      columnNames: ['is_active', 'email']
    }));
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.dropIndex('users', 'idx_users_active_email');
    await queryRunner.dropIndex('users', 'idx_users_password_reset_token');
    await queryRunner.dropIndex('users', 'idx_users_locked_until');
    await queryRunner.dropIndex('users', 'idx_users_login_attempts');

    // Drop columns
    await queryRunner.dropColumns('users', [
      'password_reset_token',
      'password_reset_expires',
      'login_attempts',
      'locked_until',
      'two_factor_secret',
      'two_factor_enabled',
    ]);
  }
}
