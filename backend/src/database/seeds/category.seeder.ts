import { DataSource } from 'typeorm';
import { Category } from '../entities/category.entity';

export class CategorySeeder {
  async run(dataSource: DataSource): Promise<void> {
    const categoryRepository = dataSource.getRepository(Category);

    // Check if categories already exist
    const existingCategories = await categoryRepository.count();
    if (existingCategories > 0) {
      console.log('📂 Categories already exist, skipping category seeder');
      return;
    }

    console.log('📂 Creating initial categories...');

    const categories = [
      {
        name: 'Electronics',
        description: 'Electronic devices and gadgets',
        slug: 'electronics',
        isActive: true,
        sortOrder: 1,
      },
      {
        name: 'Clothing',
        description: 'Fashion and apparel',
        slug: 'clothing',
        isActive: true,
        sortOrder: 2,
      },
      {
        name: 'Books',
        description: 'Books and educational materials',
        slug: 'books',
        isActive: true,
        sortOrder: 3,
      },
      {
        name: 'Home & Garden',
        description: 'Home improvement and gardening supplies',
        slug: 'home-garden',
        isActive: true,
        sortOrder: 4,
      },
      {
        name: 'Sports & Outdoors',
        description: 'Sports equipment and outdoor gear',
        slug: 'sports-outdoors',
        isActive: true,
        sortOrder: 5,
      },
    ];

    // Use raw insert to avoid tree structure issues
    for (const categoryData of categories) {
      await categoryRepository.query(`
        INSERT INTO categories (name, slug, description, is_active, sort_order)
        VALUES ($1, $2, $3, $4, $5)
      `, [
        categoryData.name,
        categoryData.slug,
        categoryData.description,
        categoryData.isActive,
        categoryData.sortOrder
      ]);
      console.log(`✅ Created category: ${categoryData.name}`);
    }

    console.log('📂 Category seeding completed');
  }
}
