import AppDataSource from '../data-source';
import { UserSeeder } from './user.seeder';
import { CategorySeeder } from './category.seeder';

async function runSeeds() {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Initialize data source
    await AppDataSource.initialize();
    console.log('✅ Database connection established');
    
    // Run seeders in order
    const userSeeder = new UserSeeder();
    await userSeeder.run(AppDataSource);
    console.log('✅ User seeder completed');
    
    const categorySeeder = new CategorySeeder();
    await categorySeeder.run(AppDataSource);
    console.log('✅ Category seeder completed');
    
    console.log('🎉 All seeds completed successfully!');
    
  } catch (error) {
    console.error('❌ Error running seeds:', error);
    process.exit(1);
  } finally {
    // Close connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run seeds if this file is executed directly
if (require.main === module) {
  runSeeds();
}

export { runSeeds };
