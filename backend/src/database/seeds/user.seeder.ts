import { DataSource } from 'typeorm';
import { User } from '../entities/user.entity';
import * as bcrypt from 'bcryptjs';

export class UserSeeder {
  async run(dataSource: DataSource): Promise<void> {
    const userRepository = dataSource.getRepository(User);
    
    // Check if users already exist
    const existingUsers = await userRepository.count();
    if (existingUsers > 0) {
      console.log('👥 Users already exist, skipping user seeder');
      return;
    }
    
    console.log('👥 Creating initial users...');
    
    // Hash password for demo users
    const hashedPassword = await bcrypt.hash('password123', 10);
    
    const users = [
      {
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin' as any,
        emailVerified: true,
        isActive: true,
      },
      {
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        firstName: 'Vendor',
        lastName: 'User',
        role: 'vendor' as any,
        emailVerified: true,
        isActive: true,
      },
      {
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        firstName: 'Customer',
        lastName: 'User',
        role: 'buyer' as any,
        emailVerified: true,
        isActive: true,
      },
    ];
    
    for (const userData of users) {
      const user = userRepository.create(userData);
      await userRepository.save(user);
      console.log(`✅ Created user: ${userData.email}`);
    }
    
    console.log('👥 User seeding completed');
  }
}
