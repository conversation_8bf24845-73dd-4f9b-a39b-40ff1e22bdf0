# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type User {
  id: ID!
  email: String!
  role: String!
  name: String
  avatar: String
  createdAt: DateTime!
  updatedAt: DateTime!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type Product {
  id: ID!
  name: String!
  description: String
  price: Float!
  currency: String!
  image: String
  vendorId: String!
  vendorName: String!
  category: String!
  inStock: Boolean!
  rating: Float
  createdAt: DateTime!
  updatedAt: DateTime!
}

type AppInfo {
  name: String!
  version: String!
  description: String!
  environment: String!
  timestamp: String!
}

type Query {
  appInfo: AppInfo!
  hello: String!
  users: [User!]!
  user(id: ID!): User
  products: [Product!]!
  product(id: ID!): Product
}