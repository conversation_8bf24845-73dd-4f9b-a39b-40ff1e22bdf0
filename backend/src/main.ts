import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { DataSource } from 'typeorm';
import compression from 'compression';
import helmet from 'helmet';
import * as net from 'net';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Security middleware
  app.use(helmet());
  app.use(compression());

  // CORS configuration
  app.enableCors({
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // API prefix
  app.setGlobalPrefix('api/v1', {
    exclude: ['graphql', 'info', ''],
  });

  // Swagger documentation
  if (process.env.NODE_ENV !== 'production') {
    const config = new DocumentBuilder()
      .setTitle("A Good Man's View API")
      .setDescription('Multi-vendor e-commerce platform with blockchain integration')
      .setVersion('1.0')
      .addBearerAuth()
      .addTag('auth', 'Authentication endpoints')
      .addTag('users', 'User management')
      .addTag('products', 'Product catalog')
      .addTag('orders', 'Order processing')
      .addTag('payments', 'Payment processing')
      .addTag('wallet', 'Digital wallet')
      .addTag('blockchain', 'Blockchain integration')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
      },
    });
  }

  // Validate database connection if not skipped
  if (process.env.SKIP_DATABASE !== 'true') {
    try {
      const dataSource = app.get(DataSource);
      await dataSource.query('SELECT 1');
      console.log('✅ Database connection validated successfully');
    } catch (error) {
      console.error('❌ Database connection validation failed:', error.message);
      console.log('⚠️  Continuing without database validation...');
    }
  } else {
    console.log('⚠️  Database connection skipped (SKIP_DATABASE=true)');
  }

  const port = await findAvailablePort(parseInt(process.env.PORT) || 4000);
  await app.listen(port);

  console.log(`🚀 A Good Man's View API is running on: http://localhost:${port}`);
  console.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
  console.log(`🔗 GraphQL Playground: http://localhost:${port}/graphql`);
  console.log(`🏠 Root Endpoint: http://localhost:${port}/`);
}

// Function to find an available port
async function findAvailablePort(startPort: number): Promise<number> {

  const isPortAvailable = (port: number): Promise<boolean> => {
    return new Promise((resolve) => {
      const server = net.createServer();
      server.listen(port, () => {
        server.close(() => resolve(true));
      });
      server.on('error', () => resolve(false));
    });
  };

  let port = startPort;
  while (port < startPort + 10) { // Try up to 10 ports
    if (await isPortAvailable(port)) {
      return port;
    }
    console.log(`⚠️  Port ${port} is in use, trying ${port + 1}...`);
    port++;
  }

  throw new Error(`No available ports found between ${startPort} and ${startPort + 9}`);
}

bootstrap();
