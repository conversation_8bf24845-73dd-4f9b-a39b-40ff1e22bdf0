import {
  Injectable,
  ConflictException,
  UnauthorizedException,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import { User, UserRole } from '../../database/entities/user.entity';
import {
  RegisterDto,
  LoginDto,
  LoginResponseDto,
  RegisterResponseDto,
  UserResponseDto,
  AuthTokensDto,
  ProfileResponseDto,
  LogoutResponseDto,
  RefreshTokenResponseDto,
} from './dto';
import { accountLockoutConfig, tokenExpirationTimes } from '../../config/jwt.config';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async register(registerDto: RegisterDto): Promise<RegisterResponseDto> {
    const { email, password, passwordConfirmation, ...userData } = registerDto;

    // Validate password confirmation
    if (password !== passwordConfirmation) {
      throw new BadRequestException('Password and password confirmation do not match');
    }

    // Check if user already exists
    const existingUser = await this.userRepository.findOne({
      where: { email },
    });

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user
    const user = this.userRepository.create({
      email,
      passwordHash,
      role: userData.role || UserRole.BUYER,
      firstName: userData.firstName,
      lastName: userData.lastName,
      phone: userData.phone,
      dateOfBirth: userData.dateOfBirth ? new Date(userData.dateOfBirth) : undefined,
      emailVerified: false,
      isActive: true,
      loginAttempts: 0,
    });

    const savedUser = await this.userRepository.save(user);

    // Generate tokens
    const tokens = await this.generateTokens(savedUser);

    // Return response
    return {
      message: 'User registered successfully',
      user: this.transformUserToResponse(savedUser),
      tokens,
    };
  }

  async login(loginDto: LoginDto): Promise<LoginResponseDto> {
    const { email, password, rememberMe = false } = loginDto;

    // Find user by email
    const user = await this.userRepository.findOne({
      where: { email },
      select: [
        'id',
        'email',
        'passwordHash',
        'role',
        'firstName',
        'lastName',
        'phone',
        'emailVerified',
        'isActive',
        'loginAttempts',
        'lockedUntil',
        'lastLoginAt',
        'createdAt',
        'updatedAt',
      ],
    });

    if (!user) {
      throw new UnauthorizedException('Invalid email or password');
    }

    // Check if account is locked
    if (user.isAccountLocked) {
      throw new UnauthorizedException(
        `Account is temporarily locked due to multiple failed login attempts. Please try again later.`,
      );
    }

    // Check if account is active
    if (!user.isActive) {
      throw new UnauthorizedException('Account is deactivated. Please contact support.');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);

    if (!isPasswordValid) {
      await this.handleFailedLogin(user);
      throw new UnauthorizedException('Invalid email or password');
    }

    // Reset login attempts on successful login
    await this.resetLoginAttempts(user);

    // Update last login time
    user.lastLoginAt = new Date();
    await this.userRepository.save(user);

    // Generate tokens
    const tokens = await this.generateTokens(user, rememberMe);

    return {
      message: 'Login successful',
      user: this.transformUserToResponse(user),
      tokens,
    };
  }

  async logout(userId: string): Promise<LogoutResponseDto> {
    // In a production app, you might want to blacklist the token
    // For now, we'll just return a success message
    // You could also clear refresh tokens from database if stored

    return {
      message: 'Logout successful',
    };
  }

  async getProfile(userId: string): Promise<ProfileResponseDto> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: [
        'id',
        'email',
        'role',
        'firstName',
        'lastName',
        'phone',
        'dateOfBirth',
        'emailVerified',
        'isActive',
        'lastLoginAt',
        'createdAt',
        'updatedAt',
      ],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return {
      message: 'Profile retrieved successfully',
      user: this.transformUserToResponse(user),
    };
  }

  async refreshToken(refreshToken: string): Promise<RefreshTokenResponseDto> {
    try {
      // Verify refresh token
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('JWT_SECRET'),
      });

      // Find user
      const user = await this.userRepository.findOne({
        where: { id: payload.sub },
        select: ['id', 'email', 'role', 'isActive'],
      });

      if (!user || !user.isActive) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Generate new tokens
      const tokens = await this.generateTokens(user);

      return {
        message: 'Token refreshed successfully',
        tokens,
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async validateUser(userId: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { id: userId, isActive: true },
      select: [
        'id',
        'email',
        'role',
        'firstName',
        'lastName',
        'phone',
        'emailVerified',
        'isActive',
        'loginAttempts',
        'lockedUntil',
        'lastLoginAt',
        'createdAt',
        'updatedAt',
      ],
    });
  }

  private async generateTokens(user: User, rememberMe = false): Promise<AuthTokensDto> {
    const payload = {
      sub: user.id,
      email: user.email,
      role: user.role,
    };

    const accessTokenExpiry = rememberMe
      ? tokenExpirationTimes.rememberMeAccessToken
      : tokenExpirationTimes.accessToken;

    const refreshTokenExpiry = rememberMe
      ? tokenExpirationTimes.rememberMeRefreshToken
      : tokenExpirationTimes.refreshToken;

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        expiresIn: accessTokenExpiry,
        issuer: this.configService.get<string>('APP_NAME', 'A Good Man\'s View API'),
        audience: this.configService.get<string>('JWT_AUDIENCE', 'agoodmansview-users'),
      }),
      this.jwtService.signAsync(payload, {
        expiresIn: refreshTokenExpiry,
        issuer: this.configService.get<string>('APP_NAME', 'A Good Man\'s View API'),
        audience: this.configService.get<string>('JWT_AUDIENCE', 'agoodmansview-users'),
      }),
    ]);

    return {
      accessToken,
      refreshToken,
      tokenType: 'Bearer',
      expiresIn: accessTokenExpiry,
    };
  }

  private async handleFailedLogin(user: User): Promise<void> {
    user.loginAttempts += 1;

    // Lock account if max attempts reached
    if (user.loginAttempts >= accountLockoutConfig.maxLoginAttempts) {
      const lockoutDuration = accountLockoutConfig.lockoutDurationMinutes * 60 * 1000; // Convert to milliseconds
      user.lockedUntil = new Date(Date.now() + lockoutDuration);
    }

    await this.userRepository.save(user);
  }

  private async resetLoginAttempts(user: User): Promise<void> {
    if (user.loginAttempts > 0 || user.lockedUntil) {
      user.loginAttempts = 0;
      user.lockedUntil = null;
      await this.userRepository.save(user);
    }
  }

  private transformUserToResponse(user: User): UserResponseDto {
    return {
      id: user.id,
      email: user.email,
      role: user.role,
      firstName: user.firstName,
      lastName: user.lastName,
      fullName: user.fullName,
      phone: user.phone,
      emailVerified: user.emailVerified,
      isActive: user.isActive,
      createdAt: user.createdAt,
      lastLoginAt: user.lastLoginAt,
    };
  }
}
