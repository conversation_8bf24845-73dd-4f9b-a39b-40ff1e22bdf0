import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '../../../database/entities/user.entity';

export class UserResponseDto {
  @ApiProperty({
    description: 'User unique identifier',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
    format: 'email',
  })
  email: string;

  @ApiProperty({
    description: 'User role',
    enum: UserRole,
    example: UserRole.BUYER,
  })
  role: UserRole;

  @ApiProperty({
    description: 'User first name',
    example: '<PERSON>',
    required: false,
  })
  firstName?: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
    required: false,
  })
  lastName?: string;

  @ApiProperty({
    description: 'User full name',
    example: '<PERSON>',
    required: false,
  })
  fullName?: string;

  @ApiProperty({
    description: 'User phone number',
    example: '+***********',
    required: false,
  })
  phone?: string;

  @ApiProperty({
    description: 'Email verification status',
    example: false,
  })
  emailVerified: boolean;

  @ApiProperty({
    description: 'Account active status',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Account creation date',
    example: '2024-01-01T00:00:00.000Z',
    format: 'date-time',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last login date',
    example: '2024-01-01T00:00:00.000Z',
    format: 'date-time',
    required: false,
  })
  lastLoginAt?: Date;
}

export class AuthTokensDto {
  @ApiProperty({
    description: 'JWT access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'JWT refresh token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refreshToken: string;

  @ApiProperty({
    description: 'Token type',
    example: 'Bearer',
    default: 'Bearer',
  })
  tokenType: string = 'Bearer';

  @ApiProperty({
    description: 'Token expiration time in seconds',
    example: 604800,
  })
  expiresIn: number;
}

export class LoginResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Login successful',
  })
  message: string;

  @ApiProperty({
    description: 'User information',
    type: UserResponseDto,
  })
  user: UserResponseDto;

  @ApiProperty({
    description: 'Authentication tokens',
    type: AuthTokensDto,
  })
  tokens: AuthTokensDto;
}

export class RegisterResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'User registered successfully',
  })
  message: string;

  @ApiProperty({
    description: 'User information',
    type: UserResponseDto,
  })
  user: UserResponseDto;

  @ApiProperty({
    description: 'Authentication tokens',
    type: AuthTokensDto,
  })
  tokens: AuthTokensDto;
}

export class RefreshTokenResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Token refreshed successfully',
  })
  message: string;

  @ApiProperty({
    description: 'New authentication tokens',
    type: AuthTokensDto,
  })
  tokens: AuthTokensDto;
}

export class LogoutResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Logout successful',
  })
  message: string;
}

export class ProfileResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Profile retrieved successfully',
  })
  message: string;

  @ApiProperty({
    description: 'User profile information',
    type: UserResponseDto,
  })
  user: UserResponseDto;
}
