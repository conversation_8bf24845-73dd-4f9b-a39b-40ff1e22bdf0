import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsDateString,
  IsEnum,
  Length,
  Matches,
  IsString,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { UserRole } from '../../../database/entities/user.entity';
import { Match } from '../../../common/decorators/match.decorator';

export class RegisterDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
    format: 'email',
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  @Transform(({ value }) => value?.toLowerCase().trim())
  email: string;

  @ApiProperty({
    description: 'User password (minimum 8 characters, must contain uppercase, lowercase, number, and special character)',
    example: 'SecurePass123!',
    minLength: 8,
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Password is required' })
  @Length(8, 255, { message: 'Password must be between 8 and 255 characters' })
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    {
      message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
    },
  )
  password: string;

  @ApiProperty({
    description: 'Password confirmation (must match password)',
    example: 'SecurePass123!',
  })
  @IsNotEmpty({ message: 'Password confirmation is required' })
  @IsString()
  @Match('password', { message: 'Password confirmation must match password' })
  passwordConfirmation: string;

  @ApiProperty({
    description: 'User role',
    enum: UserRole,
    default: UserRole.BUYER,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserRole, { message: 'Invalid user role' })
  role?: UserRole = UserRole.BUYER;

  @ApiProperty({
    description: 'User first name',
    example: 'John',
    required: false,
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @Length(1, 100, { message: 'First name must be between 1 and 100 characters' })
  @Transform(({ value }) => value?.trim())
  firstName?: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
    required: false,
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @Length(1, 100, { message: 'Last name must be between 1 and 100 characters' })
  @Transform(({ value }) => value?.trim())
  lastName?: string;

  @ApiProperty({
    description: 'User phone number (South African format)',
    example: '+27123456789',
    required: false,
  })
  @IsOptional()
  @IsPhoneNumber('ZA', { message: 'Please provide a valid South African phone number' })
  phone?: string;

  @ApiProperty({
    description: 'User date of birth',
    example: '1990-01-01',
    format: 'date',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Please provide a valid date of birth' })
  dateOfBirth?: string;
}
