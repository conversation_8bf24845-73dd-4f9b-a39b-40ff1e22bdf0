import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../../database/entities/user.entity';
import { jwtStrategyConfig } from '../../../config/jwt.config';

export interface JwtPayload {
  sub: string; // User ID
  email: string;
  role: string;
  iat?: number;
  exp?: number;
  iss?: string;
  aud?: string;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        // Custom extractor that checks Authorization header and cookies
        (request) => {
          let token = null;

          // Check Authorization header first
          if (request && request.headers && request.headers.authorization) {
            const authHeader = request.headers.authorization;
            if (authHeader.startsWith('Bearer ')) {
              token = authHeader.substring(7);
            }
          }

          // Fallback to cookies if no Authorization header
          if (!token && request && request.cookies) {
            token = request.cookies['access_token'];
          }

          return token;
        },
      ]),
      secretOrKey: configService.get<string>('JWT_SECRET'),
      issuer: configService.get<string>('APP_NAME', 'A Good Man\'s View API'),
      audience: configService.get<string>('JWT_AUDIENCE', 'agoodmansview-users'),
      ignoreExpiration: false,
      passReqToCallback: false,
    });
  }

  async validate(payload: JwtPayload): Promise<User> {
    const { sub: userId, email } = payload;

    // Find user by ID
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: [
        'id',
        'email',
        'role',
        'firstName',
        'lastName',
        'phone',
        'emailVerified',
        'isActive',
        'loginAttempts',
        'lockedUntil',
        'lastLoginAt',
        'createdAt',
        'updatedAt',
      ],
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Check if user is active
    if (!user.isActive) {
      throw new UnauthorizedException('Account is deactivated');
    }

    // Check if account is locked
    if (user.isAccountLocked) {
      throw new UnauthorizedException('Account is temporarily locked due to multiple failed login attempts');
    }

    // Verify email matches (additional security check)
    if (user.email !== email) {
      throw new UnauthorizedException('Token email mismatch');
    }

    // Return user object (will be attached to request.user)
    return user;
  }
}
