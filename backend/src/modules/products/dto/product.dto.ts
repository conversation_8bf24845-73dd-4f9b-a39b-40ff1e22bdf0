import { ObjectType, Field, ID, Float } from '@nestjs/graphql';

@ObjectType()
export class Product {
  @Field(() => ID)
  id: string;

  @Field()
  name: string;

  @Field({ nullable: true })
  description?: string;

  @Field(() => Float)
  price: number;

  @Field()
  currency: string;

  @Field({ nullable: true })
  image?: string;

  @Field()
  vendorId: string;

  @Field()
  vendorName: string;

  @Field()
  category: string;

  @Field()
  inStock: boolean;

  @Field(() => Float, { nullable: true })
  rating?: number;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}
