import { Resolver, Query, Args, ID } from '@nestjs/graphql';
import { ProductsService } from './products.service';
import { Product } from './dto/product.dto';

@Resolver(() => Product)
export class ProductsResolver {
  constructor(private readonly productsService: ProductsService) {}

  @Query(() => [Product], { name: 'products' })
  async findAll(): Promise<Product[]> {
    const result = await this.productsService.findAll();
    return result.products || [];
  }

  @Query(() => Product, { name: 'product', nullable: true })
  async findOne(@Args('id', { type: () => ID }) id: string): Promise<Product | null> {
    const result = await this.productsService.findOne(id);
    return result.product || null;
  }
}
