import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';

describe('UsersController', () => {
  let controller: UsersController;
  let usersService: UsersService;

  const mockUsersService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    usersService = module.get<UsersService>(UsersService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of users', async () => {
      const expectedUsers = [
        { id: '1', email: '<EMAIL>', firstName: 'John', lastName: 'Doe' },
        { id: '2', email: '<EMAIL>', firstName: 'Jane', lastName: 'Smith' },
      ];
      
      mockUsersService.findAll.mockResolvedValue(expectedUsers);

      const result = await controller.findAll();

      expect(usersService.findAll).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedUsers);
    });

    it('should handle empty user list', async () => {
      mockUsersService.findAll.mockResolvedValue([]);

      const result = await controller.findAll();

      expect(usersService.findAll).toHaveBeenCalledTimes(1);
      expect(result).toEqual([]);
    });

    it('should handle service errors', async () => {
      const error = new Error('Database connection failed');
      mockUsersService.findAll.mockRejectedValue(error);

      await expect(controller.findAll()).rejects.toThrow('Database connection failed');
      expect(usersService.findAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('findOne', () => {
    it('should return a single user by ID', async () => {
      const userId = '1';
      const expectedUser = { 
        id: '1', 
        email: '<EMAIL>', 
        firstName: 'John', 
        lastName: 'Doe' 
      };
      
      mockUsersService.findOne.mockResolvedValue(expectedUser);

      const result = await controller.findOne(userId);

      expect(usersService.findOne).toHaveBeenCalledWith(userId);
      expect(usersService.findOne).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedUser);
    });

    it('should handle user not found', async () => {
      const userId = 'non-existent';
      const error = new Error('User not found');
      
      mockUsersService.findOne.mockRejectedValue(error);

      await expect(controller.findOne(userId)).rejects.toThrow('User not found');
      expect(usersService.findOne).toHaveBeenCalledWith(userId);
    });

    it('should handle invalid user ID format', async () => {
      const invalidId = 'invalid-id';
      const error = new Error('Invalid user ID format');
      
      mockUsersService.findOne.mockRejectedValue(error);

      await expect(controller.findOne(invalidId)).rejects.toThrow('Invalid user ID format');
      expect(usersService.findOne).toHaveBeenCalledWith(invalidId);
    });
  });
});
