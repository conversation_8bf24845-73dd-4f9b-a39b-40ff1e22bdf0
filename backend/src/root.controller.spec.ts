import { Test, TestingModule } from '@nestjs/testing';
import { RootController } from './root.controller';

describe('RootController', () => {
  let rootController: RootController;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [RootController],
    }).compile();

    rootController = app.get<RootController>(RootController);
  });

  describe('getRoot', () => {
    it('should return welcome message with API information', () => {
      const result = rootController.getRoot();
      
      expect(result).toBeDefined();
      expect(result.message).toBe('Welcome to A Good Man\'s View API');
      expect(result.name).toBe('A Good Man\'s View');
      expect(result.version).toBe('1.0.0');
      expect(result.status).toBe('running');
      expect(result.timestamp).toBeDefined();
      expect(new Date(result.timestamp)).toBeInstanceOf(Date);
    });

    it('should include correct endpoints information', () => {
      const result = rootController.getRoot();
      
      expect(result.endpoints).toBeDefined();
      expect(result.endpoints.api).toBe('/api/v1');
      expect(result.endpoints.docs).toBe('/api/docs');
      expect(result.endpoints.graphql).toBe('/graphql');
    });

    it('should include platform description and features', () => {
      const result = rootController.getRoot();
      
      expect(result.description).toBe('Multi-vendor e-commerce platform with blockchain integration for South Africa');
      expect(result.features).toContain('REST API');
      expect(result.features).toContain('GraphQL API');
      expect(result.features).toContain('Blockchain Integration');
      expect(result.features).toContain('Multi-vendor Support');
      expect(result.features).toContain('South African Market Focus');
    });

    it('should return a fresh timestamp on each call', async () => {
      const result1 = rootController.getRoot();
      
      // Wait a small amount to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10));
      
      const result2 = rootController.getRoot();
      
      expect(result1.timestamp).not.toBe(result2.timestamp);
    });
  });
});
