import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('root')
@Controller()
export class RootController {
  @Get()
  @ApiOperation({ summary: 'Root endpoint - API welcome message' })
  @ApiResponse({ 
    status: 200, 
    description: 'Welcome message with API information',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        name: { type: 'string' },
        version: { type: 'string' },
        status: { type: 'string' },
        timestamp: { type: 'string' },
        endpoints: {
          type: 'object',
          properties: {
            api: { type: 'string' },
            docs: { type: 'string' },
            graphql: { type: 'string' },

          },
        },
      },
    },
  })
  getRoot() {
    return {
      message: 'Welcome to A Good Man\'s View API',
      name: 'A Good Man\'s View',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString(),
      endpoints: {
        api: '/api/v1',
        docs: '/api/docs',
        graphql: '/graphql',
      },
      description: 'Multi-vendor e-commerce platform with blockchain integration for South Africa',
      features: [
        'REST API',
        'GraphQL API',
        'Blockchain Integration',
        'Multi-vendor Support',
        'South African Market Focus',
      ],
    };
  }
}
