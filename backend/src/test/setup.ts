// Test setup file for Jest
// This file is executed before each test file

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.SKIP_DATABASE = 'true';
process.env.GRAPHQL_ENABLED = 'false';
process.env.JWT_SECRET = 'test-secret-key';
process.env.RATE_LIMIT_TTL = '60000';
process.env.RATE_LIMIT_MAX = '100';

// Mock console methods to reduce noise in tests
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  // Suppress console output during tests unless explicitly needed
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  // Restore console methods
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Global test timeout
jest.setTimeout(30000);
