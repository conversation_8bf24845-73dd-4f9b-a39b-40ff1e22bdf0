import AppDataSource from './src/database/data-source';

async function testConnection() {
  try {
    console.log('🔌 Testing database connection...');
    
    await AppDataSource.initialize();
    console.log('✅ Database connection successful!');
    
    // Test a simple query
    const result = await AppDataSource.query('SELECT NOW() as current_time, version() as postgres_version');
    console.log('📊 Database info:');
    console.log(`- Current time: ${result[0].current_time}`);
    console.log(`- PostgreSQL version: ${result[0].postgres_version}`);
    
    await AppDataSource.destroy();
    console.log('🔌 Connection closed');
    
  } catch (error) {
    console.error('❌ Database connection failed:');
    console.error(error.message);
    process.exit(1);
  }
}

testConnection();
