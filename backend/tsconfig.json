{"compilerOptions": {"module": "commonjs", "moduleResolution": "node", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "strictPropertyInitialization": false, "paths": {"@/*": ["src/*"], "@/modules/*": ["src/modules/*"], "@/common/*": ["src/common/*"], "@/database/*": ["src/database/*"], "@/shared/*": ["src/shared/*"]}}, "exclude": ["node_modules", "dist", "**/*.md"]}