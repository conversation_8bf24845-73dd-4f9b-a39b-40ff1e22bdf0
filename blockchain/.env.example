# Blockchain Environment Variables
# Copy this file to .env and update the values

# Network Configuration
BLOCKCHAIN_NETWORK_ID=1337
BLOCKCHAIN_CONSENSUS=pos
BLOCKCHAIN_RPC_PORT=8545
BLOCKCHAIN_API_PORT=8546

# Node Configuration
NODE_ENV=development
LOG_LEVEL=info
DATA_DIR=./data

# Consensus (Proof of Stake)
POS_BLOCK_TIME=5000
POS_DIFFICULTY=4
POS_REWARD=10

# Network
MAX_PEERS=10
DISCOVERY_PORT=30303

# Security
PRIVATE_KEY=your-node-private-key
