{"name": "@agoodmansview/blockchain", "version": "1.0.0", "description": "Private blockchain implementation with Proof of Stake consensus", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/main.js", "dev": "ts-node src/main.ts", "start:node": "ts-node src/scripts/start-node.ts", "create:genesis": "ts-node src/scripts/create-genesis.ts", "deploy:contracts": "ts-node src/scripts/deploy-contracts.ts", "benchmark": "ts-node src/scripts/benchmark.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:performance": "jest --config jest.performance.config.js", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "clean": "rm -rf dist"}, "dependencies": {"elliptic": "^6.6.1", "crypto-js": "^4.2.0", "level": "^8.0.1", "ws": "^8.18.2", "express": "^5.1.0", "cors": "^2.8.5", "helmet": "^8.1.0", "compression": "^1.8.0", "winston": "^3.17.0", "uuid": "^11.1.0", "big.js": "^7.0.1", "merkle-tree-gen": "^1.1.0"}, "devDependencies": {"@types/node": "^24.0.3", "@types/express": "^5.0.3", "@types/ws": "^8.18.1", "@types/cors": "^2.8.19", "@types/compression": "^1.8.1", "@types/uuid": "^10.0.0", "@types/big.js": "^6.2.2", "@types/jest": "^30.0.0", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint": "^9.29.0", "jest": "^30.0.2", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/scripts/**/*.ts"]}}