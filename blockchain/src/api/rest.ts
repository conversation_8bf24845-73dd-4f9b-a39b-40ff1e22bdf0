import express from 'express';
import { BlockchainNode } from '../core/blockchain';
import { WalletService } from '../wallet/wallet';
import { logger } from '../utils/logger';

export interface ApiServerConfig {
  port: number;
  blockchain: BlockchainNode;
  walletService: WalletService;
}

export class ApiServer {
  private app: express.Application;
  private config: ApiServerConfig;
  private server: any;

  constructor(config: ApiServerConfig) {
    this.config = config;
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    
    // CORS
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      next();
    });
  }

  private setupRoutes(): void {
    // Blockchain info
    this.app.get('/blockchain/info', (req, res) => {
      res.json({
        height: this.config.blockchain.getBlockHeight(),
        networkId: this.config.blockchain.getNetworkId(),
        latestBlock: this.config.blockchain.getLatestBlock(),
      });
    });

    // Get blocks
    this.app.get('/blockchain/blocks', (req, res) => {
      const blocks = this.config.blockchain.getBlocks();
      res.json(blocks);
    });

    // Wallet endpoints
    this.app.post('/wallet/create', (req, res) => {
      const { address } = req.body;
      const wallet = this.config.walletService.createWallet(address);
      res.json(wallet);
    });

    this.app.get('/wallet/:address', (req, res) => {
      const { address } = req.params;
      const wallet = this.config.walletService.getWallet(address);
      
      if (wallet) {
        res.json(wallet);
      } else {
        res.status(404).json({ error: 'Wallet not found' });
      }
    });

    // Send transaction
    this.app.post('/transaction/send', async (req, res) => {
      const { from, to, amount } = req.body;
      const txId = await this.config.walletService.sendTransaction(from, to, amount);
      
      if (txId) {
        res.json({ transactionId: txId });
      } else {
        res.status(400).json({ error: 'Transaction failed' });
      }
    });
  }

  async start(): Promise<void> {
    return new Promise((resolve) => {
      this.server = this.app.listen(this.config.port, () => {
        logger.info(`Blockchain API server started on port ${this.config.port}`);
        resolve();
      });
    });
  }

  async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          logger.info('Blockchain API server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}
