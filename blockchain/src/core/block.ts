import * as crypto from 'crypto';
import { Transaction } from './transaction';

export interface BlockData {
  index: number;
  timestamp: number;
  transactions: Transaction[];
  previousHash: string;
  nonce: number;
  hash?: string;
}

export class Block {
  public index: number;
  public timestamp: number;
  public transactions: Transaction[];
  public previousHash: string;
  public nonce: number;
  public hash: string;

  constructor(data: BlockData) {
    this.index = data.index;
    this.timestamp = data.timestamp;
    this.transactions = data.transactions;
    this.previousHash = data.previousHash;
    this.nonce = data.nonce;
    this.hash = data.hash || this.calculateHash();
  }

  calculateHash(): string {
    const data = {
      index: this.index,
      timestamp: this.timestamp,
      transactions: this.transactions.map(tx => tx.toJSON()),
      previousHash: this.previousHash,
      nonce: this.nonce,
    };

    return crypto
      .createHash('sha256')
      .update(JSON.stringify(data))
      .digest('hex');
  }

  mineBlock(difficulty: number): void {
    const target = Array(difficulty + 1).join('0');
    
    while (this.hash.substring(0, difficulty) !== target) {
      this.nonce++;
      this.hash = this.calculateHash();
    }
  }

  validateBlock(): boolean {
    // Validate hash
    if (this.hash !== this.calculateHash()) {
      return false;
    }

    // Validate transactions
    for (const transaction of this.transactions) {
      if (!transaction.validate()) {
        return false;
      }
    }

    return true;
  }

  toJSON() {
    return {
      index: this.index,
      timestamp: this.timestamp,
      transactions: this.transactions.map(tx => tx.toJSON()),
      previousHash: this.previousHash,
      nonce: this.nonce,
      hash: this.hash,
    };
  }

  static fromJSON(data: any): Block {
    const transactions = data.transactions.map((tx: any) => Transaction.fromJSON(tx));
    
    return new Block({
      index: data.index,
      timestamp: data.timestamp,
      transactions,
      previousHash: data.previousHash,
      nonce: data.nonce,
      hash: data.hash,
    });
  }
}
