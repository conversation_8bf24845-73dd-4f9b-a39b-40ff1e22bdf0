import { Block } from './block';
import { Transaction } from './transaction';
import { ProofOfStake } from '../consensus/pos';
import { logger } from '../utils/logger';

export interface BlockchainConfig {
  networkId: number;
  consensus: string;
  dataDir: string;
}

export class BlockchainNode {
  private blocks: Block[] = [];
  private pendingTransactions: Transaction[] = [];
  private consensus: ProofOfStake;
  private config: BlockchainConfig;

  constructor(config: BlockchainConfig) {
    this.config = config;
    this.consensus = new ProofOfStake();
  }

  async initialize(): Promise<void> {
    logger.info('Initializing blockchain...');
    
    // Create genesis block if no blocks exist
    if (this.blocks.length === 0) {
      const genesisBlock = this.createGenesisBlock();
      this.blocks.push(genesisBlock);
      logger.info(`Genesis block created: ${genesisBlock.hash}`);
    }

    // Initialize consensus mechanism
    await this.consensus.initialize();
    
    logger.info('Blockchain initialized successfully');
  }

  private createGenesisBlock(): Block {
    const genesisBlock = new Block({
      index: 0,
      timestamp: Date.now(),
      transactions: [],
      previousHash: '0',
      nonce: 0,
    });
    
    genesisBlock.hash = genesisBlock.calculateHash();
    return genesisBlock;
  }

  getGenesisBlock(): Block {
    return this.blocks[0];
  }

  getLatestBlock(): Block {
    return this.blocks[this.blocks.length - 1];
  }

  getNetworkId(): number {
    return this.config.networkId;
  }

  getBlockHeight(): number {
    return this.blocks.length;
  }

  getBlocks(): Block[] {
    return [...this.blocks];
  }

  addTransaction(transaction: Transaction): boolean {
    try {
      // Validate transaction
      if (!this.validateTransaction(transaction)) {
        logger.warn(`Invalid transaction: ${transaction.id}`);
        return false;
      }

      this.pendingTransactions.push(transaction);
      logger.info(`Transaction added to pool: ${transaction.id}`);
      return true;
    } catch (error) {
      logger.error('Error adding transaction:', error);
      return false;
    }
  }

  async mineBlock(minerAddress: string): Promise<Block | null> {
    try {
      if (this.pendingTransactions.length === 0) {
        logger.info('No pending transactions to mine');
        return null;
      }

      const latestBlock = this.getLatestBlock();
      const newBlock = new Block({
        index: latestBlock.index + 1,
        timestamp: Date.now(),
        transactions: [...this.pendingTransactions],
        previousHash: latestBlock.hash,
        nonce: 0,
      });

      // Use consensus mechanism to validate and mine block
      const minedBlock = await this.consensus.mineBlock(newBlock, minerAddress);
      
      if (minedBlock) {
        this.blocks.push(minedBlock);
        this.pendingTransactions = [];
        logger.info(`Block mined successfully: ${minedBlock.hash}`);
        return minedBlock;
      }

      return null;
    } catch (error) {
      logger.error('Error mining block:', error);
      return null;
    }
  }

  validateChain(): boolean {
    for (let i = 1; i < this.blocks.length; i++) {
      const currentBlock = this.blocks[i];
      const previousBlock = this.blocks[i - 1];

      // Validate current block hash
      if (currentBlock.hash !== currentBlock.calculateHash()) {
        logger.error(`Invalid hash for block ${i}`);
        return false;
      }

      // Validate previous hash reference
      if (currentBlock.previousHash !== previousBlock.hash) {
        logger.error(`Invalid previous hash for block ${i}`);
        return false;
      }
    }

    return true;
  }

  private validateTransaction(transaction: Transaction): boolean {
    // Basic transaction validation
    if (!transaction.id || !transaction.from || !transaction.to) {
      return false;
    }

    if (transaction.amount <= 0) {
      return false;
    }

    // TODO: Add signature validation
    // TODO: Add balance validation
    
    return true;
  }

  async shutdown(): Promise<void> {
    logger.info('Shutting down blockchain...');
    // TODO: Save state to disk
    // TODO: Close database connections
    logger.info('Blockchain shutdown complete');
  }
}
