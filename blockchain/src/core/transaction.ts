import * as crypto from 'crypto';

export interface TransactionData {
  id?: string;
  from: string;
  to: string;
  amount: number;
  fee: number;
  timestamp: number;
  signature?: string;
  data?: any;
}

export class Transaction {
  public id: string;
  public from: string;
  public to: string;
  public amount: number;
  public fee: number;
  public timestamp: number;
  public signature?: string;
  public data?: any;

  constructor(data: TransactionData) {
    this.id = data.id || this.generateId();
    this.from = data.from;
    this.to = data.to;
    this.amount = data.amount;
    this.fee = data.fee;
    this.timestamp = data.timestamp;
    this.signature = data.signature;
    this.data = data.data;
  }

  private generateId(): string {
    const data = {
      from: this.from,
      to: this.to,
      amount: this.amount,
      timestamp: this.timestamp,
    };

    return crypto
      .createHash('sha256')
      .update(JSON.stringify(data))
      .digest('hex');
  }

  calculateHash(): string {
    const data = {
      id: this.id,
      from: this.from,
      to: this.to,
      amount: this.amount,
      fee: this.fee,
      timestamp: this.timestamp,
      data: this.data,
    };

    return crypto
      .createHash('sha256')
      .update(JSON.stringify(data))
      .digest('hex');
  }

  sign(privateKey: string): void {
    // TODO: Implement proper digital signature
    const hash = this.calculateHash();
    this.signature = crypto
      .createHmac('sha256', privateKey)
      .update(hash)
      .digest('hex');
  }

  validate(): boolean {
    // Basic validation
    if (!this.id || !this.from || !this.to) {
      return false;
    }

    if (this.amount <= 0 || this.fee < 0) {
      return false;
    }

    if (this.from === this.to) {
      return false;
    }

    // TODO: Validate signature
    // TODO: Validate balance

    return true;
  }

  toJSON() {
    return {
      id: this.id,
      from: this.from,
      to: this.to,
      amount: this.amount,
      fee: this.fee,
      timestamp: this.timestamp,
      signature: this.signature,
      data: this.data,
    };
  }

  static fromJSON(data: any): Transaction {
    return new Transaction({
      id: data.id,
      from: data.from,
      to: data.to,
      amount: data.amount,
      fee: data.fee,
      timestamp: data.timestamp,
      signature: data.signature,
      data: data.data,
    });
  }
}
