import { BlockchainNode } from './core/blockchain';
import { NetworkNode } from './network/node';
import { WalletService } from './wallet/wallet';
import { ApiServer } from './api/rest';
import { logger } from './utils/logger';

async function main() {
  try {
    logger.info('🚀 Starting A Good Man\'s View Blockchain Node...');

    // Initialize blockchain
    const blockchain = new BlockchainNode({
      networkId: parseInt(process.env.BLOCKCHAIN_NETWORK_ID || '1337'),
      consensus: process.env.BLOCKCHAIN_CONSENSUS || 'pos',
      dataDir: './data/blockchain',
    });

    // Initialize network node
    const networkNode = new NetworkNode({
      port: 8545,
      blockchain,
    });

    // Initialize wallet service
    const walletService = new WalletService({
      blockchain,
    });

    // Initialize API server
    const apiServer = new ApiServer({
      port: 8546,
      blockchain,
      walletService,
    });

    // Start services
    await blockchain.initialize();
    await networkNode.start();
    await apiServer.start();

    logger.info('✅ Blockchain node started successfully');
    logger.info(`🔗 RPC Server: http://localhost:8545`);
    logger.info(`🌐 API Server: http://localhost:8546`);
    logger.info(`📊 Network ID: ${blockchain.getNetworkId()}`);
    logger.info(`⛓️  Genesis Block: ${blockchain.getGenesisBlock().hash}`);

    // Graceful shutdown
    process.on('SIGINT', async () => {
      logger.info('🛑 Shutting down blockchain node...');
      await apiServer.stop();
      await networkNode.stop();
      await blockchain.shutdown();
      process.exit(0);
    });

  } catch (error) {
    logger.error('❌ Failed to start blockchain node:', error);
    process.exit(1);
  }
}

// Start the blockchain node
main();
