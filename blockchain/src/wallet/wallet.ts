import { BlockchainNode } from '../core/blockchain';
import { Transaction } from '../core/transaction';
import { logger } from '../utils/logger';

export interface WalletServiceConfig {
  blockchain: BlockchainNode;
}

export class WalletService {
  private blockchain: BlockchainNode;
  private wallets: Map<string, any> = new Map();

  constructor(config: WalletServiceConfig) {
    this.blockchain = config.blockchain;
  }

  createWallet(address: string): any {
    const wallet = {
      address,
      balance: 0,
      transactions: [],
      createdAt: new Date(),
    };

    this.wallets.set(address, wallet);
    logger.info(`Created wallet for address: ${address}`);
    
    return wallet;
  }

  getWallet(address: string): any | null {
    return this.wallets.get(address) || null;
  }

  getBalance(address: string): number {
    const wallet = this.getWallet(address);
    return wallet ? wallet.balance : 0;
  }

  async sendTransaction(from: string, to: string, amount: number): Promise<string | null> {
    try {
      const transaction = new Transaction({
        from,
        to,
        amount,
        fee: 0.01,
        timestamp: Date.now(),
      });

      // Add transaction to blockchain
      const success = this.blockchain.addTransaction(transaction);
      
      if (success) {
        logger.info(`Transaction sent: ${transaction.id}`);
        return transaction.id;
      }

      return null;
    } catch (error) {
      logger.error('Error sending transaction:', error);
      return null;
    }
  }

  getTransactionHistory(address: string): any[] {
    const wallet = this.getWallet(address);
    return wallet ? wallet.transactions : [];
  }

  updateBalance(address: string, amount: number): void {
    const wallet = this.getWallet(address);
    if (wallet) {
      wallet.balance += amount;
      logger.info(`Updated balance for ${address}: ${wallet.balance}`);
    }
  }
}
