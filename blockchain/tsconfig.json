{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "declaration": true, "removeComments": true, "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/core/*": ["src/core/*"], "@/consensus/*": ["src/consensus/*"], "@/network/*": ["src/network/*"], "@/wallet/*": ["src/wallet/*"], "@/utils/*": ["src/utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "**/*.md"]}