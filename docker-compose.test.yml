# 🧪 Docker Compose Configuration for Testing
#
# This configuration provides isolated test environments for:
# - Integration testing with real databases
# - E2E testing with full application stack
# - Performance testing with production-like setup
#
# Usage:
#   docker-compose -f docker-compose.test.yml up -d    # Start test services
#   docker-compose -f docker-compose.test.yml down     # Stop and cleanup

version: '3.8'

services:
  # PostgreSQL Test Database
  # Isolated database instance for integration testing
  postgres:
    image: postgres:15-alpine
    container_name: agoodmansview-test-postgres
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    environment:
      POSTGRES_DB: agoodmansview_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_INITDB_ARGS: --auth-host=scram-sha-256
    volumes:
      # Use tmpfs for faster test execution
      - type: tmpfs
        target: /var/lib/postgresql/data
        tmpfs:
          size: 1G
      # Initialize test data
      - ./backend/database/test-data:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d agoodmansview_test"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - test-network

  # Redis Test Cache
  # Isolated Redis instance for caching tests
  redis:
    image: redis:7-alpine
    container_name: agoodmansview-test-redis
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      # Use tmpfs for faster test execution
      - type: tmpfs
        target: /data
        tmpfs:
          size: 512M
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - test-network

  # Test Backend Service
  # Backend service configured for testing
  backend-test:
    build:
      context: ./backend
      dockerfile: Dockerfile.test
    container_name: agoodmansview-test-backend
    ports:
      - "4001:4000"  # Different port for testing
    environment:
      NODE_ENV: test
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_USERNAME: postgres
      DATABASE_PASSWORD: postgres
      DATABASE_NAME: agoodmansview_test
      REDIS_URL: redis://redis:6379
      JWT_SECRET: test-jwt-secret-key
      CORS_ORIGIN: http://localhost:3001
      LOG_LEVEL: error  # Reduce log noise during tests
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/api/health"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - test-network
    profiles:
      - full-stack  # Only start with full stack testing

  # Test Frontend Service
  # Frontend service for E2E testing
  frontend-test:
    build:
      context: ./frontend
      dockerfile: Dockerfile.test
    container_name: agoodmansview-test-frontend
    ports:
      - "3001:3000"  # Different port for testing
    environment:
      NODE_ENV: test
      NEXT_PUBLIC_API_URL: http://backend-test:4000
      NEXT_PUBLIC_GRAPHQL_URL: http://backend-test:4000/graphql
      NEXT_PUBLIC_WS_URL: ws://backend-test:4000/graphql
    depends_on:
      backend-test:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - test-network
    profiles:
      - full-stack  # Only start with full stack testing

  # Blockchain Test Node
  # Isolated blockchain node for testing
  blockchain-test:
    build:
      context: ./blockchain
      dockerfile: Dockerfile.test
    container_name: agoodmansview-test-blockchain
    ports:
      - "8546:8545"  # Different port for testing
    environment:
      NODE_ENV: test
      NETWORK_ID: 1338  # Different network ID for testing
      CONSENSUS: pos
      MINING_ENABLED: true
      BLOCK_TIME: 1  # Faster block time for testing
    volumes:
      # Use tmpfs for faster test execution
      - type: tmpfs
        target: /app/data
        tmpfs:
          size: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8545/health"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - test-network
    profiles:
      - blockchain  # Only start for blockchain testing

  # Test Data Seeder
  # Service to populate test data
  test-seeder:
    build:
      context: ./backend
      dockerfile: Dockerfile.seeder
    container_name: agoodmansview-test-seeder
    environment:
      NODE_ENV: test
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_USERNAME: postgres
      DATABASE_PASSWORD: postgres
      DATABASE_NAME: agoodmansview_test
    depends_on:
      postgres:
        condition: service_healthy
    command: npm run seed:test
    networks:
      - test-network
    profiles:
      - seeding  # Only run when seeding is needed

  # Performance Testing Service
  # Lighthouse and performance testing
  lighthouse:
    image: femtopixel/google-lighthouse
    container_name: agoodmansview-lighthouse
    command: lighthouse http://frontend-test:3000 --output=json --output-path=/tmp/lighthouse-report.json --chrome-flags="--headless --no-sandbox"
    volumes:
      - ./test-results:/tmp
    depends_on:
      frontend-test:
        condition: service_healthy
    networks:
      - test-network
    profiles:
      - performance  # Only run for performance testing

  # Accessibility Testing Service
  # Pa11y accessibility testing
  accessibility-test:
    image: node:18-alpine
    container_name: agoodmansview-a11y
    working_dir: /app
    volumes:
      - ./frontend:/app
      - ./test-results:/app/test-results
    command: >
      sh -c "
        npm install -g pa11y &&
        pa11y http://frontend-test:3000 --reporter json > test-results/a11y-report.json
      "
    depends_on:
      frontend-test:
        condition: service_healthy
    networks:
      - test-network
    profiles:
      - accessibility  # Only run for accessibility testing

  # Security Testing Service
  # OWASP ZAP security testing
  security-test:
    image: owasp/zap2docker-stable
    container_name: agoodmansview-security
    command: zap-baseline.py -t http://frontend-test:3000 -J /zap/wrk/security-report.json
    volumes:
      - ./test-results:/zap/wrk
    depends_on:
      frontend-test:
        condition: service_healthy
    networks:
      - test-network
    profiles:
      - security  # Only run for security testing

  # Test Results Collector
  # Collects and aggregates test results
  test-collector:
    image: node:18-alpine
    container_name: agoodmansview-test-collector
    working_dir: /app
    volumes:
      - ./test-results:/app/results
      - ./scripts:/app/scripts
    command: >
      sh -c "
        echo 'Collecting test results...' &&
        node scripts/collect-test-results.js &&
        echo 'Test results collected successfully'
      "
    networks:
      - test-network
    profiles:
      - collect  # Only run for result collection

# Network Configuration
networks:
  test-network:
    driver: bridge
    name: agoodmansview-test-network

# Volume Configuration
volumes:
  # Persistent volumes for test data (if needed)
  test-postgres-data:
    driver: local
  test-redis-data:
    driver: local
  test-blockchain-data:
    driver: local
