version: '3.8'

services:
  # Frontend - Next.js Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://backend:4000
      - NEXT_PUBLIC_GRAPHQL_URL=http://backend:4000/graphql
      - NEXT_PUBLIC_WS_URL=ws://backend:4000/graphql
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - agoodmansview-network

  # Backend - NestJS API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=development
      - SKIP_DATABASE=false
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USERNAME=postgres
      - DATABASE_PASSWORD=password
      - DATABASE_NAME=agoodmansview_db
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key
      - BLOCKCHAIN_RPC_URL=http://blockchain:8545
      - CORS_ORIGIN=http://localhost:3000
    volumes:
      - ./backend:/app
      - /app/node_modules
      - /app/dist
    depends_on:
      - postgres
      - redis
      - blockchain
    networks:
      - agoodmansview-network

  # Blockchain - Private Blockchain Node
  blockchain:
    build:
      context: ./blockchain
      dockerfile: Dockerfile
    ports:
      - "8545:8545"
      - "8546:8546"
    environment:
      - NODE_ENV=development
      - NETWORK_ID=1337
      - CONSENSUS=pos
    volumes:
      - ./blockchain:/app
      - blockchain-data:/app/data
    networks:
      - agoodmansview-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=agoodmansview_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres-data:/var/lib/postgresql/data

    networks:
      - agoodmansview-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - agoodmansview-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./infrastructure/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - agoodmansview-network



volumes:
  postgres-data:
  redis-data:
  blockchain-data:

networks:
  agoodmansview-network:
    driver: bridge
