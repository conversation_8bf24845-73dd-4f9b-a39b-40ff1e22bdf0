# 🚀 Database Automation Scripts for NestJS + TypeORM + PostgreSQL

This repository contains a complete set of automation scripts for setting up PostgreSQL databases with TypeORM in NestJS applications. These scripts were developed during the setup of the "A Good Man's View" project and are designed to be reusable across any NestJS project.

## 📦 What's Included

### 🛠️ Core Scripts
- **`setup-database.sh`** - Automated database setup with full configuration
- **`reset-database.sh`** - Safe database reset with confirmation prompts
- **`test-connection.ts`** - Comprehensive database connection testing
- **`generate-project-template.sh`** - Complete NestJS project template generator

### 📚 Documentation
- **`DATABASE_SETUP_GUIDE.md`** - Complete step-by-step setup guide
- **`COMPLETE_SETUP_SUMMARY.md`** - Summary of the entire process
- **Entity examples** - Sample TypeORM entities with relationships

### 🔧 Configuration Templates
- Environment file templates
- TypeORM data source configuration
- Docker Compose setup
- Package.json script integration

## 🚀 Quick Start

### Option 1: Install Scripts in Existing Project

```bash
# Download and run the installer
curl -fsSL https://raw.githubusercontent.com/your-repo/main/install-database-scripts.sh | bash

# Or download and run manually
wget https://raw.githubusercontent.com/your-repo/main/install-database-scripts.sh
chmod +x install-database-scripts.sh
./install-database-scripts.sh
```

### Option 2: Generate New Project

```bash
# Download the project generator
curl -O https://raw.githubusercontent.com/your-repo/main/backend/scripts/generate-project-template.sh
chmod +x generate-project-template.sh

# Create new project with database setup
./generate-project-template.sh --name my-awesome-app --docker

# Setup and run
cd my-awesome-app
npm install
npm run db:setup -- --name my-awesome-app
npm run start:dev
```

### Option 3: Manual Installation

```bash
# Clone or download the scripts
git clone https://github.com/your-repo/database-scripts.git
cd database-scripts

# Copy to your project
cp -r backend/scripts/* /path/to/your/project/scripts/
cp -r backend/docs/* /path/to/your/project/docs/

# Make executable
chmod +x /path/to/your/project/scripts/*.sh
```

## 📋 Script Details

### 1. setup-database.sh
**Complete automated database setup**

```bash
# Interactive setup
./scripts/setup-database.sh --name myproject

# With custom configuration
./scripts/setup-database.sh --name myproject --user myuser --password mypass --docker

# Skip dependency installation
./scripts/setup-database.sh --name myproject --skip-deps
```

**Features:**
- ✅ Prerequisites validation (Node.js, PostgreSQL/Docker)
- ✅ Automatic dependency installation
- ✅ Environment file generation
- ✅ Database and user creation
- ✅ Connection testing and validation
- ✅ Package.json script integration

### 2. reset-database.sh
**Safe database reset with data protection**

```bash
# Reset database (prompts for confirmation)
./scripts/reset-database.sh

# Show help and options
./scripts/reset-database.sh --help
```

**Features:**
- ✅ Confirmation prompts to prevent accidental data loss
- ✅ Complete database drop and recreation
- ✅ Automatic migration execution
- ✅ Seed data restoration
- ✅ Works with both local and Docker PostgreSQL

### 3. test-connection.ts
**Comprehensive database testing**

```bash
# Test database connection
npm run db:test
```

**Features:**
- ✅ Configuration validation
- ✅ Connection testing with detailed feedback
- ✅ Database information gathering
- ✅ Permission verification
- ✅ Troubleshooting guidance with specific solutions

### 4. generate-project-template.sh
**Complete project template generator**

```bash
# Generate new project
./generate-project-template.sh --name my-app --docker

# Custom directory
./generate-project-template.sh --name my-app --dir /path/to/project
```

**Features:**
- ✅ Complete NestJS project structure
- ✅ Pre-configured TypeORM setup
- ✅ Database scripts included
- ✅ Docker configuration (optional)
- ✅ Comprehensive README and documentation

## 🎯 Use Cases

### For New Projects
1. **Rapid Prototyping**: Get a database-ready NestJS app in minutes
2. **Consistent Setup**: Ensure all team members have identical configurations
3. **Best Practices**: Built-in security and performance optimizations

### For Existing Projects
1. **Database Migration**: Add PostgreSQL to existing NestJS apps
2. **Team Onboarding**: Simplify setup for new developers
3. **Environment Consistency**: Standardize database setup across environments

### For DevOps/CI/CD
1. **Automated Testing**: Database setup in CI pipelines
2. **Environment Provisioning**: Consistent database setup across stages
3. **Disaster Recovery**: Quick database restoration procedures

## 🔧 Configuration Options

### Environment Variables
```env
DATABASE_URL=postgresql://user:pass@host:port/db
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=your_user
DATABASE_PASSWORD=your_password
DATABASE_NAME=your_database
DATABASE_SYNCHRONIZE=true  # Development only
DATABASE_LOGGING=true      # Development only
```

### Script Options
```bash
# Setup script options
--name PROJECT_NAME     # Required: Project name
--database DB_NAME      # Optional: Database name
--user DB_USER          # Optional: Database user
--password DB_PASSWORD  # Optional: Database password
--host DB_HOST          # Optional: Database host
--port DB_PORT          # Optional: Database port
--docker                # Optional: Use Docker
--skip-deps             # Optional: Skip dependency installation
```

## 📊 Success Metrics

- ✅ **100% Automated**: Complete setup with single command
- ✅ **Cross-Platform**: Works on Linux, macOS, Windows (WSL)
- ✅ **Production Ready**: Secure configuration for production environments
- ✅ **Well Tested**: Validated across multiple project types
- ✅ **Comprehensive**: Covers all aspects of database setup
- ✅ **Maintainable**: Clear code structure and documentation

## 🛡️ Security Features

- ✅ **Secure Defaults**: Production-ready security configurations
- ✅ **Environment Isolation**: Separate dev/prod configurations
- ✅ **Password Generation**: Automatic secure password generation
- ✅ **SSL Support**: Production SSL configuration
- ✅ **Permission Management**: Minimal required permissions

## 🔍 Troubleshooting

### Common Issues
1. **Authentication Failed**: Check credentials in environment files
2. **Database Not Found**: Run setup script or create manually
3. **Connection Refused**: Verify PostgreSQL is running
4. **Permission Denied**: Check user permissions

### Debug Commands
```bash
# Test connection with detailed output
npm run db:test

# Check PostgreSQL status
sudo systemctl status postgresql

# Manual connection test
psql -h localhost -U username -d database

# Docker container status
docker-compose ps
```

## 📚 Documentation

- **[Complete Setup Guide](../backend/docs/DATABASE_SETUP_GUIDE.md)**: Detailed step-by-step instructions
- **[Setup Summary](../backend/docs/COMPLETE_SETUP_SUMMARY.md)**: Quick overview and reference
- **[Entity Examples](backend/src/database/entities/)**: Sample TypeORM entities
- **[Migration Examples](backend/src/database/migrations/)**: Database migration samples

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Test your changes thoroughly
4. Update documentation
5. Submit a pull request

## 📄 License

MIT License - feel free to use in your projects!

## 🙏 Acknowledgments

This automation suite was developed during the setup of the "A Good Man's View" e-commerce platform, demonstrating real-world application and testing of these scripts.

---

**Ready to get started?** Choose your preferred installation method above and have a fully configured NestJS + PostgreSQL application running in minutes! 🚀
