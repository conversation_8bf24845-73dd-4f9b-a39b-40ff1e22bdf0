# Dependency Migration Guide

This document outlines the deprecated dependencies that have been updated and the code changes required.

## Updated Dependencies

### 1. Apollo Server (Backend)

**Changed:** `apollo-server-express` → `@apollo/server`
**Reason:** apollo-server-express is deprecated

**Required Code Changes:**

- Update import statements from `apollo-server-express` to `@apollo/server`
- Update server initialization code to use new Apollo Server 4 syntax
- Update middleware integration for Express

### 2. Redis Cache Store (Backend)

**Changed:** `cache-manager-redis-store` → `cache-manager-redis-yet`
**Reason:** cache-manager-redis-store is deprecated

**Required Code Changes:**

- Update import statements
- Update cache configuration to use new store

### 3. Level Database (Blockchain)

**Changed:** `leveldown` + `levelup` → `level`
**Reason:** leveldown and levelup are deprecated in favor of the unified level package

**Required Code Changes:**

- Update import statements to use `level` instead of separate packages
- Update database initialization code

### 4. <PERSON><PERSON> (Backend)

**Changed:** `m<PERSON>@^2.0.1` → `m<PERSON>@^1.4.5-lts.1`
**Reason:** <PERSON>lter v2 is unstable, v1.4.5-lts.1 is the stable LTS version

### 5. Husky Configuration (Root)

**Changed:** Deprecated hooks configuration → Modern .husky/ directory structure
**Reason:** Husky v4+ uses a different configuration format

**Changes Made:**

- Removed `husky.hooks` from package.json
- Created `.husky/pre-commit` and `.husky/commit-msg` files

### 6. Node.js Engine Requirements

**Changed:** `>=16.0.0` → `>=18.0.0`
**Reason:** Node.js 16 is approaching end-of-life, Node.js 18 is the current LTS

## Next Steps

1. **Install Updated Dependencies:**

   ```bash
   ./scripts/npm-wrapper.sh install
   ```

2. **Update Code for Apollo Server:**

   - Check `backend/src/` for Apollo Server usage
   - Update imports and server initialization

3. **Update Code for Cache Manager:**

   - Check `backend/src/` for Redis cache usage
   - Update store configuration

4. **Update Code for Level Database:**

   - Check `blockchain/src/` for database usage
   - Update imports and initialization

5. **Test All Changes:**
   ```bash
   npm run test
   npm run build
   ```

## Breaking Changes to Watch For

- **Apollo Server 4:** Significant API changes from v3
- **Level:** Different API from levelup/leveldown
- **Cache Manager Redis Yet:** Different configuration options

## Rollback Plan

If issues arise, you can temporarily rollback by reverting the package.json changes and running:

```bash
./scripts/npm-wrapper.sh install
```

However, it's recommended to fix the code to work with the new dependencies as the old ones are deprecated and may have security vulnerabilities.
