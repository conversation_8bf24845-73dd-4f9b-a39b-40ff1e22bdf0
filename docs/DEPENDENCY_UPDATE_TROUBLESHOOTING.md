# Dependency Update Troubleshooting Guide

## Issues Fixed During Update

### 1. Storybook Version Conflicts ✅ FIXED
**Problem:** Mixed Storybook versions (v8.6.14 and v9.0.12) causing peer dependency conflicts.

**Solution Applied:** Standardized all Storybook packages to v8.6.14 in frontend/package.json:
```json
"@storybook/addon-essentials": "^8.6.14",
"@storybook/addon-interactions": "^8.6.14", 
"@storybook/addon-links": "^8.6.14",
"@storybook/blocks": "^8.6.14",
"@storybook/nextjs": "^8.6.14",
"@storybook/react": "^8.6.14"
```

### 2. Missing npm-wrapper.sh Script ✅ FIXED
**Problem:** Package.json referenced non-existent `npm-wrapper.sh` script.

**Solution Applied:** Updated package.json scripts to use existing `setup.sh`:
```json
"setup": "./scripts/setup.sh",
"setup:frontend": "cd frontend && npm install",
"setup:backend": "cd backend && npm install", 
"setup:blockchain": "cd blockchain && npm install"
```

## Installation Commands

### Option 1: Standard Installation
```bash
npm install --legacy-peer-deps
npm run setup
```

### Option 2: Manual Installation (if setup fails)
```bash
# Root dependencies
npm install --legacy-peer-deps

# Individual workspaces
cd shared && npm install --legacy-peer-deps && cd ..
cd frontend && npm install --legacy-peer-deps && cd ..
cd backend && npm install --legacy-peer-deps && cd ..
cd blockchain && npm install --legacy-peer-deps && cd ..
```

### Option 3: Force Installation (last resort)
```bash
npm install --force
npm run setup
```

## Common Issues and Solutions

### Issue: ERESOLVE Dependency Conflicts
**Symptoms:** npm install fails with ERESOLVE errors
**Solution:** Use `--legacy-peer-deps` flag

### Issue: Network Timeouts
**Symptoms:** Installation hangs or times out
**Solutions:**
1. Use different npm registry: `npm install --registry https://registry.npmjs.org/`
2. Increase timeout: `npm install --timeout=300000`
3. Clear npm cache: `npm cache clean --force`

### Issue: React 19 Compatibility
**Symptoms:** Type errors or runtime issues with React components
**Solutions:**
1. Update @types/react and @types/react-dom to v19+
2. Review React 19 breaking changes documentation
3. Update component patterns that use deprecated APIs

### Issue: Next.js 15 Compatibility  
**Symptoms:** Build errors or runtime issues
**Solutions:**
1. Review Next.js 15 upgrade guide
2. Update next.config.js if needed
3. Check for deprecated API usage

### Issue: NestJS 11 Compatibility
**Symptoms:** Decorator errors or module loading issues
**Solutions:**
1. Review NestJS 11 migration guide
2. Update decorator usage patterns
3. Check module imports and exports

### Issue: ESLint 9 Configuration
**Symptoms:** Linting errors or configuration warnings
**Solutions:**
1. Update ESLint configuration to flat config format
2. Review ESLint 9 migration guide
3. Update rule configurations

### Issue: Tailwind CSS 4 Configuration
**Symptoms:** Styling not working or build errors
**Solutions:**
1. Update tailwind.config.js to v4 format
2. Review Tailwind CSS 4 migration guide
3. Update CSS imports and usage

## Testing After Installation

### 1. Verify Installation
```bash
# Check if all packages installed correctly
npm ls --depth=0

# Check workspace packages
npm run setup:frontend
npm run setup:backend
npm run setup:blockchain
```

### 2. Run Tests
```bash
# Run all tests
npm test

# Run individual package tests
npm run test:frontend
npm run test:backend
npm run test:blockchain
```

### 3. Check Build Process
```bash
# Build all packages
npm run build

# Build individual packages
npm run build:frontend
npm run build:backend
npm run build:blockchain
```

### 4. Start Development Environment
```bash
# Start all development servers
npm run dev

# Or start individually
npm run dev:frontend
npm run dev:backend
```

## Rollback Plan

If critical issues arise, you can rollback by:

1. **Restore from Git:** `git checkout HEAD~1 -- package.json frontend/package.json backend/package.json blockchain/package.json shared/package.json`

2. **Reinstall old dependencies:** `npm install`

3. **Selective rollback:** Revert specific packages to previous versions in package.json

## Getting Help

1. **Check logs:** Look in npm debug logs for specific error details
2. **Review documentation:** Check migration guides for major version updates
3. **Community support:** Search GitHub issues for specific packages
4. **Gradual updates:** Consider updating packages incrementally rather than all at once

## Success Indicators

✅ All npm install commands complete without errors
✅ All tests pass
✅ All builds complete successfully  
✅ Development servers start without errors
✅ No runtime errors in browser/application
✅ All linting passes
✅ All TypeScript compilation succeeds

## Performance Monitoring

After successful installation, monitor:
- Application startup time
- Build times
- Test execution time
- Bundle sizes
- Runtime performance

If performance degrades significantly, consider:
- Profiling build process
- Analyzing bundle composition
- Checking for performance regressions in updated packages
- Optimizing configuration for new versions
