# Git Best Practices for Large Repositories

## Overview
This document outlines best practices to keep your Git repository clean, efficient, and focused on essential source code while avoiding large commits caused by build artifacts, dependencies, and temporary files.

## What Was Fixed

### Issues Identified
1. **Build artifacts being tracked**: 192 files including:
   - `backend/dist/` directory with compiled JavaScript and TypeScript declaration files
   - `blockchain/dist/` directory with compiled outputs
   - `frontend/.next/` directory with Next.js build cache and webpack files (some over 20MB)
   - Source maps (*.js.map files)
   - TypeScript declaration files (*.d.ts files)

2. **Large cache files**: Webpack cache files in `.next/cache/` directory
3. **Missing comprehensive .gitignore patterns**

### Actions Taken
1. **Updated .gitignore** with comprehensive patterns for:
   - All build outputs (`dist/`, `build/`, `.next/`)
   - Node.js dependencies (`node_modules/`)
   - Cache directories (`.cache/`, `.webpack/`, `.turbo/`)
   - TypeScript build artifacts (`*.tsbuildinfo`, `*.d.ts`, `*.js.map`)
   - Environment files and editor configurations
   - OS-specific files (`.DS_Store`, `Thumbs.db`)

2. **Removed tracked build artifacts**: Used `git rm --cached` to untrack 192 files without deleting them locally

## Git Best Practices

### 1. What Should Be Tracked
✅ **DO track these files:**
- Source code (`.ts`, `.tsx`, `.js`, `.jsx`)
- Configuration files (`package.json`, `tsconfig.json`, `next.config.js`)
- Documentation (`.md` files)
- Environment examples (`.env.example`)
- Database schemas and migrations
- Test files
- Static assets (images, fonts) that are part of the application

### 2. What Should NOT Be Tracked
❌ **DON'T track these files:**
- Build outputs (`dist/`, `build/`, `.next/`)
- Dependencies (`node_modules/`)
- Cache directories (`.cache/`, `.webpack/`)
- Compiled files (`*.js.map`, `*.d.ts` from TypeScript compilation)
- Environment files with secrets (`.env`, `.env.local`)
- Editor/IDE files (`.vscode/`, `.idea/`)
- OS files (`.DS_Store`, `Thumbs.db`)
- Log files (`*.log`)
- Temporary files

### 3. Repository Structure Best Practices

```
project/
├── .gitignore              # Comprehensive ignore patterns
├── README.md              # Project documentation
├── package.json           # Dependencies and scripts
├── tsconfig.json          # TypeScript configuration
├── backend/
│   ├── src/              # Source code only
│   ├── package.json      # Backend dependencies
│   └── dist/             # ❌ Build output (ignored)
├── frontend/
│   ├── src/              # Source code only
│   ├── package.json      # Frontend dependencies
│   └── .next/            # ❌ Next.js build (ignored)
├── docs/                 # Documentation
└── scripts/              # Utility scripts
```

### 4. Commit Guidelines

#### Good Commit Practices
- **Small, focused commits**: Each commit should represent one logical change
- **Descriptive messages**: Use clear, descriptive commit messages
- **Regular commits**: Commit frequently to avoid large changesets
- **Review before committing**: Use `git status` and `git diff` to review changes

#### Example Good Commits
```bash
git add src/components/Button.tsx
git commit -m "feat: add reusable Button component with variants"

git add docs/API.md
git commit -m "docs: update API documentation for user endpoints"
```

#### Bad Commit Examples
```bash
# ❌ Too many unrelated changes
git add .
git commit -m "fix stuff"

# ❌ Including build artifacts
git add dist/ src/
git commit -m "update features and build"
```

### 5. Pre-Commit Checklist

Before each commit, verify:
- [ ] Only source code and configuration files are staged
- [ ] No build artifacts (`dist/`, `.next/`, `*.js.map`) are included
- [ ] No sensitive information (API keys, passwords) is included
- [ ] No large files (>10MB) are being added
- [ ] Commit message is descriptive and follows conventions

### 6. Useful Git Commands

#### Check what's being committed
```bash
git status                    # See staged and unstaged changes
git diff --cached            # See exactly what will be committed
git ls-files | grep -E '\.(pack|js\.map|d\.ts)$'  # Check for build artifacts
```

#### Clean up accidentally tracked files
```bash
git rm --cached filename     # Remove from tracking, keep locally
git rm -r --cached directory/  # Remove directory from tracking
```

#### Check repository size
```bash
git count-objects -vH        # See repository size and object count
du -sh .git/                 # Check .git directory size
```

### 7. Automated Solutions

#### Pre-commit Hooks
Consider setting up pre-commit hooks to automatically:
- Run linting and formatting
- Check for large files
- Verify no build artifacts are being committed
- Run tests

#### Example pre-commit hook (`.git/hooks/pre-commit`):
```bash
#!/bin/sh
# Check for build artifacts
if git diff --cached --name-only | grep -E '\.(pack|js\.map|d\.ts)$'; then
    echo "Error: Build artifacts detected in commit"
    exit 1
fi

# Check for large files
if git diff --cached --name-only | xargs ls -la | awk '$5 > 10485760 {print $9}'; then
    echo "Error: Large files detected (>10MB)"
    exit 1
fi
```

### 8. Repository Maintenance

#### Regular Cleanup
```bash
# Clean up untracked files
git clean -fd

# Remove remote tracking branches that no longer exist
git remote prune origin

# Garbage collect to optimize repository
git gc --aggressive
```

#### Monitor Repository Health
- Regularly check repository size: `du -sh .git/`
- Monitor commit sizes: `git log --oneline --stat`
- Review what's being tracked: `git ls-files | wc -l`

## Emergency Recovery

If you accidentally commit large files:

1. **Remove from latest commit** (if not pushed):
```bash
git reset --soft HEAD~1
git rm --cached large-file
git commit -m "remove large file"
```

2. **Remove from history** (if already pushed - use with caution):
```bash
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch path/to/large-file' \
--prune-empty --tag-name-filter cat -- --all
```

## Conclusion

Following these practices will:
- Keep your repository size manageable
- Speed up clone and fetch operations
- Focus commits on meaningful code changes
- Improve collaboration and code review processes
- Prevent accidental exposure of sensitive information

Remember: **When in doubt, don't commit it!** It's easier to add files later than to remove them from Git history.
