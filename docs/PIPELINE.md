# 🚀 CI/CD Pipeline Documentation

## 📋 Overview

This document provides comprehensive documentation for the A Good Man's View e-commerce platform CI/CD pipeline. Our pipeline implements modern DevOps practices with a focus on quality, security, and educational value.

## 🏗️ Pipeline Architecture

### Visual Pipeline Flow

```mermaid
graph TD
    A[Code Push/PR] --> B[🔧 Setup & Validation]
    B --> C[🧪 Unit Tests]
    B --> D[🔗 Integration Tests]
    B --> E[🎭 E2E Tests]
    B --> F[⚡ Performance Tests]
    B --> G[♿ Accessibility Tests]
    B --> H[🔒 Security Tests]
    
    C --> I[🏗️ Build Verification]
    D --> I
    E --> I
    F --> I
    G --> I
    H --> I
    
    I --> J[✅ CI Summary]
    J --> K{Main Branch?}
    K -->|Yes| L[🚀 Deploy to Staging]
    K -->|No| M[End]
    
    L --> N[🏥 Health Checks]
    N --> O{Manual Approval}
    O -->|Approved| P[🎯 Deploy to Production]
    O -->|Rejected| Q[End]
    
    P --> R[📊 Post-deployment Monitoring]
```

## 🧪 Continuous Integration (CI)

### Pipeline Triggers
- **Pull Requests**: All PRs to `main` and `develop` branches
- **Push Events**: Direct pushes to `main` and `develop` branches
- **Manual Dispatch**: For testing and debugging purposes

### Job Structure

#### 1. 🔧 Setup & Validation
**Purpose**: Validates environment and detects changes
**Duration**: ~10 minutes
**Key Features**:
- Change detection for monorepo optimization
- Dependency installation with caching
- Security vulnerability scanning
- Project structure validation

```yaml
# Example change detection configuration
filters: |
  frontend:
    - 'frontend/**'
    - 'shared/**'
  backend:
    - 'backend/**'
    - 'shared/**'
```

#### 2. 🧪 Unit Tests
**Purpose**: Tests individual components and functions
**Duration**: ~15 minutes
**Strategy**: Parallel execution across workspaces
**Coverage**: Minimum 80% code coverage required

**Workspaces Tested**:
- Frontend (Vitest + React Testing Library)
- Backend (Jest + NestJS Testing)
- Blockchain (Custom test suite)
- Shared (Jest for utilities)

#### 3. 🔗 Integration Tests
**Purpose**: Tests component interactions and data flow
**Duration**: ~20 minutes
**Infrastructure**: PostgreSQL + Redis test containers

**Test Categories**:
- API endpoint integration
- Database operations
- External service mocking
- Cross-workspace communication

#### 4. 🎭 End-to-End Tests
**Purpose**: Tests complete user workflows
**Duration**: ~30 minutes
**Tool**: Playwright with multi-browser support

**Browser Matrix**:
- Chromium (Desktop)
- Firefox (Desktop)
- WebKit (Safari simulation)
- Mobile Chrome (Responsive testing)
- Mobile Safari (iOS simulation)

**Test Scenarios**:
- User registration and authentication
- Product browsing and search
- Shopping cart and checkout flow
- Vendor dashboard operations
- Payment processing workflows

#### 5. ⚡ Performance Tests
**Purpose**: Ensures optimal application performance
**Duration**: ~15 minutes
**Tools**: Lighthouse CI + Bundle Analyzer

**Metrics Monitored**:
- First Contentful Paint (FCP) < 2s
- Largest Contentful Paint (LCP) < 3s
- Cumulative Layout Shift (CLS) < 0.1
- Bundle size limits and optimization

#### 6. ♿ Accessibility Tests
**Purpose**: Ensures WCAG 2.1 AA compliance
**Duration**: ~15 minutes
**Tools**: axe-core + pa11y

**Standards Tested**:
- WCAG 2.1 Level AA compliance
- Keyboard navigation support
- Screen reader compatibility
- Color contrast requirements
- Focus management

#### 7. 🔒 Security Tests
**Purpose**: Identifies security vulnerabilities
**Duration**: ~15 minutes
**Tools**: CodeQL + npm audit

**Security Checks**:
- Dependency vulnerability scanning
- Static code analysis
- Secret detection
- SQL injection prevention
- XSS vulnerability detection

#### 8. 🏗️ Build Verification
**Purpose**: Ensures all components build successfully
**Duration**: ~20 minutes
**Strategy**: Parallel builds with artifact upload

**Build Outputs**:
- Frontend: Next.js optimized build
- Backend: NestJS compiled TypeScript
- Blockchain: Compiled smart contracts
- Shared: Distributed packages

## 🚀 Continuous Deployment (CD)

### Deployment Strategy

#### Environment Promotion Flow
```
Development → Staging → Production
     ↓           ↓         ↓
   Feature    Integration  Live
   Testing     Testing    Users
```

#### Deployment Triggers
- **Automatic**: Successful CI on `main` branch → Staging
- **Manual**: Workflow dispatch for any environment
- **Emergency**: Skip tests flag for critical hotfixes

### Deployment Environments

#### 🧪 Staging Environment
**Purpose**: Pre-production testing and validation
**URL**: https://staging.agoodmansview.com
**Database**: Staging PostgreSQL instance
**Features**:
- Automatic deployment from `main` branch
- Full feature testing environment
- Performance monitoring
- User acceptance testing

#### 🎯 Production Environment
**Purpose**: Live user-facing application
**URL**: https://agoodmansview.com
**Database**: Production PostgreSQL cluster
**Features**:
- Manual approval required
- Blue-green deployment strategy
- Comprehensive health checks
- Automatic rollback on failure

### Database Migration Strategy

#### Migration Process
1. **Backup**: Automatic database backup (production only)
2. **Validation**: Schema validation and conflict detection
3. **Execution**: Sequential migration execution
4. **Verification**: Post-migration data integrity checks
5. **Rollback**: Automatic rollback on failure

#### Safety Measures
- Zero-downtime migrations when possible
- Backward compatibility requirements
- Migration testing in staging first
- Rollback scripts for all migrations

## 🛠️ Local Development Testing

### Prerequisites
```bash
# Required software
node >= 18.0.0
npm >= 9.0.0
docker >= 20.0.0
docker-compose >= 2.0.0
```

### Setup Commands
```bash
# Clone and setup
git clone https://github.com/yourusername/agoodmansview_website.git
cd agoodmansview_website
npm run setup

# Start development environment
npm run dev

# Run full test suite locally
npm run test
```

### Testing Commands

#### Unit Tests
```bash
# Run all unit tests
npm run test:unit

# Run specific workspace tests
npm run test:frontend
npm run test:backend
npm run test:blockchain

# Watch mode for development
cd frontend && npm run test:watch
```

#### Integration Tests
```bash
# Start test database
docker-compose up -d postgres redis

# Run integration tests
npm run test:integration

# Clean up
docker-compose down
```

#### End-to-End Tests
```bash
# Install browsers
cd frontend && npx playwright install

# Run E2E tests
npm run test:e2e

# Run with UI mode
npm run test:e2e:ui

# Run specific browser
npx playwright test --project=chromium
```

#### Performance Tests
```bash
# Build and analyze
npm run build:analyze

# Run Lighthouse audit
npm run lighthouse

# Bundle size analysis
npm run bundle-analyzer
```

#### Accessibility Tests
```bash
# Install tools
npm install -g @axe-core/cli pa11y

# Run accessibility audit
npm run a11y:test

# Generate accessibility report
npm run a11y:report
```

## 🔧 Configuration Files

### Key Configuration Files
- `.github/workflows/ci.yml` - CI pipeline configuration
- `.github/workflows/cd.yml` - CD pipeline configuration
- `frontend/.lighthouserc.json` - Performance testing config
- `frontend/playwright.config.ts` - E2E testing config
- `frontend/vitest.config.ts` - Unit testing config

### Environment Variables

#### CI/CD Variables
```bash
# GitHub Secrets (required)
STAGING_HOST=staging.agoodmansview.com
STAGING_USER=deploy
STAGING_SSH_KEY=<private-key>
STAGING_DATABASE_URL=postgresql://...
STAGING_REDIS_URL=redis://...
STAGING_JWT_SECRET=<secret>

PRODUCTION_HOST=agoodmansview.com
PRODUCTION_USER=deploy
PRODUCTION_SSH_KEY=<private-key>
PRODUCTION_DATABASE_URL=postgresql://...
PRODUCTION_REDIS_URL=redis://...
PRODUCTION_JWT_SECRET=<secret>
```

#### Local Development
```bash
# .env.local (frontend)
NEXT_PUBLIC_API_URL=http://localhost:4000
NEXT_PUBLIC_GRAPHQL_URL=http://localhost:4000/graphql

# .env (backend)
DATABASE_URL=postgresql://postgres:password@localhost:5432/agoodmansview_dev
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-local-jwt-secret
```

## 📊 Monitoring and Metrics

### Pipeline Metrics
- **Build Success Rate**: Target > 95%
- **Test Coverage**: Target > 80%
- **Deployment Frequency**: Daily to staging, weekly to production
- **Mean Time to Recovery**: Target < 1 hour

### Performance Metrics
- **Lighthouse Score**: Target > 90
- **Bundle Size**: Monitor and alert on increases
- **Load Time**: Target < 3 seconds
- **Core Web Vitals**: All metrics in "Good" range

### Quality Metrics
- **Security Vulnerabilities**: Zero high/critical
- **Accessibility Score**: Target > 95%
- **Code Quality**: SonarQube grade A
- **Technical Debt**: Monitor and reduce monthly

## 🚨 Troubleshooting Guide

### Common Issues and Solutions

#### CI Pipeline Failures

**Issue**: Unit tests failing
```bash
# Solution: Run tests locally first
npm run test:unit
# Check for environment differences
# Update test snapshots if needed
npm run test:update-snapshots
```

**Issue**: E2E tests timing out
```bash
# Solution: Increase timeout or check selectors
# Update playwright.config.ts
timeout: 60000 // Increase timeout
# Use more specific selectors
await page.locator('[data-testid="submit-button"]').click()
```

**Issue**: Build failures
```bash
# Solution: Check TypeScript errors
npm run type-check
# Clear cache and rebuild
npm run clean && npm run build
```

#### Deployment Issues

**Issue**: Database migration failures
```bash
# Solution: Check migration scripts
npm run migration:show
# Rollback if needed
npm run migration:revert
# Fix migration and retry
```

**Issue**: Health check failures
```bash
# Solution: Check application logs
docker-compose logs -f
# Verify environment variables
# Check database connectivity
```

### Emergency Procedures

#### Rollback Deployment
```bash
# Automatic rollback (if health checks fail)
# Manual rollback via GitHub Actions
# Revert to previous Docker images
docker-compose down
docker-compose up -d --scale app=0
docker-compose up -d
```

#### Hotfix Deployment
```bash
# Create hotfix branch
git checkout -b hotfix/critical-fix main
# Make minimal changes
# Deploy with skip tests flag (emergency only)
# Monitor closely and follow up with proper testing
```

## 📚 Best Practices

### Code Quality
- Write tests before implementing features (TDD)
- Maintain high test coverage (>80%)
- Use TypeScript strict mode
- Follow ESLint and Prettier configurations
- Document complex business logic

### Security
- Never commit secrets to version control
- Use environment variables for configuration
- Regularly update dependencies
- Implement proper authentication and authorization
- Conduct security reviews for sensitive changes

### Performance
- Monitor bundle size and optimize regularly
- Implement proper caching strategies
- Use lazy loading for non-critical components
- Optimize images and assets
- Monitor Core Web Vitals

### Deployment
- Test thoroughly in staging before production
- Use feature flags for gradual rollouts
- Implement proper monitoring and alerting
- Have rollback procedures ready
- Document all deployment procedures

## 🔄 Pipeline Evolution

### Current State (v1.0)
- Basic CI/CD with comprehensive testing
- Docker-based deployment
- Manual production approvals
- Basic monitoring

### Planned Improvements (v2.0)
- Advanced deployment strategies (canary, blue-green)
- Automated performance regression detection
- Enhanced security scanning
- Infrastructure as Code (Terraform)
- Advanced monitoring and alerting

### Future Enhancements (v3.0)
- Machine learning-based test optimization
- Predictive failure detection
- Automated dependency updates
- Advanced analytics and insights
- Multi-region deployment support

---

## 📞 Support and Contact

For pipeline issues or questions:
- **GitHub Issues**: Create an issue with the `ci/cd` label
- **Documentation**: Check this guide and inline comments
- **Team Chat**: #devops channel in team Slack
- **Emergency**: Contact DevOps team lead directly

---

*This documentation is maintained by the DevOps team and updated with each pipeline enhancement.*
