# A Good Man's View - Documentation

Welcome to the comprehensive documentation for the A Good Man's View e-commerce platform.

## 📚 Documentation Structure

### 🔧 API Documentation
- **[GraphQL API](./api/graphql/)** - GraphQL schema, queries, mutations, and subscriptions
- **[REST API](./api/rest/)** - RESTful endpoints documentation

### 🏗️ Architecture
- **[Database Schema](./architecture/database-schema.md)** - Entity relationships and database design
- **[API Design](./architecture/api-design.md)** - Hybrid GraphQL + REST architecture
- **[Blockchain Design](./architecture/blockchain-design.md)** - Private blockchain implementation

### 🚀 Deployment
- **[Development Setup](./deployment/development.md)** - Local development environment
- **[Staging Deployment](./deployment/staging.md)** - Staging environment setup
- **[Production Deployment](./deployment/production.md)** - Production deployment guide

### 👥 User Guides
- **[Buyer Guide](./user-guides/buyer-guide.md)** - How to use the platform as a buyer
- **[Vendor Guide](./user-guides/vendor-guide.md)** - How to manage your store as a vendor

## 🚀 Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/agoodmansview_website.git
   cd agoodmansview_website
   ```

2. **Run the setup script**
   ```bash
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   ```

3. **Start development environment**
   ```bash
   npm run dev
   ```

## 🛠️ Technology Stack

- **Frontend**: Next.js 14+ with TypeScript
- **Backend**: NestJS with TypeScript
- **Database**: PostgreSQL with TypeORM
- **Blockchain**: Custom implementation with Proof of Stake
- **API**: Hybrid GraphQL + REST architecture
- **Caching**: Redis
- **Containerization**: Docker & Docker Compose
- **Orchestration**: Kubernetes
- **Infrastructure**: Terraform


## 📋 Development Workflow

1. **Feature Development**
   - Create feature branch from `develop`
   - Implement feature with tests
   - Submit pull request

2. **Code Quality**
   - TypeScript strict mode
   - ESLint + Prettier
   - Comprehensive testing
   - Code coverage requirements

3. **Deployment Pipeline**
   - Automated CI/CD with GitHub Actions
   - Staging deployment for testing
   - Production deployment with approval

## 🤝 Contributing

Please read our [Contributing Guidelines](../CONTRIBUTING.md) before submitting pull requests.

## 📞 Support

- **Email**: <EMAIL>
- **Documentation Issues**: Create an issue in this repository
- **Feature Requests**: Use the feature request template

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.
