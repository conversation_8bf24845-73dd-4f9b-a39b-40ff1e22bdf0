# Sharp Installation Fix Guide

## Problem
The `sharp` package installation is failing with timeout errors when trying to download the libvips binary. This is a common issue with Sharp, especially in environments with slow or unreliable internet connections.

## Root Cause
- Next.js automatically tries to install Sharp for optimized image processing
- Sharp requires downloading large binary files (libvips) during installation
- Network timeouts are causing the download to fail

## Solutions Implemented

### 1. NPM Configuration (.npmrc files)
Created `.npmrc` files with optimized settings:
- Increased timeout values (300 seconds)
- Added retry configuration
- Configured Sharp-specific download hosts

### 2. Next.js Configuration Update
Modified `frontend/next.config.js` to:
- Keep image optimization enabled
- Allow fallback to built-in optimization if <PERSON> fails

### 3. Automated Fix Script
Created `scripts/fix-sharp.sh` that tries multiple approaches:
1. Install Sharp with optimized configuration
2. Install Sharp using prebuild binaries
3. Configure Next.js to use fallback image optimization

## How to Use

### Quick Fix
Run the automated fix script:
```bash
./scripts/fix-sharp.sh
```

### Manual Steps

#### Option 1: Install with Configuration
```bash
# Set environment variables
export SHARP_IGNORE_GLOBAL_LIBVIPS=1
export npm_config_sharp_binary_host="https://github.com/lovell/sharp-libvips/releases/download/"

# Install with extended timeout
npm install sharp --timeout=300000 --fetch-timeout=300000 --fetch-retries=5
```

#### Option 2: Use Prebuild Binaries
```bash
# Install prebuild-install first
npm install prebuild-install

# Install Sharp without building from source
npm install sharp --build-from-source=false --timeout=300000
```

#### Option 3: Use Alternative Registry
```bash
# Use npm mirror (if available)
npm install sharp --registry=https://registry.npmmirror.com/
```

### Fallback Solution
If Sharp installation continues to fail, the project is configured to use Next.js built-in image optimization:
- Slightly slower than Sharp
- No additional dependencies required
- Fully functional image optimization

## Verification

### Check if Sharp is Working
```bash
# In the frontend directory
cd frontend
node -e "try { require('sharp'); console.log('Sharp is available'); } catch(e) { console.log('Sharp not available, using fallback'); }"
```

### Test Next.js Build
```bash
cd frontend
npm run build
```

## Prevention

### For Future Installations
1. Always use the provided `.npmrc` configuration
2. Consider using the fix script before running `npm install`
3. Monitor network connectivity during installations

### Alternative Approaches
1. **Docker**: Use containerized builds with pre-installed Sharp
2. **CI/CD**: Cache Sharp binaries in build pipelines
3. **Local Mirror**: Set up local npm registry mirror

## Troubleshooting

### If the Fix Script Fails
1. Check internet connectivity
2. Try using a VPN or different network
3. Use the fallback configuration (Option 3 in the script)

### If Next.js Build Fails
1. Clear node_modules and reinstall
2. Use the fallback Next.js configuration
3. Check for other dependency conflicts

### Performance Considerations
- Built-in Next.js optimization is ~20% slower than Sharp
- For production, consider using Docker with pre-installed Sharp
- Monitor image optimization performance in production

## Files Modified
- `frontend/next.config.js` - Updated image configuration
- `frontend/.npmrc` - NPM configuration for frontend
- `.npmrc` - Root NPM configuration
- `scripts/fix-sharp.sh` - Automated fix script

## Additional Resources
- [Sharp Installation Guide](https://sharp.pixelplumbing.com/install)
- [Next.js Image Optimization](https://nextjs.org/docs/basic-features/image-optimization)
- [NPM Configuration](https://docs.npmjs.com/cli/v7/configuring-npm/npmrc)
