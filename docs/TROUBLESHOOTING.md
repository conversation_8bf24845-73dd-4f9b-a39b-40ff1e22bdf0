# 🔧 CI/CD Pipeline Troubleshooting Guide

## 📋 Overview

This guide provides solutions to common issues encountered in the A Good Man's View CI/CD pipeline. Each section includes symptoms, root causes, and step-by-step solutions.

## 🚨 Common CI Pipeline Issues

### 1. Unit Test Failures

#### **Symptom**: Tests pass locally but fail in CI
```bash
❌ Test suite failed to run
TypeError: Cannot read property 'xxx' of undefined
```

#### **Root Causes**:
- Environment differences between local and CI
- Missing environment variables
- Timing issues in async tests
- Different Node.js versions

#### **Solutions**:

**Check Environment Variables**:
```bash
# Verify environment variables in CI
echo "NODE_ENV: $NODE_ENV"
echo "CI: $CI"

# Add missing variables to GitHub Secrets
# Settings → Secrets and variables → Actions
```

**Fix Timing Issues**:
```typescript
// ❌ Bad: Hardcoded timeouts
await new Promise(resolve => setTimeout(resolve, 1000));

// ✅ Good: Use waitFor utilities
await waitFor(() => expect(element).toBeInTheDocument());

// ✅ Good: Increase timeout for slow operations
test('slow operation', async () => {
  // ...
}, 10000); // 10 second timeout
```

**Standardize Node.js Version**:
```json
// package.json
{
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=9.0.0"
  }
}
```

### 2. E2E Test Failures

#### **Symptom**: Playwright tests timeout or fail to find elements
```bash
❌ Test timeout of 30000ms exceeded
❌ Locator '[data-testid="submit-button"]' not found
```

#### **Root Causes**:
- Application not fully loaded
- Incorrect selectors
- Network delays
- Browser compatibility issues

#### **Solutions**:

**Improve Element Selection**:
```typescript
// ❌ Bad: Fragile selectors
await page.click('button');
await page.click('.submit-btn');

// ✅ Good: Stable data-testid selectors
await page.click('[data-testid="submit-button"]');

// ✅ Good: Wait for element to be ready
await page.waitForSelector('[data-testid="submit-button"]');
await page.click('[data-testid="submit-button"]');
```

**Handle Async Operations**:
```typescript
// ✅ Wait for network requests
await page.waitForResponse(response => 
  response.url().includes('/api/products') && response.status() === 200
);

// ✅ Wait for navigation
await Promise.all([
  page.waitForNavigation(),
  page.click('[data-testid="checkout-button"]')
]);
```

**Increase Timeouts for Slow Operations**:
```typescript
// playwright.config.ts
export default defineConfig({
  timeout: 60000, // 60 seconds
  expect: {
    timeout: 10000 // 10 seconds for assertions
  }
});
```

### 3. Build Failures

#### **Symptom**: TypeScript compilation errors in CI
```bash
❌ Type 'string | undefined' is not assignable to type 'string'
❌ Cannot find module '@/components/Button'
```

#### **Root Causes**:
- TypeScript strict mode differences
- Missing type definitions
- Path resolution issues
- Dependency version mismatches

#### **Solutions**:

**Fix Type Issues**:
```typescript
// ❌ Bad: Assuming values exist
const userName = user.name.toUpperCase();

// ✅ Good: Handle undefined values
const userName = user.name?.toUpperCase() ?? 'Unknown';

// ✅ Good: Type guards
if (user.name) {
  const userName = user.name.toUpperCase();
}
```

**Fix Path Resolution**:
```json
// tsconfig.json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"]
    }
  }
}
```

**Lock Dependency Versions**:
```bash
# Use exact versions for critical dependencies
npm install --save-exact react@18.2.0

# Commit package-lock.json
git add package-lock.json
git commit -m "Lock dependency versions"
```

### 4. Performance Test Failures

#### **Symptom**: Lighthouse scores below thresholds
```bash
❌ Performance score: 65 (threshold: 90)
❌ First Contentful Paint: 4.2s (threshold: 2s)
```

#### **Root Causes**:
- Large bundle sizes
- Unoptimized images
- Blocking JavaScript
- Missing caching headers

#### **Solutions**:

**Optimize Bundle Size**:
```javascript
// next.config.js
module.exports = {
  experimental: {
    optimizeCss: true,
  },
  webpack: (config) => {
    config.optimization.splitChunks = {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    };
    return config;
  },
};
```

**Optimize Images**:
```typescript
// Use Next.js Image component
import Image from 'next/image';

// ✅ Good: Optimized images
<Image
  src="/product.jpg"
  alt="Product"
  width={300}
  height={200}
  priority={isAboveFold}
/>
```

**Implement Code Splitting**:
```typescript
// ✅ Good: Lazy load components
const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <Spinner />,
});
```

### 5. Security Test Failures

#### **Symptom**: Security vulnerabilities detected
```bash
❌ High severity vulnerability in package 'xyz'
❌ Potential XSS vulnerability detected
```

#### **Root Causes**:
- Outdated dependencies
- Insecure coding practices
- Missing security headers
- Exposed sensitive data

#### **Solutions**:

**Update Dependencies**:
```bash
# Check for vulnerabilities
npm audit

# Fix automatically
npm audit fix

# Update specific packages
npm update package-name

# For breaking changes, update manually
npm install package-name@latest
```

**Implement Security Headers**:
```javascript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};
```

**Sanitize User Input**:
```typescript
import DOMPurify from 'dompurify';

// ✅ Good: Sanitize HTML content
const sanitizedHTML = DOMPurify.sanitize(userInput);

// ✅ Good: Validate input
const schema = z.object({
  email: z.string().email(),
  name: z.string().min(1).max(100),
});
```

## 🚀 Deployment Issues

### 1. Database Migration Failures

#### **Symptom**: Migration fails during deployment
```bash
❌ Migration failed: relation "users" already exists
❌ Connection timeout to database
```

#### **Solutions**:

**Check Migration Order**:
```bash
# View migration status
npm run migration:show

# Revert problematic migration
npm run migration:revert

# Fix migration file and re-run
npm run migration:run
```

**Handle Connection Issues**:
```bash
# Test database connectivity
npm run db:test-connection

# Check environment variables
echo $DATABASE_URL

# Verify database is accessible
pg_isready -h $DB_HOST -p $DB_PORT
```

### 2. Docker Build Failures

#### **Symptom**: Docker image build fails
```bash
❌ npm ERR! Cannot resolve dependency
❌ COPY failed: no such file or directory
```

#### **Solutions**:

**Fix Dockerfile Issues**:
```dockerfile
# ✅ Good: Multi-stage build
FROM node:18-alpine AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY --from=builder /app/.next ./.next
COPY package*.json ./
CMD ["npm", "start"]
```

**Optimize Docker Build**:
```dockerfile
# Use .dockerignore
node_modules
.git
.next
coverage
test-results
```

### 3. Health Check Failures

#### **Symptom**: Deployment health checks fail
```bash
❌ Health check failed: HTTP 500
❌ Service not responding after 60 seconds
```

#### **Solutions**:

**Implement Proper Health Checks**:
```typescript
// pages/api/health.ts
export default function handler(req, res) {
  try {
    // Check database connection
    await db.query('SELECT 1');
    
    // Check external services
    await fetch(process.env.EXTERNAL_API_URL);
    
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version,
    });
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
    });
  }
}
```

**Configure Proper Timeouts**:
```yaml
# docker-compose.yml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 60s
```

## 🛠️ Debugging Strategies

### 1. Local Pipeline Testing

```bash
# Run complete pipeline locally
./scripts/test-pipeline.sh --full

# Run specific test types
./scripts/test-pipeline.sh --quick
npm run test:unit
npm run test:e2e

# Debug with verbose output
DEBUG=* npm run test
npm run test -- --verbose
```

### 2. CI Debugging

**Enable Debug Logging**:
```yaml
# .github/workflows/ci.yml
env:
  DEBUG: '*'
  ACTIONS_STEP_DEBUG: true
  ACTIONS_RUNNER_DEBUG: true
```

**Add Debug Steps**:
```yaml
- name: 🐛 Debug Environment
  run: |
    echo "Node version: $(node --version)"
    echo "NPM version: $(npm --version)"
    echo "Working directory: $(pwd)"
    echo "Environment variables:"
    env | grep -E '^(NODE_|NPM_|CI)' | sort
```

### 3. Performance Debugging

**Analyze Bundle Size**:
```bash
# Generate bundle analysis
npm run build:analyze

# Check specific chunks
npx webpack-bundle-analyzer .next/static/chunks/*.js
```

**Profile Test Performance**:
```bash
# Profile Jest tests
npm run test -- --detectOpenHandles --forceExit

# Profile Playwright tests
npx playwright test --trace on
```

## 📞 Getting Help

### 1. Check Logs
- **GitHub Actions**: Check workflow logs in Actions tab
- **Local**: Use `--verbose` flags for detailed output
- **Docker**: `docker-compose logs -f service-name`

### 2. Common Resources
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Playwright Documentation](https://playwright.dev/)
- [Jest Documentation](https://jestjs.io/)
- [Next.js Documentation](https://nextjs.org/docs)

### 3. Team Support
- **GitHub Issues**: Create issue with `ci/cd` label
- **Documentation**: Check [PIPELINE.md](./PIPELINE.md)
- **Emergency**: Contact DevOps team lead

## 🔄 Prevention Strategies

### 1. Pre-commit Hooks
```bash
# Install husky for git hooks
npm install --save-dev husky

# Setup pre-commit hook
npx husky add .husky/pre-commit "npm run lint && npm run test:quick"
```

### 2. Regular Maintenance
```bash
# Weekly dependency updates
npm run deps:check
npm run deps:update

# Monthly security audits
npm audit
npm run security:scan
```

### 3. Monitoring
- Set up alerts for pipeline failures
- Monitor performance metrics
- Track deployment frequency and success rates

---

*This troubleshooting guide is maintained by the DevOps team and updated based on common issues encountered.*
