# Database Schema Design

## 🗄️ Complete Normalized Schema for A Good Man's View

This document provides the complete database schema design for Issue #62, implementing a robust, scalable, and normalized database structure for the multi-vendor e-commerce platform.

## 📐 Schema Design Principles

### Normalization Level: Third Normal Form (3NF)
- **Eliminates data redundancy**
- **Ensures data integrity**
- **Optimizes storage efficiency**
- **Maintains referential consistency**

### Design Goals
1. **Scalability**: Support 100K+ users and 500K+ products
2. **Performance**: Sub-second query response times
3. **Security**: Role-based access and data protection
4. **Flexibility**: Adaptable to changing business needs
5. **Compliance**: POPIA and international standards

## 🏗️ Core Schema Structure

### 👥 User Management Schema

#### **users**
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role user_role_enum NOT NULL DEFAULT 'buyer',
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    phone VARCHAR(20),
    date_of_birth DATE,
    email_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

**Indexes:**
- `idx_users_email` (UNIQUE)
- `idx_users_role`
- `idx_users_active`

#### **user_addresses**
```sql
CREATE TABLE user_addresses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type address_type_enum NOT NULL, -- 'home', 'work/office', 'both'
    street_address VARCHAR(255) NOT NULL,
    suburb VARCHAR(100),
    city VARCHAR(100) NOT NULL,
    province VARCHAR(50) NOT NULL,
    postal_code VARCHAR(10) NOT NULL,
    country VARCHAR(2) DEFAULT 'ZA',
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Address Types:**
- `home` - Personal/residential address
- `work/office` - Business/office address
- `both` - Address used for both home and work purposes

#### **vendors**
```sql
CREATE TABLE vendors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    business_name VARCHAR(255) NOT NULL,
    artist_bio TEXT,
    verification_status vendor_status_enum DEFAULT 'pending',
    verification_date TIMESTAMP WITH TIME ZONE,
    verification_notes TEXT,
    store_logo_url VARCHAR(500),
    store_banner_url VARCHAR(500),
    store_layout VARCHAR(50) DEFAULT 'grid',
    store_color_scheme JSONB,
    contact_email VARCHAR(255),
    current_package subscription_package_enum DEFAULT 'standard',
    commission_rate DECIMAL(5,4) DEFAULT 0.0000,
    portfolio_storage_used BIGINT DEFAULT 0,
    portfolio_storage_limit BIGINT DEFAULT 1073741824, -- 1GB
    can_sell_products BOOLEAN DEFAULT FALSE,
    can_join_competitions BOOLEAN DEFAULT FALSE,
    has_priority_registration BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Store Customization:**
- `store_logo_url` - Artist's logo for branding
- `store_banner_url` - Header banner image
- `store_layout` - Layout options: grid, list, masonry, carousel
- `store_color_scheme` - Custom color palette (JSON)
- `contact_email` - Artist's contact email for customers

**Subscription Packages:**
- `standard` - R99/month: Portfolio only, no selling, no competitions
- `premium` - R250/month: Portfolio + Shop + Competitions (20% commission)
- `platinum` - R500/month: All features + Priority + More storage (15% commission)

#### **subscriptions**
```sql
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    package_type subscription_package_enum NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    monthly_price DECIMAL(8,2) NOT NULL,
    commission_rate DECIMAL(5,4) NOT NULL,
    storage_limit BIGINT NOT NULL,
    features JSONB,
    payment_reference VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Package Pricing:**
- Standard: R99/month, 0% commission, 1GB storage
- Premium: R250/month, 20% commission, 2GB storage
- Platinum: R500/month, 15% commission, 5GB storage

### 🛍️ Product Catalog Schema

#### **categories**
```sql
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES categories(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **products**
```sql
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    sku VARCHAR(100) UNIQUE,
    price DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ZAR',
    cost_price DECIMAL(12,2),
    stock_quantity INTEGER DEFAULT 0,
    low_stock_threshold INTEGER DEFAULT 10,
    weight DECIMAL(8,3),
    dimensions JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    auto_discount_enabled BOOLEAN DEFAULT TRUE,
    meta_title VARCHAR(255),
    meta_description VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

**Automatic Discount System:**
- Products get 10% discount after 3 months in shop
- Products get 15% discount after 6 months in shop
- Vendors can disable auto-discount with `auto_discount_enabled` field

**Store Customization Restrictions:**
- Store customization (logo, banner, layout, colors) only available for Premium/Platinum packages
- Standard package users get default store appearance

#### **product_categories**
```sql
CREATE TABLE product_categories (
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    category_id UUID NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
    PRIMARY KEY (product_id, category_id)
);
```

#### **product_images**
```sql
CREATE TABLE product_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    image_url VARCHAR(500) NOT NULL,
    alt_text VARCHAR(255),
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,

    -- Internal blockchain fields (completely hidden from all user interfaces)
    internal_token_id VARCHAR(255) UNIQUE,
    free_mint_platform VARCHAR(100), -- e.g., 'polygon', 'opensea', 'mintbase'
    blockchain_network VARCHAR(50), -- e.g., 'polygon', 'ethereum', 'near'
    blockchain_tx_hash VARCHAR(255),
    platform_collection_id VARCHAR(255), -- Collection ID on free minting platform
    uploader_reference UUID REFERENCES users(id),
    tokenization_status VARCHAR(20) DEFAULT 'queued',
    metadata_uri VARCHAR(500), -- IPFS or platform metadata URL
    tokenized_at TIMESTAMP WITH TIME ZONE,
    process_attempts INTEGER DEFAULT 0,
    process_log TEXT,
    gas_cost DECIMAL(18,8) DEFAULT 0, -- Track any minimal gas costs

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 🛒 Order Management Schema

#### **orders**
```sql
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    status order_status_enum DEFAULT 'pending',
    subtotal DECIMAL(12,2) NOT NULL,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    shipping_amount DECIMAL(12,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ZAR',
    payment_status payment_status_enum DEFAULT 'pending',
    payment_method VARCHAR(50),
    shipping_address JSONB,
    billing_address JSONB,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **order_items**
```sql
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id),
    vendor_id UUID NOT NULL REFERENCES vendors(id),
    product_name VARCHAR(255) NOT NULL,
    product_sku VARCHAR(100),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(12,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 💰 Financial Schema

#### **wallets**
```sql
CREATE TABLE wallets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    currency VARCHAR(3) NOT NULL,
    balance DECIMAL(18,8) DEFAULT 0,
    blockchain_address VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, currency)
);
```

#### **transactions**
```sql
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    wallet_id UUID NOT NULL REFERENCES wallets(id),
    type transaction_type_enum NOT NULL,
    amount DECIMAL(18,8) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    reference_id UUID,
    reference_type VARCHAR(50),
    description TEXT,
    blockchain_hash VARCHAR(255),
    status transaction_status_enum DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confirmed_at TIMESTAMP WITH TIME ZONE
);
```

### ⭐ Review System Schema

#### **reviews**
```sql
CREATE TABLE reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    order_item_id UUID REFERENCES order_items(id),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255),
    comment TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT FALSE,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);
```

### 🔗 Blockchain Integration Schema

#### **blockchain_records**
```sql
CREATE TABLE blockchain_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID NOT NULL REFERENCES transactions(id),
    block_number BIGINT,
    block_hash VARCHAR(255),
    transaction_hash VARCHAR(255) UNIQUE NOT NULL,
    gas_used BIGINT,
    gas_price DECIMAL(18,8),
    confirmation_count INTEGER DEFAULT 0,
    status blockchain_status_enum DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confirmed_at TIMESTAMP WITH TIME ZONE
);
```

## 🔄 Relationship Summary

### One-to-Many Relationships
- `users` → `user_addresses` (1:N)
- `users` → `vendors` (1:1)
- `users` → `orders` (1:N)
- `users` → `wallets` (1:N)
- `users` → `reviews` (1:N)
- `vendors` → `products` (1:N)
- `categories` → `categories` (1:N) - Self-referencing
- `products` → `product_images` (1:N)
- `products` → `order_items` (1:N)
- `products` → `reviews` (1:N)
- `orders` → `order_items` (1:N)
- `wallets` → `transactions` (1:N)
- `transactions` → `blockchain_records` (1:1)

### Many-to-Many Relationships
- `products` ↔ `categories` (through `product_categories`)

## 📊 Storage Estimates

| Table | Est. Records | Storage/Record | Total Storage |
|-------|-------------|----------------|---------------|
| users | 100K | 1KB | 100MB |
| products | 500K | 2KB | 1GB |
| orders | 1M | 1KB | 1GB |
| order_items | 5M | 0.5KB | 2.5GB |
| reviews | 500K | 1KB | 500MB |
| transactions | 1M | 0.5KB | 500MB |

**Total Estimated Storage**: ~6GB (excluding indexes and logs)

## 🔒 Security Features

### Data Protection
- Password hashing with bcrypt
- Sensitive data encryption at rest
- PII data anonymization support
- Audit trail for all modifications

### Access Control
- Role-based permissions
- Row-level security policies
- API rate limiting support
- SQL injection prevention

### Compliance
- POPIA compliance ready
- GDPR data portability
- Right to be forgotten implementation
- Data retention policies

---

**Reference Diagram**: `../diagram-export-19-06-2025-10_57_58.png`  
**Issue**: #62 - Design Database Schema and ERD  
**Last Updated**: June 20, 2025  
**Status**: Complete and Reviewed
