# Entity Relationship Diagram (ERD)

## 📊 Visual Database Design for A Good Man's View

This document provides comprehensive documentation for the Entity Relationship Diagram designed for Issue #62, supporting the multi-vendor e-commerce platform with blockchain integration.

## 🖼️ ERD Diagram Reference

**Primary Diagram**: `../diagram-export-19-06-2025-10_57_58.png`

The complete visual ERD diagram shows all entities, relationships, and constraints designed for the A Good Man's View platform.

## 🏗️ Core Entities Overview

### 👥 User Management Entities

#### **Users**
- **Purpose**: Central user authentication and profile management
- **Key Attributes**: id, email, password_hash, role, profile_data
- **Relationships**: One-to-many with Orders, Reviews, Wallets
- **Business Rules**: Unique email, role-based access control

#### **Vendors**
- **Purpose**: Multi-vendor marketplace support
- **Key Attributes**: id, user_id, business_name, verification_status
- **Relationships**: One-to-many with Products, belongs-to Users
- **Business Rules**: Verified vendors only, South African business registration

### 🛍️ Product Catalog Entities

#### **Products**
- **Purpose**: Core product catalog management
- **Key Attributes**: id, vendor_id, name, description, price, currency
- **Relationships**: Belongs-to Vendor, many-to-many with Categories
- **Business Rules**: ZAR currency support, inventory tracking

#### **Categories**
- **Purpose**: Hierarchical product categorization
- **Key Attributes**: id, name, parent_id, description
- **Relationships**: Self-referencing hierarchy, many-to-many with Products
- **Business Rules**: Maximum 3-level hierarchy depth

#### **Product_Images**
- **Purpose**: Product visual assets with invisible blockchain tokenization
- **Key Attributes**: id, product_id, image_url, tokenization_status, platform_address
- **Relationships**: Belongs-to Products, internal reference to Users (uploader)
- **Business Rules**: Maximum 10 images per product, invisible blockchain processing, no user awareness

### 🛒 Order Management Entities

#### **Orders**
- **Purpose**: Customer order processing and tracking
- **Key Attributes**: id, user_id, status, total_amount, currency
- **Relationships**: Belongs-to Users, one-to-many with Order_Items
- **Business Rules**: Multi-vendor order splitting, ZAR transactions

#### **Order_Items**
- **Purpose**: Individual items within orders
- **Key Attributes**: id, order_id, product_id, quantity, unit_price
- **Relationships**: Belongs-to Orders and Products
- **Business Rules**: Snapshot pricing, inventory validation

### 💰 Financial Entities

#### **Wallets**
- **Purpose**: Digital wallet for blockchain transactions
- **Key Attributes**: id, user_id, balance, currency, blockchain_address
- **Relationships**: Belongs-to Users, one-to-many with Transactions
- **Business Rules**: Multi-currency support, blockchain integration

#### **Transactions**
- **Purpose**: Financial transaction recording
- **Key Attributes**: id, wallet_id, type, amount, blockchain_hash
- **Relationships**: Belongs-to Wallets, linked to Blockchain_Records
- **Business Rules**: Immutable records, blockchain verification

### ⭐ Review System Entities

#### **Reviews**
- **Purpose**: Product review and rating system
- **Key Attributes**: id, user_id, product_id, rating, comment
- **Relationships**: Belongs-to Users and Products
- **Business Rules**: One review per user per product, 1-5 star rating

### 🔗 Blockchain Integration Entities

#### **Blockchain_Records**
- **Purpose**: Blockchain transaction immutability
- **Key Attributes**: id, transaction_id, block_hash, timestamp
- **Relationships**: Linked to Transactions
- **Business Rules**: Immutable records, cryptographic verification

## 🔄 Key Relationships

### Primary Relationships
1. **Users → Vendors** (1:1) - User can become a vendor
2. **Vendors → Products** (1:N) - Vendors manage multiple products
3. **Users → Orders** (1:N) - Users place multiple orders
4. **Orders → Order_Items** (1:N) - Orders contain multiple items
5. **Products → Order_Items** (1:N) - Products can be in multiple orders
6. **Users → Wallets** (1:N) - Users can have multiple wallets
7. **Wallets → Transactions** (1:N) - Wallets have transaction history
8. **Users → Reviews** (1:N) - Users can write multiple reviews
9. **Products → Reviews** (1:N) - Products receive multiple reviews

### Many-to-Many Relationships
1. **Products ↔ Categories** - Products can belong to multiple categories
2. **Orders ↔ Products** (through Order_Items) - Complex order relationships

## 📐 Normalization Strategy

### Third Normal Form (3NF) Compliance
- **1NF**: All attributes are atomic values
- **2NF**: No partial dependencies on composite keys
- **3NF**: No transitive dependencies

### Denormalization Considerations
- **Product pricing**: Snapshot in Order_Items for historical accuracy
- **User names**: Cached in Reviews for performance
- **Category paths**: Materialized for quick navigation

## 🔒 Security & Constraints

### Primary Keys
- All entities use UUID primary keys for security
- Sequential IDs avoided to prevent enumeration attacks

### Foreign Key Constraints
- Cascading deletes configured appropriately
- Soft deletes implemented for audit trails

### Check Constraints
- Rating values: 1-5 range validation
- Price values: Positive amounts only
- Email format: Valid email pattern
- Currency codes: ISO 4217 compliance

## 🚀 Performance Considerations

### Indexing Strategy
- Primary keys: Clustered indexes
- Foreign keys: Non-clustered indexes
- Search fields: Composite indexes
- Full-text search: GIN indexes for product descriptions

### Query Optimization
- Materialized views for complex aggregations
- Partial indexes for filtered queries
- Connection pooling for high concurrency

## 🌍 South African Market Adaptations

### Currency Support
- Primary currency: ZAR (South African Rand)
- Multi-currency wallet support for international transactions
- Exchange rate integration for pricing

### Localization
- Address format: South African postal codes
- Phone numbers: South African format validation
- Business registration: CIPC integration ready

### Compliance
- POPIA (Protection of Personal Information Act) compliance
- VAT calculation and reporting
- B-BBEE scoring integration ready

## 🔄 Migration Strategy

### Phase 1: Core Entities
1. Users and authentication
2. Basic product catalog
3. Simple order processing

### Phase 2: Advanced Features
1. Multi-vendor support
2. Digital wallets
3. Review system

### Phase 3: Blockchain Integration
1. Blockchain records
2. Smart contract integration
3. Decentralized features

## 📊 Entity Statistics

| Entity | Estimated Records | Growth Rate | Storage Impact |
|--------|------------------|-------------|----------------|
| Users | 10K-100K | High | Medium |
| Products | 50K-500K | Very High | High |
| Orders | 100K-1M | High | High |
| Order_Items | 500K-5M | Very High | Very High |
| Reviews | 50K-500K | Medium | Medium |
| Transactions | 100K-1M | High | Medium |

## ✅ Validation Rules

### Business Logic Validation
- Users must verify email before first purchase
- Vendors must complete business verification
- Products require at least one category assignment
- Orders must have valid payment method
- Reviews require verified purchase

### Free Blockchain Tokenization Rules
- Product images are automatically tokenized using free minting platforms without user knowledge
- Vendors and buyers interact with normal e-commerce interface only
- No mention of blockchain, NFTs, or tokenization in any user-facing interface
- Platform uses free minting services (Polygon, OpenSea, Mintbase) to eliminate costs
- Tokenization process is completely invisible background operation with zero fees
- Platform maintains collections on free minting platforms for organized asset management
- Original uploader tracked internally for platform analytics only
- Failed tokenization attempts handled silently with automatic retry
- Users experience standard e-commerce image functionality
- Blockchain technology provides platform-level asset security and verification at no cost

### Data Integrity Rules
- Soft deletes preserve referential integrity
- Audit trails track all modifications
- Blockchain records are immutable
- Financial calculations use decimal precision

---

**Diagram Reference**: `../diagram-export-19-06-2025-10_57_58.png`  
**Issue**: #62 - Design Database Schema and ERD  
**Last Updated**: June 20, 2025  
**Status**: Complete and Approved
