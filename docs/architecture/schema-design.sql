-- =====================================================
-- A Good Man's View - PostgreSQL Database Schema
-- Issue #62: Design Database Schema and ERD
-- Created: June 20, 2025
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- =====================================================
-- ENUMS AND CUSTOM TYPES
-- =====================================================

-- User role enumeration
CREATE TYPE user_role_enum AS ENUM (
    'buyer',
    'vendor',
    'admin',
    'super_admin'
);

-- Address type enumeration
CREATE TYPE address_type_enum AS ENUM (
    'home',
    'work/office',
    'both'
);

-- Vendor verification status
CREATE TYPE vendor_status_enum AS ENUM (
    'pending',
    'verified',
    'rejected',
    'suspended'
);

-- Subscription package enumeration
CREATE TYPE subscription_package_enum AS ENUM (
    'standard',
    'premium',
    'platinum'
);

-- Order status enumeration
CREATE TYPE order_status_enum AS ENUM (
    'pending',
    'confirmed',
    'processing',
    'shipped',
    'delivered'
);

-- Payment status enumeration
CREATE TYPE payment_status_enum AS ENUM (
    'pending',
    'processing',
    'completed',
    'failed'
);

-- Transaction type enumeration
CREATE TYPE transaction_type_enum AS ENUM (
    'deposit',
    'withdrawal',
    'payment',
    'commission'
);

-- Transaction status enumeration
CREATE TYPE transaction_status_enum AS ENUM (
    'pending',
    'processing',
    'completed',
    'failed'
);

-- Blockchain status enumeration
CREATE TYPE blockchain_status_enum AS ENUM (
    'pending',
    'submitted',
    'confirmed',
    'failed'
);

-- =====================================================
-- CORE TABLES
-- =====================================================

-- Users table - Central user management
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role user_role_enum NOT NULL DEFAULT 'buyer',
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    date_of_birth DATE,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT users_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT users_phone_check CHECK (phone IS NULL OR phone ~* '^\+?[0-9\s\-\(\)]{10,15}$')
);

-- User addresses table
CREATE TABLE user_addresses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type address_type_enum NOT NULL,
    street_address VARCHAR(255) NOT NULL,
    suburb VARCHAR(100),
    city VARCHAR(100) NOT NULL,
    province VARCHAR(50) NOT NULL,
    postal_code VARCHAR(10) NOT NULL,
    country VARCHAR(2) DEFAULT 'ZA',
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT user_addresses_postal_code_check CHECK (postal_code ~* '^[0-9]{4}$')
);

-- Vendors table (Artists/Creators)
CREATE TABLE vendors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    business_name VARCHAR(255) NOT NULL,
    artist_bio TEXT,
    verification_status vendor_status_enum DEFAULT 'pending',
    verification_date TIMESTAMP WITH TIME ZONE,
    verification_notes TEXT,
    store_logo_url VARCHAR(500),
    store_banner_url VARCHAR(500),
    store_layout VARCHAR(50) DEFAULT 'grid',
    store_color_scheme JSONB,
    contact_email VARCHAR(255),
    current_package subscription_package_enum DEFAULT 'standard',
    commission_rate DECIMAL(5,4) DEFAULT 0.0000,
    portfolio_storage_used BIGINT DEFAULT 0,
    portfolio_storage_limit BIGINT DEFAULT 1073741824, -- 1GB default
    can_sell_products BOOLEAN DEFAULT FALSE,
    can_join_competitions BOOLEAN DEFAULT FALSE,
    has_priority_registration BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT vendors_commission_rate_check CHECK (commission_rate >= 0 AND commission_rate <= 1),
    CONSTRAINT vendors_storage_check CHECK (portfolio_storage_used >= 0 AND portfolio_storage_used <= portfolio_storage_limit),
    UNIQUE(user_id)
);

-- Categories table - Hierarchical product categories
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES categories(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    meta_title VARCHAR(255),
    meta_description VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT categories_slug_check CHECK (slug ~* '^[a-z0-9\-]+$')
);

-- Products table
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    sku VARCHAR(100) UNIQUE,
    price DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ZAR',
    cost_price DECIMAL(12,2),
    stock_quantity INTEGER DEFAULT 0,
    low_stock_threshold INTEGER DEFAULT 10,
    weight DECIMAL(8,3),
    dimensions JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    auto_discount_enabled BOOLEAN DEFAULT TRUE,
    meta_title VARCHAR(255),
    meta_description VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT products_price_check CHECK (price > 0),
    CONSTRAINT products_cost_price_check CHECK (cost_price IS NULL OR cost_price >= 0),
    CONSTRAINT products_stock_check CHECK (stock_quantity >= 0),
    CONSTRAINT products_slug_check CHECK (slug ~* '^[a-z0-9\-]+$')
);

-- Product categories junction table
CREATE TABLE product_categories (
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    category_id UUID NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (product_id, category_id)
);

-- Product images table with free blockchain tokenization
CREATE TABLE product_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    image_url VARCHAR(500) NOT NULL,
    alt_text VARCHAR(255),
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,

    -- Internal blockchain fields (completely hidden from all user interfaces)
    internal_token_id VARCHAR(255) UNIQUE,
    free_mint_platform VARCHAR(100), -- e.g., 'polygon', 'opensea', 'mintbase'
    blockchain_network VARCHAR(50), -- e.g., 'polygon', 'ethereum', 'near'
    blockchain_tx_hash VARCHAR(255),
    platform_collection_id VARCHAR(255), -- Collection ID on free minting platform
    uploader_reference UUID REFERENCES users(id),
    tokenization_status VARCHAR(20) DEFAULT 'queued', -- queued, processing, completed, error
    metadata_uri VARCHAR(500), -- IPFS or platform metadata URL
    tokenized_at TIMESTAMP WITH TIME ZONE,
    process_attempts INTEGER DEFAULT 0,
    process_log TEXT,
    gas_cost DECIMAL(18,8) DEFAULT 0, -- Track any minimal gas costs

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT product_images_tokenization_check CHECK (tokenization_status IN ('queued', 'processing', 'completed', 'error')),
    CONSTRAINT product_images_attempts_check CHECK (process_attempts >= 0),
    CONSTRAINT product_images_gas_cost_check CHECK (gas_cost >= 0)
);

-- Orders table
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    status order_status_enum DEFAULT 'pending',
    subtotal DECIMAL(12,2) NOT NULL,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    shipping_amount DECIMAL(12,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ZAR',
    payment_status payment_status_enum DEFAULT 'pending',
    payment_method VARCHAR(50),
    shipping_address JSONB,
    billing_address JSONB,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT orders_amounts_check CHECK (
        subtotal >= 0 AND 
        tax_amount >= 0 AND 
        shipping_amount >= 0 AND 
        discount_amount >= 0 AND
        total_amount >= 0
    )
);

-- Order items table
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id),
    vendor_id UUID NOT NULL REFERENCES vendors(id),
    product_name VARCHAR(255) NOT NULL,
    product_sku VARCHAR(100),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(12,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT order_items_quantity_check CHECK (quantity > 0),
    CONSTRAINT order_items_prices_check CHECK (unit_price >= 0 AND total_price >= 0)
);

-- Wallets table
CREATE TABLE wallets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    currency VARCHAR(3) DEFAULT 'ZAR' NOT NULL,
    balance DECIMAL(18,8) DEFAULT 0,
    payment_gateway_reference VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT wallets_balance_check CHECK (balance >= 0),
    UNIQUE(user_id, currency)
);

-- Transactions table
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    wallet_id UUID NOT NULL REFERENCES wallets(id),
    type transaction_type_enum NOT NULL,
    amount DECIMAL(18,8) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    reference_id UUID,
    reference_type VARCHAR(50),
    description TEXT,
    blockchain_hash VARCHAR(255),
    status transaction_status_enum DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confirmed_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT transactions_amount_check CHECK (amount != 0)
);

-- Subscriptions table
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    package_type subscription_package_enum NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    monthly_price DECIMAL(8,2) NOT NULL,
    commission_rate DECIMAL(5,4) NOT NULL,
    storage_limit BIGINT NOT NULL,
    features JSONB,
    payment_reference VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT subscriptions_price_check CHECK (monthly_price > 0),
    CONSTRAINT subscriptions_commission_check CHECK (commission_rate >= 0 AND commission_rate <= 1),
    CONSTRAINT subscriptions_storage_check CHECK (storage_limit > 0),
    CONSTRAINT subscriptions_dates_check CHECK (end_date > start_date),
    CONSTRAINT subscriptions_status_check CHECK (status IN ('active', 'expired', 'cancelled', 'suspended'))
);

-- Blockchain records table
CREATE TABLE blockchain_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID NOT NULL REFERENCES transactions(id),
    block_number BIGINT,
    block_hash VARCHAR(255),
    transaction_hash VARCHAR(255) UNIQUE NOT NULL,
    record_type VARCHAR(50) DEFAULT 'payment' NOT NULL,
    gas_used BIGINT,
    gas_price DECIMAL(18,8),
    confirmation_count INTEGER DEFAULT 0,
    status blockchain_status_enum DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confirmed_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT blockchain_records_confirmation_count_check CHECK (confirmation_count >= 0)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- Users indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);

-- User addresses indexes
CREATE INDEX idx_user_addresses_user_id ON user_addresses(user_id);
CREATE INDEX idx_user_addresses_type ON user_addresses(type);
CREATE INDEX idx_user_addresses_default ON user_addresses(is_default) WHERE is_default = TRUE;

-- Vendors indexes
CREATE INDEX idx_vendors_user_id ON vendors(user_id);
CREATE INDEX idx_vendors_status ON vendors(verification_status);
CREATE INDEX idx_vendors_active ON vendors(is_active);
CREATE INDEX idx_vendors_package ON vendors(current_package);
CREATE INDEX idx_vendors_can_sell ON vendors(can_sell_products);
CREATE INDEX idx_vendors_layout ON vendors(store_layout);
CREATE INDEX idx_vendors_contact_email ON vendors(contact_email) WHERE contact_email IS NOT NULL;

-- Subscriptions indexes
CREATE INDEX idx_subscriptions_vendor_id ON subscriptions(vendor_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_subscriptions_package_type ON subscriptions(package_type);
CREATE INDEX idx_subscriptions_end_date ON subscriptions(end_date);
CREATE INDEX idx_subscriptions_active ON subscriptions(status, end_date) WHERE status = 'active';

-- Categories indexes
CREATE INDEX idx_categories_parent_id ON categories(parent_id);
CREATE INDEX idx_categories_slug ON categories(slug);
CREATE INDEX idx_categories_active ON categories(is_active);

-- Products indexes
CREATE INDEX idx_products_vendor_id ON products(vendor_id);
CREATE INDEX idx_products_slug ON products(slug);
CREATE INDEX idx_products_active ON products(is_active);
CREATE INDEX idx_products_featured ON products(is_featured) WHERE is_featured = TRUE;
CREATE INDEX idx_products_price ON products(price);
CREATE INDEX idx_products_created_at ON products(created_at);
CREATE INDEX idx_products_auto_discount ON products(auto_discount_enabled, created_at) WHERE auto_discount_enabled = TRUE;
CREATE INDEX idx_products_name_trgm ON products USING gin(name gin_trgm_ops);

-- Product categories indexes
CREATE INDEX idx_product_categories_product_id ON product_categories(product_id);
CREATE INDEX idx_product_categories_category_id ON product_categories(category_id);

-- Product images indexes
CREATE INDEX idx_product_images_product_id ON product_images(product_id);
CREATE INDEX idx_product_images_primary ON product_images(is_primary) WHERE is_primary = TRUE;
CREATE INDEX idx_product_images_internal_token ON product_images(internal_token_id) WHERE internal_token_id IS NOT NULL;
CREATE INDEX idx_product_images_free_mint_platform ON product_images(free_mint_platform);
CREATE INDEX idx_product_images_blockchain_network ON product_images(blockchain_network);
CREATE INDEX idx_product_images_collection_id ON product_images(platform_collection_id) WHERE platform_collection_id IS NOT NULL;
CREATE INDEX idx_product_images_uploader_ref ON product_images(uploader_reference);
CREATE INDEX idx_product_images_tokenization_status ON product_images(tokenization_status);
CREATE INDEX idx_product_images_queued_processing ON product_images(tokenization_status) WHERE tokenization_status IN ('queued', 'processing');
CREATE INDEX idx_product_images_error_processing ON product_images(tokenization_status) WHERE tokenization_status = 'error';

-- Orders indexes
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_payment_status ON orders(payment_status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_order_number ON orders(order_number);

-- Order items indexes
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);
CREATE INDEX idx_order_items_vendor_id ON order_items(vendor_id);

-- Wallets indexes
CREATE INDEX idx_wallets_user_id ON wallets(user_id);
CREATE INDEX idx_wallets_currency ON wallets(currency);
CREATE INDEX idx_wallets_active ON wallets(is_active);

-- Transactions indexes
CREATE INDEX idx_transactions_wallet_id ON transactions(wallet_id);
CREATE INDEX idx_transactions_type ON transactions(type);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
CREATE INDEX idx_transactions_reference ON transactions(reference_id, reference_type);

-- Reviews indexes
CREATE INDEX idx_reviews_user_id ON reviews(user_id);
CREATE INDEX idx_reviews_product_id ON reviews(product_id);
CREATE INDEX idx_reviews_rating ON reviews(rating);
CREATE INDEX idx_reviews_approved ON reviews(is_approved) WHERE is_approved = TRUE;
CREATE INDEX idx_reviews_created_at ON reviews(created_at);

-- Blockchain records indexes
CREATE INDEX idx_blockchain_records_transaction_id ON blockchain_records(transaction_id);
CREATE INDEX idx_blockchain_records_status ON blockchain_records(status);
CREATE INDEX idx_blockchain_records_block_number ON blockchain_records(block_number);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- =====================================================

-- Function to update timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables with updated_at columns
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_addresses_updated_at BEFORE UPDATE ON user_addresses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vendors_updated_at BEFORE UPDATE ON vendors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_product_images_updated_at BEFORE UPDATE ON product_images FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_wallets_updated_at BEFORE UPDATE ON wallets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON reviews FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE users IS 'Central user management table for customers, vendors, and administrators';
COMMENT ON TABLE vendors IS 'Vendor-specific information and verification status';
COMMENT ON TABLE products IS 'Product catalog with vendor ownership and inventory tracking';
COMMENT ON TABLE orders IS 'Customer orders with multi-vendor support';
COMMENT ON TABLE wallets IS 'Digital wallets for blockchain integration';
COMMENT ON TABLE transactions IS 'Financial transaction records with blockchain support';
COMMENT ON TABLE reviews IS 'Product reviews and ratings system';
COMMENT ON TABLE blockchain_records IS 'Immutable blockchain transaction records';

-- =====================================================
-- END OF SCHEMA DEFINITION
-- =====================================================
