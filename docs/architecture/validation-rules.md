# Data Validation Rules

## 🛡️ Comprehensive Data Validation and Business Rules

This document defines all data validation rules and business logic constraints for the A Good Man's View database schema, ensuring data integrity, security, and compliance with business requirements.

## 🎯 Validation Strategy

### Multi-Layer Validation Approach
1. **Database Level**: CHECK constraints, foreign keys, unique constraints
2. **Application Level**: Business logic validation in NestJS
3. **API Level**: Input sanitization and validation
4. **Frontend Level**: User experience and immediate feedback

### Validation Principles
- **Fail Fast**: Validate at the earliest possible point
- **Clear Messages**: Provide meaningful error messages
- **Security First**: Prevent injection attacks and data breaches
- **Business Logic**: Enforce business rules consistently
- **Performance**: Efficient validation without impacting performance

## 📊 Entity Validation Rules

### 👥 Users Table Validation

#### Database Constraints
```sql
-- Email format validation
CONSTRAINT users_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')

-- Phone number format (South African + international)
CONSTRAINT users_phone_check CHECK (phone IS NULL OR phone ~* '^\+?[0-9\s\-\(\)]{10,15}$')

-- Unique email constraint
UNIQUE(email)
```

#### Business Rules
- **Email Uniqueness**: One account per email address
- **Password Strength**: Minimum 8 characters, mixed case, numbers, symbols
- **Age Verification**: Must be 18+ for account creation
- **Role Assignment**: Only admins can assign admin/super_admin roles
- **Account Deactivation**: Soft delete preserves order history

#### Validation Examples
```typescript
// Application-level validation
export class CreateUserDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsStrongPassword({
    minLength: 8,
    minLowercase: 1,
    minUppercase: 1,
    minNumbers: 1,
    minSymbols: 1
  })
  password: string;

  @IsOptional()
  @IsPhoneNumber('ZA')
  phone?: string;

  @IsDateString()
  @IsAdult()
  dateOfBirth: string;
}
```

### 🏢 Vendors Table Validation

#### Database Constraints
```sql
-- Commission rate validation (0-100%)
CONSTRAINT vendors_commission_rate_check CHECK (commission_rate >= 0 AND commission_rate <= 1)

-- One vendor per user
UNIQUE(user_id)
```

#### Business Rules
- **Business Registration**: Valid CIPC registration number required
- **Tax Compliance**: Valid tax number for South African businesses
- **Verification Process**: Manual verification before activation
- **Commission Limits**: 5-15% commission rate range
- **Banking Details**: Valid South African bank account required

#### Validation Examples
```typescript
export class CreateVendorDto {
  @IsNotEmpty()
  @Length(2, 255)
  businessName: string;

  @IsOptional()
  @Matches(/^[0-9]{10}\/[0-9]{2}$/) // CIPC format
  businessRegistration?: string;

  @IsOptional()
  @Matches(/^[0-9]{10}$/) // South African tax number
  taxNumber?: string;

  @IsNumber()
  @Min(0.05)
  @Max(0.15)
  commissionRate: number;
}
```

### 🛍️ Products Table Validation

#### Database Constraints
```sql
-- Price validation (positive values only)
CONSTRAINT products_price_check CHECK (price > 0)

-- Cost price validation (non-negative)
CONSTRAINT products_cost_price_check CHECK (cost_price IS NULL OR cost_price >= 0)

-- Stock quantity validation (non-negative)
CONSTRAINT products_stock_check CHECK (stock_quantity >= 0)

-- SEO-friendly slug format
CONSTRAINT products_slug_check CHECK (slug ~* '^[a-z0-9\-]+$')

-- Unique constraints
UNIQUE(slug)
UNIQUE(sku)
```

#### Business Rules
- **Vendor Ownership**: Only product owner can modify
- **Price Currency**: ZAR primary, multi-currency support
- **Stock Management**: Automatic low-stock alerts
- **Image Requirements**: At least one product image required
- **Category Assignment**: Must belong to at least one category
- **SEO Optimization**: Unique slug and meta tags required

#### Validation Examples
```typescript
export class CreateProductDto {
  @IsNotEmpty()
  @Length(3, 255)
  name: string;

  @IsSlug()
  @IsUnique('products', 'slug')
  slug: string;

  @IsNumber()
  @Min(0.01)
  @Max(999999.99)
  price: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  costPrice?: number;

  @IsInt()
  @Min(0)
  stockQuantity: number;

  @IsArray()
  @ArrayMinSize(1)
  @IsUUID(4, { each: true })
  categoryIds: string[];
}
```

### 🛒 Orders Table Validation

#### Database Constraints
```sql
-- Amount validations (non-negative)
CONSTRAINT orders_amounts_check CHECK (
    subtotal >= 0 AND 
    tax_amount >= 0 AND 
    shipping_amount >= 0 AND 
    discount_amount >= 0 AND
    total_amount >= 0
)

-- Unique order number
UNIQUE(order_number)
```

#### Business Rules
- **Order Number Format**: AGV-YYYYMMDD-XXXXXX
- **Multi-vendor Orders**: Automatic vendor splitting
- **Tax Calculation**: 15% VAT for South African orders
- **Shipping Calculation**: Based on weight and location
- **Payment Validation**: Valid payment method required
- **Address Validation**: Complete shipping address required

#### Validation Examples
```typescript
export class CreateOrderDto {
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => OrderItemDto)
  items: OrderItemDto[];

  @IsObject()
  @ValidateNested()
  @Type(() => AddressDto)
  shippingAddress: AddressDto;

  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AddressDto)
  billingAddress?: AddressDto;

  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;
}

export class OrderItemDto {
  @IsUUID()
  productId: string;

  @IsInt()
  @Min(1)
  @Max(100)
  quantity: number;
}
```

### 💰 Financial Validation Rules

#### Wallets Table
```sql
-- Balance validation (non-negative)
CONSTRAINT wallets_balance_check CHECK (balance >= 0)

-- One wallet per currency per user
UNIQUE(user_id, currency)
```

#### Transactions Table
```sql
-- Amount validation (non-zero)
CONSTRAINT transactions_amount_check CHECK (amount != 0)
```

#### Business Rules
- **Currency Support**: ZAR, BTC, ETH, USD
- **Balance Validation**: Sufficient funds before transactions
- **Transaction Limits**: Daily/monthly limits per user type
- **Blockchain Integration**: Automatic blockchain recording
- **Audit Trail**: Immutable transaction history

### ⭐ Reviews Table Validation

#### Database Constraints
```sql
-- Rating range validation (1-5 stars)
CONSTRAINT reviews_rating_check CHECK (rating >= 1 AND rating <= 5)

-- Helpful count validation (non-negative)
CONSTRAINT reviews_helpful_count_check CHECK (helpful_count >= 0)

-- One review per user per product
UNIQUE(user_id, product_id)
```

#### Business Rules
- **Verified Purchase**: Only verified buyers can review
- **Content Moderation**: Automatic profanity filtering
- **Review Approval**: Manual approval for sensitive content
- **Rating Impact**: Real-time product rating updates
- **Helpful Votes**: Community-driven review ranking

## 🔒 Security Validation Rules

### Input Sanitization
```typescript
// XSS Prevention
@Transform(({ value }) => sanitizeHtml(value))
@IsString()
description: string;

// SQL Injection Prevention
@IsAlphanumeric()
@Length(3, 50)
username: string;

// File Upload Validation
@IsImageFile()
@MaxFileSize(5 * 1024 * 1024) // 5MB
productImage: Express.Multer.File;
```

### Authentication & Authorization
```typescript
// Password validation
@IsStrongPassword()
@IsNotCompromised() // Check against breach databases
password: string;

// Role-based validation
@HasRole(['admin', 'super_admin'])
@ValidatePermission('users.delete')
deleteUser(@Param('id') userId: string) { }
```

## 🌍 South African Specific Validations

### Address Validation
```sql
-- South African postal code format
CONSTRAINT user_addresses_postal_code_check CHECK (postal_code ~* '^[0-9]{4}$')
```

```typescript
export class AddressDto {
  @IsNotEmpty()
  @Length(5, 255)
  streetAddress: string;

  @IsNotEmpty()
  @Length(2, 100)
  city: string;

  @IsEnum(SouthAfricanProvince)
  province: SouthAfricanProvince;

  @Matches(/^[0-9]{4}$/)
  postalCode: string;

  @IsISO31661Alpha2()
  @IsOptional()
  country?: string = 'ZA';
}
```

### Tax and Compliance
```typescript
// VAT number validation
@IsOptional()
@Matches(/^[0-9]{10}$/)
vatNumber?: string;

// CIPC registration validation
@IsOptional()
@Matches(/^[0-9]{10}\/[0-9]{2}$/)
cipcRegistration?: string;

// Banking details validation
@Matches(/^[0-9]{6}$/) // Branch code
branchCode: string;

@Matches(/^[0-9]{10,11}$/) // Account number
accountNumber: string;
```

## 📊 Performance Validation Rules

### Query Optimization
- **Pagination**: Maximum 100 records per page
- **Search Limits**: Minimum 3 characters for search
- **Filter Validation**: Valid filter parameters only
- **Sort Validation**: Allowed sort fields only

### Rate Limiting
```typescript
// API rate limiting
@Throttle(100, 60) // 100 requests per minute
@RateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
})
```

## 🔄 Business Logic Validation

### Order Processing Rules
1. **Inventory Check**: Validate stock availability
2. **Price Validation**: Confirm current pricing
3. **Shipping Validation**: Valid delivery address
4. **Payment Validation**: Sufficient funds/valid payment
5. **Vendor Validation**: Active vendor status

### Vendor Onboarding Rules
1. **Business Verification**: Valid registration documents
2. **Banking Verification**: Valid bank account details
3. **Tax Compliance**: Valid tax registration
4. **Identity Verification**: KYC documentation
5. **Agreement Acceptance**: Terms and conditions

### User Account Rules
1. **Email Verification**: Confirmed email address
2. **Age Verification**: 18+ years old
3. **Identity Verification**: For high-value transactions
4. **Address Verification**: For shipping purposes
5. **Phone Verification**: For security notifications

## 🚨 Error Handling and Messages

### Validation Error Response Format
```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "code": "INVALID_EMAIL_FORMAT",
      "message": "Please enter a valid email address",
      "value": "invalid-email"
    },
    {
      "field": "password",
      "code": "WEAK_PASSWORD",
      "message": "Password must contain at least 8 characters with uppercase, lowercase, numbers, and symbols",
      "constraints": {
        "minLength": 8,
        "requireUppercase": true,
        "requireLowercase": true,
        "requireNumbers": true,
        "requireSymbols": true
      }
    }
  ]
}
```

### Localized Error Messages
```typescript
// English (default)
const errorMessages = {
  'INVALID_EMAIL': 'Please enter a valid email address',
  'WEAK_PASSWORD': 'Password is too weak',
  'INSUFFICIENT_STOCK': 'Not enough items in stock'
};

// Afrikaans
const errorMessagesAF = {
  'INVALID_EMAIL': 'Voer asseblief \'n geldige e-posadres in',
  'WEAK_PASSWORD': 'Wagwoord is te swak',
  'INSUFFICIENT_STOCK': 'Nie genoeg items in voorraad nie'
};
```

## ✅ Validation Testing Strategy

### Unit Tests
- Test each validation rule independently
- Test edge cases and boundary conditions
- Test error message accuracy
- Test performance impact

### Integration Tests
- Test validation across multiple layers
- Test business rule enforcement
- Test security validation effectiveness
- Test user experience flows

### Performance Tests
- Validate under high load conditions
- Test validation response times
- Test database constraint performance
- Test concurrent validation scenarios

---

**Reference Schema**: `schema-design.sql`  
**Issue**: #62 - Design Database Schema and ERD  
**Last Updated**: June 20, 2025  
**Status**: Validation Rules Complete and Tested
