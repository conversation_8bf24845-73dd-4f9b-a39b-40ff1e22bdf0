# Next.js Configuration
NEXT_PUBLIC_APP_NAME="A Good Man's View"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NEXT_PUBLIC_APP_DESCRIPTION="E-commerce platform for A Good Man's View"

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:4000
NEXT_PUBLIC_GRAPHQL_URL=http://localhost:4000/graphql
NEXT_PUBLIC_WS_URL=ws://localhost:4000

# Authentication
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# Database (if needed for API routes)
DATABASE_URL=postgresql://nipho:micanipho@localhost:5432/agoodmansview

# External Services
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload
NEXT_PUBLIC_UPLOAD_URL=http://localhost:4000/uploads
MAX_FILE_SIZE=5242880

# Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
NEXT_PUBLIC_GTM_ID=GTM-XXXXXXX

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_PWA=true
NEXT_PUBLIC_ENABLE_DARK_MODE=true

# Development
NODE_ENV=development
NEXT_PUBLIC_DEBUG=false

# Blockchain (if using)
NEXT_PUBLIC_BLOCKCHAIN_NETWORK=localhost
NEXT_PUBLIC_BLOCKCHAIN_RPC_URL=http://localhost:8545

# Social Media
NEXT_PUBLIC_FACEBOOK_URL=https://facebook.com/agoodmansview
NEXT_PUBLIC_TWITTER_URL=https://twitter.com/agoodmansview
NEXT_PUBLIC_INSTAGRAM_URL=https://instagram.com/agoodmansview
