# NPM configuration for Sharp installation
# Increase timeout for Sharp binary downloads
timeout=300000
fetch-timeout=300000
fetch-retry-mintimeout=20000
fetch-retry-maxtimeout=120000
fetch-retries=5

# Sharp-specific configuration
sharp_binary_host=https://github.com/lovell/sharp-libvips/releases/download/
sharp_libvips_binary_host=https://github.com/lovell/sharp-libvips/releases/download/

# Alternative: Use npm registry mirror (uncomment if needed)
# registry=https://registry.npmmirror.com/

# Disable package-lock for workspace
package-lock=false
