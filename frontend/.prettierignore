# Dependencies
node_modules/

# Build outputs
.next/
out/
dist/
build/

# Coverage
coverage/

# Environment files
.env*

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Generated files
*.tsbuildinfo
next-env.d.ts

# Storybook
storybook-static/

# Playwright
test-results/
playwright-report/

# Package files
package-lock.json
yarn.lock
pnpm-lock.yaml

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db
