{"name": "@agoodmansview/frontend", "version": "1.0.0", "description": "Next.js frontend for A Good Man's View e-commerce platform", "type": "module", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "preview": "next build && next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:jest": "jest", "test:jest:watch": "jest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "clean": "rm -rf .next out dist", "clean:all": "rm -rf .next out dist node_modules && npm install", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "deps:check": "npm outdated", "deps:update": "npm update", "security:audit": "npm audit", "security:fix": "npm audit fix", "lighthouse": "lighthouse http://localhost:3000 --output=json --output-path=./test-results/lighthouse-report.json --chrome-flags=\"--headless --no-sandbox\"", "a11y:test": "pa11y http://localhost:3000 --reporter json > ./test-results/a11y-report.json", "bundle-analyzer": "ANALYZE=true npm run build"}, "dependencies": {"@agoodmansview/shared": "file:../shared", "@tanstack/react-query": "5.81.2", "next": "15.3.4", "react": "^19.1.0", "react-dom": "^19.1.0", "sharp": "0.33.5"}, "optionalDependencies": {"@apollo/client": "^3.13.8", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.18.1", "graphql": "^16.11.0", "lucide-react": "^0.522.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.4.2", "@next/bundle-analyzer": "15.3.4", "@playwright/test": "^1.53.1", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/nextjs": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/test": "8.6.14", "@tailwindcss/forms": "0.5.10", "@tailwindcss/typography": "0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "30.0.0", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@vitejs/plugin-react": "4.5.2", "@vitest/coverage-v8": "3.2.4", "@vitest/ui": "3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-config-next": "15.3.4", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "jsdom": "26.1.0", "msw": "2.10.2", "postcss": "^8.4.49", "prettier": "3.5.3", "tailwindcss": "3.4.17", "typescript": "^5.8.3", "vitest": "3.2.4"}, "engines": {"node": ">=18.0.0"}}