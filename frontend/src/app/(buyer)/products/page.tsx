'use client'

import { useState } from 'react'
import Link from 'next/link'

// Dummy product data
const dummyProducts = [
  {
    id: '1',
    name: 'Handcrafted Wooden Bowl',
    price: 299.99,
    vendor: 'Cape Town Crafts',
    image: '/images/products/wooden-bowl.jpg',
    rating: 4.8,
    reviews: 24,
  },
  {
    id: '2',
    name: 'Organic Rooibos Tea Set',
    price: 149.99,
    vendor: 'Karoo Tea Co.',
    image: '/images/products/rooibos-tea.jpg',
    rating: 4.9,
    reviews: 67,
  },
  {
    id: '3',
    name: 'Beaded African Jewelry',
    price: 89.99,
    vendor: 'Ubuntu Designs',
    image: '/images/products/beaded-jewelry.jpg',
    rating: 4.7,
    reviews: 15,
  },
  {
    id: '4',
    name: 'Biltong Variety Pack',
    price: 199.99,
    vendor: 'Boerewors Bros',
    image: '/images/products/biltong-pack.jpg',
    rating: 4.6,
    reviews: 89,
  },
]

export default function ProductsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState('name')

  const filteredProducts = dummyProducts.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.vendor.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="agmv-container py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Products</h1>
          <p className="text-gray-600">Discover amazing products from South African vendors</p>
        </div>

        {/* Search and Filter */}
        <div className="bg-white p-6 rounded-lg shadow-lg mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
                Search Products
              </label>
              <input
                type="text"
                id="search"
                className="agmv-input"
                placeholder="Search products or vendors..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="sort" className="block text-sm font-medium text-gray-700 mb-2">
                Sort By
              </label>
              <select
                id="sort"
                className="agmv-input"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="name">Name</option>
                <option value="price">Price</option>
                <option value="rating">Rating</option>
                <option value="vendor">Vendor</option>
              </select>
            </div>
          </div>
        </div>

        {/* Product Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.map((product) => (
            <div key={product.id} className="agmv-card hover:shadow-xl transition-shadow duration-300">
              <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-lg bg-gray-200 mb-4">
                <div className="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                  <span className="text-gray-500">Product Image</span>
                </div>
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{product.name}</h3>
              <p className="text-sm text-gray-600 mb-2">by {product.vendor}</p>
              
              <div className="flex items-center mb-2">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <span
                      key={i}
                      className={`text-sm ${
                        i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-300'
                      }`}
                    >
                      ★
                    </span>
                  ))}
                </div>
                <span className="text-sm text-gray-600 ml-2">({product.reviews})</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-xl font-bold text-green-600">R{product.price}</span>
                <Link
                  href={`/products/${product.id}`}
                  className="agmv-button-primary text-sm"
                >
                  View Details
                </Link>
              </div>
            </div>
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No products found matching your search.</p>
          </div>
        )}
      </div>
    </div>
  )
}
