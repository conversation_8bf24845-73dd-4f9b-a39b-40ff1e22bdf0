@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables for theming */
:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 249, 250, 251;
  --primary-green: 34, 197, 94;
  --primary-blue: 59, 130, 246;
  --primary-gold: 245, 158, 11;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 17, 24, 39;
    --background-end-rgb: 31, 41, 55;
  }
}

/* Base styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-start-rgb));
  line-height: 1.6;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: none;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid rgb(var(--primary-green));
  outline-offset: 2px;
}

/* Custom styles for A Good Man's View */
.agmv-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.agmv-button-primary {
  @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.agmv-button-secondary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.agmv-card {
  @apply bg-white rounded-lg shadow-lg p-6 border border-gray-200;
}

.agmv-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500;
}
