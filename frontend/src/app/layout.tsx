import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: "A Good Man's View - Multi-Vendor E-commerce Platform",
  description: 'Secure, transparent e-commerce platform with blockchain integration for South Africa',
  keywords: ['e-commerce', 'blockchain', 'south africa', 'multi-vendor'],
  authors: [{ name: "A Good Man's View Team" }],
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div id="root">
          {children}
        </div>
      </body>
    </html>
  )
}
