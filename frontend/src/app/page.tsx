import Link from 'next/link'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            Welcome to <span className="text-green-600">A Good Man&apos;s View</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            South Africa&apos;s premier multi-vendor e-commerce platform powered by blockchain technology.
            Secure, transparent, and built for the local market.
          </p>
          
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-12">
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">For Buyers</h2>
              <p className="text-gray-600 mb-4">
                Discover amazing products from local vendors with secure blockchain-verified transactions.
              </p>
              <Link 
                href="/products" 
                className="inline-block bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Start Shopping
              </Link>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">For Vendors</h2>
              <p className="text-gray-600 mb-4">
                Join our platform and reach customers across South Africa with our subscription-based model.
              </p>
              <Link 
                href="/vendor/dashboard" 
                className="inline-block bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                Vendor Dashboard
              </Link>
            </div>
          </div>

          <div className="bg-white p-8 rounded-lg shadow-lg max-w-2xl mx-auto">
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">🔗 Blockchain Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="text-2xl mb-2">🔒</div>
                <div className="font-medium">Secure Transactions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">💰</div>
                <div className="font-medium">Digital Wallet</div>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">📊</div>
                <div className="font-medium">Transparent Records</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
