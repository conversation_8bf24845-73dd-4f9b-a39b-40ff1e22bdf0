/**
 * 🎭 Critical User Workflows E2E Tests
 * 
 * This test suite covers the most important user journeys for the
 * A Good Man's View e-commerce platform. These tests ensure that
 * core business functionality works end-to-end.
 * 
 * Test Categories:
 * - User Authentication (Registration, Login, Logout)
 * - Product Discovery (Browse, Search, Filter)
 * - Shopping Cart (Add, Remove, Update quantities)
 * - Checkout Process (Payment, Order confirmation)
 * - Vendor Dashboard (Product management, Orders)
 */

import { test, expect, Page } from '@playwright/test';

// Test data and utilities
const testUser = {
  email: '<EMAIL>',
  password: 'SecurePassword123!',
  firstName: 'Test',
  lastName: 'User'
};

const testVendor = {
  email: '<EMAIL>',
  password: 'VendorPassword123!',
  businessName: 'Test Vendor Store',
  firstName: 'Vendor',
  lastName: 'User'
};

const testProduct = {
  name: 'Test Product',
  description: 'A test product for E2E testing',
  price: 99.99,
  category: 'Electronics'
};

// Helper functions
async function loginUser(page: Page, email: string, password: string) {
  await page.goto('/auth/login');
  await page.fill('[data-testid="email-input"]', email);
  await page.fill('[data-testid="password-input"]', password);
  await page.click('[data-testid="login-button"]');
  await page.waitForURL('/dashboard');
}

async function addProductToCart(page: Page, productName: string) {
  await page.goto('/products');
  await page.click(`[data-testid="product-${productName}"]`);
  await page.click('[data-testid="add-to-cart-button"]');
  await expect(page.locator('[data-testid="cart-notification"]')).toBeVisible();
}

// Test Suite: User Authentication
test.describe('🔐 User Authentication Workflows', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing session
    await page.context().clearCookies();
    await page.goto('/');
  });

  test('should register new user successfully', async ({ page }) => {
    await page.goto('/auth/register');
    
    // Fill registration form
    await page.fill('[data-testid="first-name-input"]', testUser.firstName);
    await page.fill('[data-testid="last-name-input"]', testUser.lastName);
    await page.fill('[data-testid="email-input"]', testUser.email);
    await page.fill('[data-testid="password-input"]', testUser.password);
    await page.fill('[data-testid="confirm-password-input"]', testUser.password);
    
    // Accept terms and conditions
    await page.check('[data-testid="terms-checkbox"]');
    
    // Submit registration
    await page.click('[data-testid="register-button"]');
    
    // Verify successful registration
    await expect(page).toHaveURL('/auth/verify-email');
    await expect(page.locator('[data-testid="verification-message"]')).toContainText('verification email');
  });

  test('should login existing user successfully', async ({ page }) => {
    await loginUser(page, testUser.email, testUser.password);
    
    // Verify successful login
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    await expect(page.locator('[data-testid="user-name"]')).toContainText(testUser.firstName);
  });

  test('should handle invalid login credentials', async ({ page }) => {
    await page.goto('/auth/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');
    await page.click('[data-testid="login-button"]');
    
    // Verify error message
    await expect(page.locator('[data-testid="error-message"]')).toContainText('Invalid credentials');
  });

  test('should logout user successfully', async ({ page }) => {
    await loginUser(page, testUser.email, testUser.password);
    
    // Logout
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="logout-button"]');
    
    // Verify logout
    await expect(page).toHaveURL('/');
    await expect(page.locator('[data-testid="login-link"]')).toBeVisible();
  });
});

// Test Suite: Product Discovery
test.describe('🔍 Product Discovery Workflows', () => {
  test('should browse products by category', async ({ page }) => {
    await page.goto('/products');
    
    // Select category
    await page.click('[data-testid="category-electronics"]');
    
    // Verify filtered results
    await expect(page.locator('[data-testid="product-grid"]')).toBeVisible();
    await expect(page.locator('[data-testid="category-filter-active"]')).toContainText('Electronics');
    
    // Verify products are displayed
    const productCards = page.locator('[data-testid^="product-card-"]');
    await expect(productCards).toHaveCountGreaterThan(0);
  });

  test('should search for products', async ({ page }) => {
    await page.goto('/products');
    
    // Search for products
    await page.fill('[data-testid="search-input"]', 'laptop');
    await page.press('[data-testid="search-input"]', 'Enter');
    
    // Verify search results
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible();
    await expect(page.locator('[data-testid="search-query"]')).toContainText('laptop');
    
    // Verify product cards contain search term
    const productTitles = page.locator('[data-testid^="product-title-"]');
    const firstTitle = await productTitles.first().textContent();
    expect(firstTitle?.toLowerCase()).toContain('laptop');
  });

  test('should filter products by price range', async ({ page }) => {
    await page.goto('/products');
    
    // Set price range
    await page.fill('[data-testid="min-price-input"]', '50');
    await page.fill('[data-testid="max-price-input"]', '200');
    await page.click('[data-testid="apply-filters-button"]');
    
    // Verify filtered results
    const productPrices = page.locator('[data-testid^="product-price-"]');
    const priceCount = await productPrices.count();
    
    for (let i = 0; i < priceCount; i++) {
      const priceText = await productPrices.nth(i).textContent();
      const price = parseFloat(priceText?.replace(/[^0-9.]/g, '') || '0');
      expect(price).toBeGreaterThanOrEqual(50);
      expect(price).toBeLessThanOrEqual(200);
    }
  });

  test('should view product details', async ({ page }) => {
    await page.goto('/products');
    
    // Click on first product
    const firstProduct = page.locator('[data-testid^="product-card-"]').first();
    await firstProduct.click();
    
    // Verify product details page
    await expect(page.locator('[data-testid="product-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="product-description"]')).toBeVisible();
    await expect(page.locator('[data-testid="product-price"]')).toBeVisible();
    await expect(page.locator('[data-testid="add-to-cart-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="product-images"]')).toBeVisible();
  });
});

// Test Suite: Shopping Cart
test.describe('🛒 Shopping Cart Workflows', () => {
  test.beforeEach(async ({ page }) => {
    // Login before cart operations
    await loginUser(page, testUser.email, testUser.password);
  });

  test('should add product to cart', async ({ page }) => {
    await addProductToCart(page, testProduct.name);
    
    // Verify cart icon shows item count
    await expect(page.locator('[data-testid="cart-count"]')).toContainText('1');
  });

  test('should view cart contents', async ({ page }) => {
    await addProductToCart(page, testProduct.name);
    
    // Go to cart
    await page.click('[data-testid="cart-icon"]');
    
    // Verify cart contents
    await expect(page.locator('[data-testid="cart-items"]')).toBeVisible();
    await expect(page.locator('[data-testid^="cart-item-"]')).toHaveCount(1);
    await expect(page.locator('[data-testid="cart-total"]')).toBeVisible();
  });

  test('should update product quantity in cart', async ({ page }) => {
    await addProductToCart(page, testProduct.name);
    await page.click('[data-testid="cart-icon"]');
    
    // Update quantity
    await page.fill('[data-testid="quantity-input-0"]', '3');
    await page.click('[data-testid="update-quantity-button-0"]');
    
    // Verify updated quantity and total
    await expect(page.locator('[data-testid="quantity-input-0"]')).toHaveValue('3');
    
    // Verify total price updated
    const totalElement = page.locator('[data-testid="cart-total"]');
    const totalText = await totalElement.textContent();
    const total = parseFloat(totalText?.replace(/[^0-9.]/g, '') || '0');
    expect(total).toBeCloseTo(testProduct.price * 3, 2);
  });

  test('should remove product from cart', async ({ page }) => {
    await addProductToCart(page, testProduct.name);
    await page.click('[data-testid="cart-icon"]');
    
    // Remove item
    await page.click('[data-testid="remove-item-button-0"]');
    
    // Verify item removed
    await expect(page.locator('[data-testid="empty-cart-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="cart-count"]')).toContainText('0');
  });
});

// Test Suite: Checkout Process
test.describe('💳 Checkout Workflows', () => {
  test.beforeEach(async ({ page }) => {
    await loginUser(page, testUser.email, testUser.password);
    await addProductToCart(page, testProduct.name);
  });

  test('should proceed through checkout process', async ({ page }) => {
    await page.click('[data-testid="cart-icon"]');
    await page.click('[data-testid="checkout-button"]');
    
    // Step 1: Shipping Information
    await expect(page.locator('[data-testid="checkout-step-shipping"]')).toBeVisible();
    await page.fill('[data-testid="shipping-address"]', '123 Test Street');
    await page.fill('[data-testid="shipping-city"]', 'Cape Town');
    await page.fill('[data-testid="shipping-postal-code"]', '8001');
    await page.click('[data-testid="continue-to-payment-button"]');
    
    // Step 2: Payment Information
    await expect(page.locator('[data-testid="checkout-step-payment"]')).toBeVisible();
    await page.fill('[data-testid="card-number"]', '****************');
    await page.fill('[data-testid="card-expiry"]', '12/25');
    await page.fill('[data-testid="card-cvv"]', '123');
    await page.fill('[data-testid="card-name"]', 'Test User');
    await page.click('[data-testid="continue-to-review-button"]');
    
    // Step 3: Order Review
    await expect(page.locator('[data-testid="checkout-step-review"]')).toBeVisible();
    await expect(page.locator('[data-testid="order-summary"]')).toBeVisible();
    await page.click('[data-testid="place-order-button"]');
    
    // Step 4: Order Confirmation
    await expect(page.locator('[data-testid="order-confirmation"]')).toBeVisible();
    await expect(page.locator('[data-testid="order-number"]')).toBeVisible();
  });

  test('should validate required checkout fields', async ({ page }) => {
    await page.click('[data-testid="cart-icon"]');
    await page.click('[data-testid="checkout-button"]');
    
    // Try to continue without filling required fields
    await page.click('[data-testid="continue-to-payment-button"]');
    
    // Verify validation errors
    await expect(page.locator('[data-testid="address-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="city-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="postal-code-error"]')).toBeVisible();
  });
});

// Test Suite: Vendor Dashboard
test.describe('🏪 Vendor Dashboard Workflows', () => {
  test.beforeEach(async ({ page }) => {
    await loginUser(page, testVendor.email, testVendor.password);
  });

  test('should access vendor dashboard', async ({ page }) => {
    await page.goto('/vendor/dashboard');
    
    // Verify dashboard elements
    await expect(page.locator('[data-testid="vendor-dashboard"]')).toBeVisible();
    await expect(page.locator('[data-testid="sales-overview"]')).toBeVisible();
    await expect(page.locator('[data-testid="recent-orders"]')).toBeVisible();
    await expect(page.locator('[data-testid="product-management"]')).toBeVisible();
  });

  test('should add new product', async ({ page }) => {
    await page.goto('/vendor/products');
    await page.click('[data-testid="add-product-button"]');
    
    // Fill product form
    await page.fill('[data-testid="product-name"]', testProduct.name);
    await page.fill('[data-testid="product-description"]', testProduct.description);
    await page.fill('[data-testid="product-price"]', testProduct.price.toString());
    await page.selectOption('[data-testid="product-category"]', testProduct.category);
    
    // Upload product image (mock)
    await page.setInputFiles('[data-testid="product-image"]', 'tests/fixtures/test-image.jpg');
    
    // Save product
    await page.click('[data-testid="save-product-button"]');
    
    // Verify product created
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Product created successfully');
    await expect(page.locator(`[data-testid="product-${testProduct.name}"]`)).toBeVisible();
  });

  test('should view and manage orders', async ({ page }) => {
    await page.goto('/vendor/orders');
    
    // Verify orders page
    await expect(page.locator('[data-testid="orders-table"]')).toBeVisible();
    
    // Update order status
    const firstOrder = page.locator('[data-testid^="order-row-"]').first();
    await firstOrder.locator('[data-testid="status-dropdown"]').selectOption('shipped');
    await firstOrder.locator('[data-testid="update-status-button"]').click();
    
    // Verify status updated
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Order status updated');
  });
});

// Test Suite: Responsive Design
test.describe('📱 Responsive Design Tests', () => {
  const viewports = [
    { name: 'Mobile', width: 375, height: 667 },
    { name: 'Tablet', width: 768, height: 1024 },
    { name: 'Desktop', width: 1920, height: 1080 }
  ];

  viewports.forEach(({ name, width, height }) => {
    test(`should display correctly on ${name}`, async ({ page }) => {
      await page.setViewportSize({ width, height });
      await page.goto('/');
      
      // Verify responsive navigation
      if (width < 768) {
        await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible();
      } else {
        await expect(page.locator('[data-testid="desktop-navigation"]')).toBeVisible();
      }
      
      // Verify content is accessible
      await expect(page.locator('[data-testid="main-content"]')).toBeVisible();
      await expect(page.locator('[data-testid="footer"]')).toBeVisible();
    });
  });
});

// Test Suite: Performance and Accessibility
test.describe('⚡ Performance and Accessibility', () => {
  test('should meet performance benchmarks', async ({ page }) => {
    await page.goto('/');
    
    // Measure page load time
    const startTime = Date.now();
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    // Verify reasonable load time (under 3 seconds)
    expect(loadTime).toBeLessThan(3000);
  });

  test('should be keyboard navigable', async ({ page }) => {
    await page.goto('/');
    
    // Test tab navigation
    await page.keyboard.press('Tab');
    await expect(page.locator(':focus')).toBeVisible();
    
    // Navigate through main elements
    for (let i = 0; i < 5; i++) {
      await page.keyboard.press('Tab');
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
    }
  });

  test('should have proper ARIA labels', async ({ page }) => {
    await page.goto('/');
    
    // Check for essential ARIA labels
    await expect(page.locator('[aria-label="Main navigation"]')).toBeVisible();
    await expect(page.locator('[aria-label="Search products"]')).toBeVisible();
    await expect(page.locator('[aria-label="Shopping cart"]')).toBeVisible();
  });
});
