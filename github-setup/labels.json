[{"name": "🎯 priority: critical", "description": "Critical issues that block development or production", "color": "d73a4a"}, {"name": "🔥 priority: high", "description": "High priority issues that should be addressed soon", "color": "ff6b6b"}, {"name": "📋 priority: medium", "description": "Medium priority issues for regular development", "color": "ffa726"}, {"name": "🌱 priority: low", "description": "Low priority issues for future consideration", "color": "4caf50"}, {"name": "🎨 frontend", "description": "Frontend development (Next.js, React, TypeScript)", "color": "61dafb"}, {"name": "🔧 backend", "description": "Backend development (NestJS, GraphQL, REST API)", "color": "e10098"}, {"name": "⛓️ blockchain", "description": "Blockchain development (PoS, Smart Contracts, Wallet)", "color": "9c27b0"}, {"name": "🗄️ database", "description": "Database related issues (PostgreSQL, TypeORM, Migrations)", "color": "336791"}, {"name": "🔐 auth", "description": "Authentication and authorization features", "color": "ff5722"}, {"name": "💳 payments", "description": "Payment processing and financial features", "color": "4caf50"}, {"name": "🛒 ecommerce", "description": "E-commerce functionality (products, orders, cart)", "color": "2196f3"}, {"name": "👥 user-management", "description": "User and vendor management features", "color": "795548"}, {"name": "🎨 ui/ux", "description": "User interface and user experience improvements", "color": "e91e63"}, {"name": "📱 mobile", "description": "Mobile responsiveness and mobile-specific features", "color": "607d8b"}, {"name": "🔍 search", "description": "Search functionality and recommendation engine", "color": "ff9800"}, {"name": "📊 analytics", "description": "Analytics, reporting, and data visualization", "color": "3f51b5"}, {"name": "🔔 notifications", "description": "Notification system and communication features", "color": "ff5722"}, {"name": "🎯 marketing", "description": "Marketing automation and promotion features", "color": "e91e63"}, {"name": "🤖 ai/ml", "description": "Artificial Intelligence and Machine Learning features", "color": "9c27b0"}, {"name": "🧪 testing", "description": "Testing related issues (unit, integration, e2e)", "color": "8bc34a"}, {"name": "📚 documentation", "description": "Documentation improvements and additions", "color": "ffc107"}, {"name": "🚀 deployment", "description": "Deployment and infrastructure related issues", "color": "3f51b5"}, {"name": "🔧 devops", "description": "DevOps, CI/CD, and development tooling", "color": "009688"}, {"name": "✨ enhancement", "description": "New feature or request", "color": "a2eeef"}, {"name": "🐛 bug", "description": "Something isn't working as expected", "color": "d73a4a"}, {"name": "🔄 refactor", "description": "Code refactoring and improvements", "color": "fbca04"}, {"name": "⚡ performance", "description": "Performance improvements and optimizations", "color": "ff9800"}, {"name": "🌍 localization", "description": "South African market specific features and localization", "color": "9e9e9e"}, {"name": "🔒 security", "description": "Security related issues and improvements", "color": "f44336"}, {"name": "🤝 help wanted", "description": "Extra attention is needed from contributors", "color": "008672"}, {"name": "👍 good first issue", "description": "Good for newcomers to the project", "color": "7057ff"}, {"name": "❓ question", "description": "Further information is requested", "color": "d876e3"}, {"name": "🚫 wontfix", "description": "This will not be worked on", "color": "ffffff"}, {"name": "📦 dependencies", "description": "Updates to project dependencies", "color": "0366d6"}, {"name": "🔧 configuration", "description": "Configuration and setup related issues", "color": "1d76db"}]