#!/bin/bash

# GitHub Setup Script for A Good Man's View E-commerce Platform
# This script creates labels, milestones, and issues from JSON files

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPO_OWNER="FreakyJay1"
REPO_NAME="agoodmansview_website"

echo -e "${BLUE}🚀 Starting GitHub Setup for A Good Man's View${NC}"
echo -e "${BLUE}Repository: ${REPO_OWNER}/${REPO_NAME}${NC}"
echo ""

# Check if gh CLI is installed and authenticated
if ! command -v gh &> /dev/null; then
    echo -e "${RED}❌ GitHub CLI (gh) is not installed${NC}"
    echo "Please install it from: https://cli.github.com/"
    exit 1
fi

if ! gh auth status &> /dev/null; then
    echo -e "${RED}❌ GitHub CLI is not authenticated${NC}"
    echo "Please run: gh auth login"
    exit 1
fi

echo -e "${GREEN}✅ GitHub CLI is installed and authenticated${NC}"

# Function to create labels
create_labels() {
    echo -e "${YELLOW}📋 Creating labels...${NC}"
    
    if [ ! -f "$SCRIPT_DIR/labels.json" ]; then
        echo -e "${RED}❌ labels.json not found${NC}"
        return 1
    fi
    
    # Read and create each label
    jq -r '.[] | @base64' "$SCRIPT_DIR/labels.json" | while read -r label; do
        _jq() {
            echo "$label" | base64 --decode | jq -r "$1"
        }
        
        name=$(_jq '.name')
        description=$(_jq '.description')
        color=$(_jq '.color')
        
        echo "Creating label: $name"
        gh label create "$name" --description "$description" --color "$color" --repo "$REPO_OWNER/$REPO_NAME" || true
    done
    
    echo -e "${GREEN}✅ Labels created successfully${NC}"
}

# Function to create milestones
create_milestones() {
    echo -e "${YELLOW}🎯 Creating milestones...${NC}"
    
    if [ ! -f "$SCRIPT_DIR/milestones.json" ]; then
        echo -e "${RED}❌ milestones.json not found${NC}"
        return 1
    fi
    
    # Read and create each milestone
    jq -r '.[] | @base64' "$SCRIPT_DIR/milestones.json" | while read -r milestone; do
        _jq() {
            echo "$milestone" | base64 --decode | jq -r "$1"
        }
        
        title=$(_jq '.title')
        description=$(_jq '.description')
        due_on=$(_jq '.due_on')
        
        echo "Creating milestone: $title"
        gh api repos/"$REPO_OWNER"/"$REPO_NAME"/milestones \
            --method POST \
            --field title="$title" \
            --field description="$description" \
            --field due_on="$due_on" \
            --field state="open" || true
    done
    
    echo -e "${GREEN}✅ Milestones created successfully${NC}"
}

# Function to create issues from a JSON file
create_issues_from_file() {
    local file=$1
    local phase_name=$2
    
    echo -e "${YELLOW}📝 Creating issues for $phase_name...${NC}"
    
    if [ ! -f "$file" ]; then
        echo -e "${RED}❌ $file not found${NC}"
        return 1
    fi
    
    # Read and create each issue
    jq -r '.[] | @base64' "$file" | while read -r issue; do
        _jq() {
            echo "$issue" | base64 --decode | jq -r "$1"
        }
        
        title=$(_jq '.title')
        body=$(_jq '.body')
        milestone=$(_jq '.milestone')
        
        # Get labels as comma-separated string
        labels=$(echo "$issue" | base64 --decode | jq -r '.labels | join(",")')
        
        echo "Creating issue: $title"
        gh issue create \
            --title "$title" \
            --body "$body" \
            --label "$labels" \
            --milestone "$milestone" \
            --repo "$REPO_OWNER/$REPO_NAME" || true
    done
    
    echo -e "${GREEN}✅ Issues for $phase_name created successfully${NC}"
}

# Main execution
main() {
    echo -e "${BLUE}Starting GitHub repository setup...${NC}"
    echo ""
    
    # Create labels
    create_labels
    echo ""
    
    # Create milestones
    create_milestones
    echo ""
    
    # Create issues for Phase 1
    create_issues_from_file "$SCRIPT_DIR/issues-phase1.json" "Phase 1: Foundation"
    echo ""
    
    # Create issues for Phase 2
    create_issues_from_file "$SCRIPT_DIR/issues-phase2.json" "Phase 2: Core Backend"
    echo ""
    
    echo -e "${GREEN}🎉 GitHub setup completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}📊 Summary:${NC}"
    echo "• Labels: 35 created"
    echo "• Milestones: 9 created"
    echo "• Issues: Phase 1 (6) + Phase 2 (5) = 11 created"
    echo ""
    echo -e "${BLUE}🔗 Next steps:${NC}"
    echo "1. Review issues and assign team members"
    echo "2. Set up project boards for tracking"
    echo "3. Configure branch protection rules"
    echo "4. Start development on Phase 1 issues"
    echo ""
    echo -e "${YELLOW}Note: Additional phases will be created as development progresses${NC}"
}

# Run main function
main
