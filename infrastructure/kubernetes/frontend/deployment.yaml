apiVersion: apps/v1
kind: Deployment
metadata:
  name: agoodmansview-frontend
  namespace: agoodmansview
  labels:
    app: agoodmansview-frontend
    component: frontend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: agoodmansview-frontend
  template:
    metadata:
      labels:
        app: agoodmansview-frontend
        component: frontend
    spec:
      containers:
      - name: frontend
        image: agoodmansview/frontend:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: NEXT_PUBLIC_API_URL
          value: "https://api.agoodmansview.co.za"
        - name: NEXT_PUBLIC_GRAPHQL_URL
          value: "https://api.agoodmansview.co.za/graphql"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"

---
apiVersion: v1
kind: Service
metadata:
  name: agoodmansview-frontend-service
  namespace: agoodmansview
spec:
  selector:
    app: agoodmansview-frontend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP
