{"name": "agoodmansview-website", "version": "1.0.0", "description": "Multi-vendor e-commerce platform with private blockchain for South African market", "type": "module", "private": true, "workspaces": ["frontend", "backend", "blockchain", "shared"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run start:dev", "dev:blockchain": "cd blockchain && npm run dev", "restart:backend": "./scripts/restart-backend.sh", "kill:port": "./scripts/kill-port.sh", "build": "npm run build:shared && npm run build:frontend && npm run build:backend && npm run build:blockchain", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build:blockchain": "cd blockchain && npm run build", "build:shared": "cd shared && npm run build", "test": "npm run test:unit && npm run test:e2e", "test:unit": "npm run test:frontend && npm run test:backend && npm run test:blockchain", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm run test", "test:blockchain": "cd blockchain && npm test", "test:e2e": "cd frontend && npm run test:e2e", "lint": "npm run lint:frontend && npm run lint:backend && npm run lint:blockchain", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "lint:blockchain": "cd blockchain && npm run lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "setup": "./scripts/npm-wrapper.sh install && ./scripts/npm-wrapper.sh run setup:frontend && ./scripts/npm-wrapper.sh run setup:backend && ./scripts/npm-wrapper.sh run setup:blockchain", "setup:frontend": "cd frontend && ../scripts/npm-wrapper.sh install", "setup:backend": "cd backend && ../scripts/npm-wrapper.sh install", "setup:blockchain": "cd blockchain && ../scripts/npm-wrapper.sh install", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules blockchain/node_modules shared/node_modules", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "deploy:dev": "npm run build && npm run docker:build && npm run docker:up", "migrate": "cd backend && npm run migration:run", "seed": "cd backend && npm run seed", "docs:generate": "npm run docs:api && npm run docs:graphql", "docs:api": "cd backend && npm run docs:generate", "docs:graphql": "cd backend && npm run graphql:schema", "pipeline:test": "./scripts/test-pipeline.sh", "pipeline:test:quick": "./scripts/test-pipeline.sh --quick", "pipeline:test:full": "./scripts/test-pipeline.sh --full", "pipeline:test:coverage": "./scripts/test-pipeline.sh --coverage", "pipeline:fix": "./scripts/test-pipeline.sh --fix", "test:collect-results": "node scripts/collect-test-results.js", "test:docker:up": "docker-compose -f docker-compose.test.yml up -d", "test:docker:down": "docker-compose -f docker-compose.test.yml down", "lighthouse": "cd frontend && npm run lighthouse", "a11y:test": "cd frontend && npm run a11y:test", "security:scan": "npm audit --audit-level=moderate && cd frontend && npm audit --audit-level=moderate && cd ../backend && npm audit --audit-level=moderate"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "9.29.0", "concurrently": "^9.1.2", "lint-staged": "^16.1.2", "prettier": "^3.5.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/agoodmansview_website.git"}, "keywords": ["e-commerce", "blockchain", "nextjs", "<PERSON><PERSON><PERSON>", "graphql", "south-africa", "multi-vendor", "proof-of-stake"], "author": "A Good Man's View Team", "license": "MIT", "overrides": {"glob": "^10.4.5", "test-exclude": "^7.0.1", "rimraf": "^6.0.1"}}