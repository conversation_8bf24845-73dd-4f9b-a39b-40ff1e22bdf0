
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A Good Man's View - Test Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .content { padding: 30px; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric-card { background: #f8f9fa; border-radius: 8px; padding: 20px; border-left: 4px solid #007bff; }
        .metric-value { font-size: 2em; font-weight: bold; color: #007bff; }
        .metric-label { color: #6c757d; margin-top: 5px; }
        .status-passed { color: #28a745; }
        .status-failed { color: #dc3545; }
        .progress-bar { background: #e9ecef; border-radius: 4px; height: 8px; margin: 10px 0; }
        .progress-fill { background: #28a745; height: 100%; border-radius: 4px; transition: width 0.3s ease; }
        .section { margin: 30px 0; }
        .section h2 { color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 A Good Man's View - Test Report</h1>
            <p>Generated on 6/24/2025, 9:06:47 AM</p>
        </div>
        <div class="content">
            <div class="section">
                <h2>📊 Overall Summary</h2>
                <div class="metric-grid">
                    <div class="metric-card">
                        <div class="metric-value status-passed">
                            PASSED
                        </div>
                        <div class="metric-label">Overall Status</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">0</div>
                        <div class="metric-label">Total Tests</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">0ms</div>
                        <div class="metric-label">Total Duration</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">0%</div>
                        <div class="metric-label">Code Coverage</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🧪 Unit Tests</h2>
                <div class="metric-grid">
                    <div class="metric-card">
                        <div class="metric-value status-passed">0</div>
                        <div class="metric-label">Passed</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value status-failed">0</div>
                        <div class="metric-label">Failed</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">0</div>
                        <div class="metric-label">Skipped</div>
                    </div>
                </div>
            </div>
            
            
            
            
        </div>
    </div>
</body>
</html>