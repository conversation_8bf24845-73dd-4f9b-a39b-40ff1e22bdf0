#!/bin/bash

# Git Commit Size Checker
# This script helps identify large files and build artifacts before committing

set -e

echo "🔍 Git Commit Size Checker"
echo "=========================="

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${RED}Error: Not in a git repository${NC}"
    exit 1
fi

# Function to check file size
check_file_size() {
    local file="$1"
    local size_bytes=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0)
    local size_mb=$((size_bytes / 1024 / 1024))
    
    if [ $size_bytes -gt 10485760 ]; then  # 10MB
        echo -e "${RED}❌ Large file detected: $file (${size_mb}MB)${NC}"
        return 1
    elif [ $size_bytes -gt 1048576 ]; then  # 1MB
        echo -e "${YELLOW}⚠️  Medium file: $file (${size_mb}MB)${NC}"
        return 0
    fi
    return 0
}

# Check for build artifacts
echo "🏗️  Checking for build artifacts..."
build_artifacts_found=false

# Common build artifact patterns
patterns=(
    "dist/"
    "build/"
    ".next/"
    "*.js.map"
    "dist/**/*.d.ts"
    "build/**/*.d.ts"
    "*.tsbuildinfo"
    "node_modules/"
    ".cache/"
    "*.pack"
    "*.pack.gz"
)

for pattern in "${patterns[@]}"; do
    if git diff --cached --name-only | grep -q "$pattern"; then
        echo -e "${RED}❌ Build artifact detected: $pattern${NC}"
        build_artifacts_found=true
    fi
done

if [ "$build_artifacts_found" = false ]; then
    echo -e "${GREEN}✅ No build artifacts found${NC}"
fi

# Check file sizes
echo ""
echo "📏 Checking file sizes..."
large_files_found=false

while IFS= read -r file; do
    if [ -f "$file" ]; then
        if ! check_file_size "$file"; then
            large_files_found=true
        fi
    fi
done < <(git diff --cached --name-only)

if [ "$large_files_found" = false ]; then
    echo -e "${GREEN}✅ No large files found${NC}"
fi

# Show staged files summary
echo ""
echo "📋 Staged files summary:"
staged_count=$(git diff --cached --name-only | wc -l)
echo "Total staged files: $staged_count"

if [ $staged_count -gt 50 ]; then
    echo -e "${YELLOW}⚠️  Large number of staged files. Consider smaller commits.${NC}"
fi

# Show total size of staged changes
echo ""
echo "📊 Repository statistics:"
echo "Current repository size: $(du -sh .git/ | cut -f1)"
echo "Total tracked files: $(git ls-files | wc -l)"

# Final recommendation
echo ""
echo "💡 Recommendations:"

if [ "$build_artifacts_found" = true ]; then
    echo -e "${RED}❌ Remove build artifacts before committing${NC}"
    echo "   Use: git rm --cached <file> or update .gitignore"
fi

if [ "$large_files_found" = true ]; then
    echo -e "${RED}❌ Consider using Git LFS for large files${NC}"
    echo "   Or exclude them if they're not essential"
fi

if [ "$build_artifacts_found" = false ] && [ "$large_files_found" = false ]; then
    echo -e "${GREEN}✅ Commit looks good!${NC}"
    exit 0
else
    echo -e "${RED}❌ Issues found. Please review before committing.${NC}"
    exit 1
fi
