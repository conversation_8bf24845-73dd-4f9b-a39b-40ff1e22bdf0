#!/usr/bin/env node

/**
 * 📊 Test Results Collector
 * 
 * This script collects and aggregates test results from various testing tools
 * and generates a comprehensive report for CI/CD pipeline analysis.
 * 
 * Features:
 * - Aggregates results from multiple test runners
 * - Generates unified coverage reports
 * - Creates performance summaries
 * - Exports results in multiple formats (JSON, HTML, Markdown)
 */

import fs from 'fs';
import path from 'path';

// Configuration
const RESULTS_DIR = './results';
const OUTPUT_DIR = './results/aggregated';
const REPORT_FORMATS = ['json', 'html', 'markdown'];

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * Utility functions
 */
const utils = {
  // Read JSON file safely
  readJsonFile(filePath) {
    try {
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        return JSON.parse(content);
      }
    } catch (error) {
      console.warn(`⚠️  Failed to read ${filePath}: ${error.message}`);
    }
    return null;
  },

  // Calculate percentage
  percentage(value, total) {
    return total > 0 ? Math.round((value / total) * 100) : 0;
  },

  // Format duration
  formatDuration(ms) {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  },

  // Get file size in human readable format
  getFileSize(filePath) {
    try {
      const stats = fs.statSync(filePath);
      const bytes = stats.size;
      if (bytes < 1024) return `${bytes}B`;
      if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
      return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
    } catch {
      return 'Unknown';
    }
  }
};

/**
 * Test result collectors
 */
const collectors = {
  // Collect unit test results (Jest/Vitest)
  collectUnitTests() {
    console.log('📊 Collecting unit test results...');
    
    const results = {
      frontend: utils.readJsonFile(path.join(RESULTS_DIR, 'frontend-unit-results.json')),
      backend: utils.readJsonFile(path.join(RESULTS_DIR, 'backend-unit-results.json')),
      blockchain: utils.readJsonFile(path.join(RESULTS_DIR, 'blockchain-unit-results.json')),
      shared: utils.readJsonFile(path.join(RESULTS_DIR, 'shared-unit-results.json'))
    };

    const summary = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      duration: 0,
      coverage: {
        lines: 0,
        functions: 0,
        branches: 0,
        statements: 0
      }
    };

    Object.entries(results).forEach(([workspace, result]) => {
      if (result) {
        summary.totalTests += result.numTotalTests || 0;
        summary.passedTests += result.numPassedTests || 0;
        summary.failedTests += result.numFailedTests || 0;
        summary.skippedTests += result.numPendingTests || 0;
        summary.duration += result.testResults?.reduce((acc, test) => acc + (test.perfStats?.runtime || 0), 0) || 0;
        
        // Aggregate coverage
        if (result.coverageMap) {
          const coverage = result.coverageMap.getCoverageSummary();
          summary.coverage.lines += coverage.lines.pct || 0;
          summary.coverage.functions += coverage.functions.pct || 0;
          summary.coverage.branches += coverage.branches.pct || 0;
          summary.coverage.statements += coverage.statements.pct || 0;
        }
      }
    });

    // Average coverage across workspaces
    const workspaceCount = Object.values(results).filter(r => r).length;
    if (workspaceCount > 0) {
      summary.coverage.lines = Math.round(summary.coverage.lines / workspaceCount);
      summary.coverage.functions = Math.round(summary.coverage.functions / workspaceCount);
      summary.coverage.branches = Math.round(summary.coverage.branches / workspaceCount);
      summary.coverage.statements = Math.round(summary.coverage.statements / workspaceCount);
    }

    return { results, summary };
  },

  // Collect E2E test results (Playwright)
  collectE2eTests() {
    console.log('🎭 Collecting E2E test results...');
    
    const playwrightResults = utils.readJsonFile(path.join(RESULTS_DIR, 'playwright-results.json'));
    
    if (!playwrightResults) {
      return { results: null, summary: null };
    }

    const summary = {
      totalTests: playwrightResults.suites?.reduce((acc, suite) => 
        acc + (suite.specs?.length || 0), 0) || 0,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      duration: playwrightResults.stats?.duration || 0,
      browsers: []
    };

    // Analyze test results by browser
    playwrightResults.suites?.forEach(suite => {
      suite.specs?.forEach(spec => {
        spec.tests?.forEach(test => {
          if (test.status === 'passed') summary.passedTests++;
          else if (test.status === 'failed') summary.failedTests++;
          else summary.skippedTests++;
        });
      });
    });

    return { results: playwrightResults, summary };
  },

  // Collect performance test results (Lighthouse)
  collectPerformanceTests() {
    console.log('⚡ Collecting performance test results...');
    
    const lighthouseResults = utils.readJsonFile(path.join(RESULTS_DIR, 'lighthouse-report.json'));
    
    if (!lighthouseResults) {
      return { results: null, summary: null };
    }

    const summary = {
      performance: lighthouseResults.categories?.performance?.score * 100 || 0,
      accessibility: lighthouseResults.categories?.accessibility?.score * 100 || 0,
      bestPractices: lighthouseResults.categories?.['best-practices']?.score * 100 || 0,
      seo: lighthouseResults.categories?.seo?.score * 100 || 0,
      metrics: {
        fcp: lighthouseResults.audits?.['first-contentful-paint']?.numericValue || 0,
        lcp: lighthouseResults.audits?.['largest-contentful-paint']?.numericValue || 0,
        cls: lighthouseResults.audits?.['cumulative-layout-shift']?.numericValue || 0,
        tti: lighthouseResults.audits?.['interactive']?.numericValue || 0
      }
    };

    return { results: lighthouseResults, summary };
  },

  // Collect accessibility test results
  collectAccessibilityTests() {
    console.log('♿ Collecting accessibility test results...');
    
    const a11yResults = utils.readJsonFile(path.join(RESULTS_DIR, 'a11y-report.json'));
    
    if (!a11yResults) {
      return { results: null, summary: null };
    }

    const summary = {
      totalIssues: a11yResults.issues?.length || 0,
      errorCount: a11yResults.issues?.filter(issue => issue.type === 'error').length || 0,
      warningCount: a11yResults.issues?.filter(issue => issue.type === 'warning').length || 0,
      noticeCount: a11yResults.issues?.filter(issue => issue.type === 'notice').length || 0,
      score: Math.max(0, 100 - (a11yResults.issues?.length || 0) * 5) // Simple scoring
    };

    return { results: a11yResults, summary };
  },

  // Collect security test results
  collectSecurityTests() {
    console.log('🔒 Collecting security test results...');
    
    const securityResults = utils.readJsonFile(path.join(RESULTS_DIR, 'security-report.json'));
    
    if (!securityResults) {
      return { results: null, summary: null };
    }

    const summary = {
      totalAlerts: securityResults.site?.[0]?.alerts?.length || 0,
      highRisk: securityResults.site?.[0]?.alerts?.filter(alert => alert.riskdesc?.includes('High')).length || 0,
      mediumRisk: securityResults.site?.[0]?.alerts?.filter(alert => alert.riskdesc?.includes('Medium')).length || 0,
      lowRisk: securityResults.site?.[0]?.alerts?.filter(alert => alert.riskdesc?.includes('Low')).length || 0,
      informational: securityResults.site?.[0]?.alerts?.filter(alert => alert.riskdesc?.includes('Informational')).length || 0
    };

    return { results: securityResults, summary };
  }
};

/**
 * Report generators
 */
const generators = {
  // Generate JSON report
  generateJsonReport(aggregatedResults) {
    const jsonReport = {
      timestamp: new Date().toISOString(),
      summary: {
        overall: {
          status: aggregatedResults.unitTests.summary.failedTests === 0 && 
                  aggregatedResults.e2eTests.summary?.failedTests === 0 ? 'PASSED' : 'FAILED',
          totalTests: (aggregatedResults.unitTests.summary.totalTests || 0) + 
                     (aggregatedResults.e2eTests.summary?.totalTests || 0),
          duration: (aggregatedResults.unitTests.summary.duration || 0) + 
                   (aggregatedResults.e2eTests.summary?.duration || 0)
        },
        coverage: aggregatedResults.unitTests.summary.coverage,
        performance: aggregatedResults.performanceTests.summary,
        accessibility: aggregatedResults.accessibilityTests.summary,
        security: aggregatedResults.securityTests.summary
      },
      details: aggregatedResults
    };

    const outputPath = path.join(OUTPUT_DIR, 'test-report.json');
    fs.writeFileSync(outputPath, JSON.stringify(jsonReport, null, 2));
    console.log(`✅ JSON report generated: ${outputPath}`);
    
    return jsonReport;
  },

  // Generate HTML report
  generateHtmlReport(aggregatedResults) {
    const htmlTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A Good Man's View - Test Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .content { padding: 30px; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric-card { background: #f8f9fa; border-radius: 8px; padding: 20px; border-left: 4px solid #007bff; }
        .metric-value { font-size: 2em; font-weight: bold; color: #007bff; }
        .metric-label { color: #6c757d; margin-top: 5px; }
        .status-passed { color: #28a745; }
        .status-failed { color: #dc3545; }
        .progress-bar { background: #e9ecef; border-radius: 4px; height: 8px; margin: 10px 0; }
        .progress-fill { background: #28a745; height: 100%; border-radius: 4px; transition: width 0.3s ease; }
        .section { margin: 30px 0; }
        .section h2 { color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 A Good Man's View - Test Report</h1>
            <p>Generated on ${new Date().toLocaleString()}</p>
        </div>
        <div class="content">
            <div class="section">
                <h2>📊 Overall Summary</h2>
                <div class="metric-grid">
                    <div class="metric-card">
                        <div class="metric-value ${aggregatedResults.unitTests.summary.failedTests === 0 ? 'status-passed' : 'status-failed'}">
                            ${aggregatedResults.unitTests.summary.failedTests === 0 ? 'PASSED' : 'FAILED'}
                        </div>
                        <div class="metric-label">Overall Status</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${(aggregatedResults.unitTests.summary.totalTests || 0) + (aggregatedResults.e2eTests.summary?.totalTests || 0)}</div>
                        <div class="metric-label">Total Tests</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${utils.formatDuration((aggregatedResults.unitTests.summary.duration || 0) + (aggregatedResults.e2eTests.summary?.duration || 0))}</div>
                        <div class="metric-label">Total Duration</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${aggregatedResults.unitTests.summary.coverage.lines}%</div>
                        <div class="metric-label">Code Coverage</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${aggregatedResults.unitTests.summary.coverage.lines}%"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🧪 Unit Tests</h2>
                <div class="metric-grid">
                    <div class="metric-card">
                        <div class="metric-value status-passed">${aggregatedResults.unitTests.summary.passedTests}</div>
                        <div class="metric-label">Passed</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value status-failed">${aggregatedResults.unitTests.summary.failedTests}</div>
                        <div class="metric-label">Failed</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${aggregatedResults.unitTests.summary.skippedTests}</div>
                        <div class="metric-label">Skipped</div>
                    </div>
                </div>
            </div>
            
            ${aggregatedResults.performanceTests.summary ? `
            <div class="section">
                <h2>⚡ Performance</h2>
                <div class="metric-grid">
                    <div class="metric-card">
                        <div class="metric-value">${Math.round(aggregatedResults.performanceTests.summary.performance)}</div>
                        <div class="metric-label">Performance Score</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${aggregatedResults.performanceTests.summary.performance}%"></div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${utils.formatDuration(aggregatedResults.performanceTests.summary.metrics.fcp)}</div>
                        <div class="metric-label">First Contentful Paint</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${utils.formatDuration(aggregatedResults.performanceTests.summary.metrics.lcp)}</div>
                        <div class="metric-label">Largest Contentful Paint</div>
                    </div>
                </div>
            </div>
            ` : ''}
            
            ${aggregatedResults.accessibilityTests.summary ? `
            <div class="section">
                <h2>♿ Accessibility</h2>
                <div class="metric-grid">
                    <div class="metric-card">
                        <div class="metric-value">${aggregatedResults.accessibilityTests.summary.score}</div>
                        <div class="metric-label">Accessibility Score</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${aggregatedResults.accessibilityTests.summary.score}%"></div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value status-failed">${aggregatedResults.accessibilityTests.summary.errorCount}</div>
                        <div class="metric-label">Errors</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${aggregatedResults.accessibilityTests.summary.warningCount}</div>
                        <div class="metric-label">Warnings</div>
                    </div>
                </div>
            </div>
            ` : ''}
        </div>
    </div>
</body>
</html>`;

    const outputPath = path.join(OUTPUT_DIR, 'test-report.html');
    fs.writeFileSync(outputPath, htmlTemplate);
    console.log(`✅ HTML report generated: ${outputPath}`);
  },

  // Generate Markdown report
  generateMarkdownReport(aggregatedResults) {
    const markdownTemplate = `# 🚀 A Good Man's View - Test Report

*Generated on ${new Date().toLocaleString()}*

## 📊 Overall Summary

| Metric | Value |
|--------|-------|
| **Status** | ${aggregatedResults.unitTests.summary.failedTests === 0 ? '✅ PASSED' : '❌ FAILED'} |
| **Total Tests** | ${(aggregatedResults.unitTests.summary.totalTests || 0) + (aggregatedResults.e2eTests.summary?.totalTests || 0)} |
| **Duration** | ${utils.formatDuration((aggregatedResults.unitTests.summary.duration || 0) + (aggregatedResults.e2eTests.summary?.duration || 0))} |
| **Code Coverage** | ${aggregatedResults.unitTests.summary.coverage.lines}% |

## 🧪 Unit Tests

| Workspace | Passed | Failed | Skipped | Coverage |
|-----------|--------|--------|---------|----------|
| Frontend | ${aggregatedResults.unitTests.results.frontend?.numPassedTests || 0} | ${aggregatedResults.unitTests.results.frontend?.numFailedTests || 0} | ${aggregatedResults.unitTests.results.frontend?.numPendingTests || 0} | ${aggregatedResults.unitTests.summary.coverage.lines}% |
| Backend | ${aggregatedResults.unitTests.results.backend?.numPassedTests || 0} | ${aggregatedResults.unitTests.results.backend?.numFailedTests || 0} | ${aggregatedResults.unitTests.results.backend?.numPendingTests || 0} | ${aggregatedResults.unitTests.summary.coverage.lines}% |
| Blockchain | ${aggregatedResults.unitTests.results.blockchain?.numPassedTests || 0} | ${aggregatedResults.unitTests.results.blockchain?.numFailedTests || 0} | ${aggregatedResults.unitTests.results.blockchain?.numPendingTests || 0} | ${aggregatedResults.unitTests.summary.coverage.lines}% |
| Shared | ${aggregatedResults.unitTests.results.shared?.numPassedTests || 0} | ${aggregatedResults.unitTests.results.shared?.numFailedTests || 0} | ${aggregatedResults.unitTests.results.shared?.numPendingTests || 0} | ${aggregatedResults.unitTests.summary.coverage.lines}% |

${aggregatedResults.e2eTests.summary ? `
## 🎭 End-to-End Tests

| Metric | Value |
|--------|-------|
| **Total Tests** | ${aggregatedResults.e2eTests.summary.totalTests} |
| **Passed** | ✅ ${aggregatedResults.e2eTests.summary.passedTests} |
| **Failed** | ❌ ${aggregatedResults.e2eTests.summary.failedTests} |
| **Skipped** | ⏭️ ${aggregatedResults.e2eTests.summary.skippedTests} |
| **Duration** | ${utils.formatDuration(aggregatedResults.e2eTests.summary.duration)} |
` : ''}

${aggregatedResults.performanceTests.summary ? `
## ⚡ Performance Metrics

| Metric | Value | Status |
|--------|-------|--------|
| **Performance Score** | ${Math.round(aggregatedResults.performanceTests.summary.performance)} | ${aggregatedResults.performanceTests.summary.performance >= 90 ? '✅' : aggregatedResults.performanceTests.summary.performance >= 70 ? '⚠️' : '❌'} |
| **First Contentful Paint** | ${utils.formatDuration(aggregatedResults.performanceTests.summary.metrics.fcp)} | ${aggregatedResults.performanceTests.summary.metrics.fcp <= 2000 ? '✅' : '⚠️'} |
| **Largest Contentful Paint** | ${utils.formatDuration(aggregatedResults.performanceTests.summary.metrics.lcp)} | ${aggregatedResults.performanceTests.summary.metrics.lcp <= 3000 ? '✅' : '⚠️'} |
| **Cumulative Layout Shift** | ${aggregatedResults.performanceTests.summary.metrics.cls.toFixed(3)} | ${aggregatedResults.performanceTests.summary.metrics.cls <= 0.1 ? '✅' : '⚠️'} |
` : ''}

${aggregatedResults.accessibilityTests.summary ? `
## ♿ Accessibility Report

| Metric | Count |
|--------|-------|
| **Score** | ${aggregatedResults.accessibilityTests.summary.score}/100 |
| **Errors** | ❌ ${aggregatedResults.accessibilityTests.summary.errorCount} |
| **Warnings** | ⚠️ ${aggregatedResults.accessibilityTests.summary.warningCount} |
| **Notices** | ℹ️ ${aggregatedResults.accessibilityTests.summary.noticeCount} |
` : ''}

${aggregatedResults.securityTests.summary ? `
## 🔒 Security Analysis

| Risk Level | Count |
|------------|-------|
| **High** | ❌ ${aggregatedResults.securityTests.summary.highRisk} |
| **Medium** | ⚠️ ${aggregatedResults.securityTests.summary.mediumRisk} |
| **Low** | ℹ️ ${aggregatedResults.securityTests.summary.lowRisk} |
| **Informational** | 📝 ${aggregatedResults.securityTests.summary.informational} |
` : ''}

---

*Report generated by A Good Man's View CI/CD Pipeline*`;

    const outputPath = path.join(OUTPUT_DIR, 'test-report.md');
    fs.writeFileSync(outputPath, markdownTemplate);
    console.log(`✅ Markdown report generated: ${outputPath}`);
  }
};

/**
 * Main execution
 */
async function main() {
  console.log('🚀 Starting test results collection...\n');

  try {
    // Collect all test results
    const aggregatedResults = {
      unitTests: collectors.collectUnitTests(),
      e2eTests: collectors.collectE2eTests(),
      performanceTests: collectors.collectPerformanceTests(),
      accessibilityTests: collectors.collectAccessibilityTests(),
      securityTests: collectors.collectSecurityTests()
    };

    console.log('\n📊 Generating reports...');

    // Generate reports in all formats
    const jsonReport = generators.generateJsonReport(aggregatedResults);
    generators.generateHtmlReport(aggregatedResults);
    generators.generateMarkdownReport(aggregatedResults);

    console.log('\n✅ Test results collection completed successfully!');
    console.log(`📁 Reports available in: ${OUTPUT_DIR}`);

    // Exit with appropriate code based on test results
    const hasFailures = aggregatedResults.unitTests.summary.failedTests > 0 || 
                       (aggregatedResults.e2eTests.summary?.failedTests || 0) > 0;
    
    if (hasFailures) {
      console.log('\n❌ Some tests failed - check the reports for details');
      process.exit(1);
    } else {
      console.log('\n🎉 All tests passed!');
      process.exit(0);
    }

  } catch (error) {
    console.error('❌ Error collecting test results:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
