#!/bin/bash

# Fix NPM Script
# This script fixes the npm issue by using the working NVM npm installation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}NPM Fix Script${NC}"
echo -e "${BLUE}==============${NC}"

# Check if NVM is available
if [ ! -d "$HOME/.nvm" ]; then
    echo -e "${RED}Error: NVM not found. Please install NVM first.${NC}"
    exit 1
fi

# Find working npm
WORKING_NPM=""
if [ -f "$HOME/.nvm/versions/node/v22.14.0/bin/npm" ]; then
    WORKING_NPM="$HOME/.nvm/versions/node/v22.14.0/bin/npm"
    echo -e "${GREEN}Found working npm at: $WORKING_NPM${NC}"
elif [ -f "$HOME/.nvm/versions/node/v18.20.8/bin/npm" ]; then
    WORKING_NPM="$HOME/.nvm/versions/node/v18.20.8/bin/npm"
    echo -e "${GREEN}Found working npm at: $WORKING_NPM${NC}"
else
    echo -e "${RED}Error: No working npm found in NVM installations.${NC}"
    exit 1
fi

# Create npm wrapper script
echo -e "${YELLOW}Creating npm wrapper script...${NC}"
cat > scripts/npm-wrapper.sh << 'EOF'
#!/bin/bash
# NPM Wrapper Script - uses working NVM npm instead of broken system npm

WORKING_NPM="$HOME/.nvm/versions/node/v22.14.0/bin/npm"

if [ ! -f "$WORKING_NPM" ]; then
    WORKING_NPM="$HOME/.nvm/versions/node/v18.20.8/bin/npm"
fi

if [ ! -f "$WORKING_NPM" ]; then
    echo "Error: No working npm found"
    exit 1
fi

# Use NODE_PATH to ensure proper module resolution
export NODE_PATH="$HOME/.nvm/versions/node/v22.14.0/lib/node_modules:$NODE_PATH"

exec "$WORKING_NPM" "$@"
EOF

chmod +x scripts/npm-wrapper.sh

# Test the wrapper
echo -e "${YELLOW}Testing npm wrapper...${NC}"
if ./scripts/npm-wrapper.sh --version > /dev/null 2>&1; then
    echo -e "${GREEN}✓ NPM wrapper is working!${NC}"
    echo -e "${GREEN}✓ NPM version: $(./scripts/npm-wrapper.sh --version)${NC}"
else
    echo -e "${RED}✗ NPM wrapper failed${NC}"
    exit 1
fi

# Update package.json scripts to use the wrapper
echo -e "${YELLOW}Updating package.json scripts...${NC}"

# Create a backup
cp package.json package.json.backup

# Update scripts to use the wrapper
cat > scripts/update-package-scripts.js << 'EOF'
const fs = require('fs');
const path = require('path');

const packagePath = path.join(process.cwd(), 'package.json');
const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

// Update scripts to use npm wrapper
const scriptsToUpdate = {
    'setup': './scripts/npm-wrapper.sh install && ./scripts/npm-wrapper.sh run setup:frontend && ./scripts/npm-wrapper.sh run setup:backend && ./scripts/npm-wrapper.sh run setup:blockchain',
    'setup:frontend': 'cd frontend && ../scripts/npm-wrapper.sh install',
    'setup:backend': 'cd backend && ../scripts/npm-wrapper.sh install',
    'setup:blockchain': 'cd blockchain && ../scripts/npm-wrapper.sh install'
};

Object.assign(pkg.scripts, scriptsToUpdate);

fs.writeFileSync(packagePath, JSON.stringify(pkg, null, 2) + '\n');
console.log('✓ Updated package.json scripts');
EOF

node scripts/update-package-scripts.js

echo -e "${GREEN}✓ NPM fix completed successfully!${NC}"
echo -e "${BLUE}Usage:${NC}"
echo -e "  ${YELLOW}./scripts/npm-wrapper.sh <npm-command>${NC} - Use npm directly"
echo -e "  ${YELLOW}./scripts/npm-workaround.sh <command>${NC} - Use project commands (dev, build, test, etc.)"
echo -e ""
echo -e "${BLUE}Examples:${NC}"
echo -e "  ${YELLOW}./scripts/npm-wrapper.sh install${NC}"
echo -e "  ${YELLOW}./scripts/npm-wrapper.sh run dev${NC}"
echo -e "  ${YELLOW}./scripts/npm-workaround.sh dev${NC}"
