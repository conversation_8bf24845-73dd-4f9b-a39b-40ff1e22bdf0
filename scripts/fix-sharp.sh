#!/bin/bash

# Fix Sharp Installation Script
# This script resolves Sharp installation issues by trying multiple approaches

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Sharp Installation Fix Script${NC}"
echo -e "${BLUE}============================${NC}"

# Function to check if npm is available
check_npm() {
    if command -v npm >/dev/null 2>&1; then
        echo -e "${GREEN}✓ npm is available${NC}"
        return 0
    else
        echo -e "${RED}✗ npm is not available${NC}"
        return 1
    fi
}

# Function to install Sharp with specific configuration
install_sharp_configured() {
    echo -e "${YELLOW}Attempting to install <PERSON> with optimized configuration...${NC}"
    
    # Set Sharp-specific environment variables
    export SHARP_IGNORE_GLOBAL_LIBVIPS=1
    export SHARP_FORCE_GLOBAL_LIBVIPS=false
    export npm_config_sharp_binary_host="https://github.com/lovell/sharp-libvips/releases/download/"
    export npm_config_sharp_libvips_binary_host="https://github.com/lovell/sharp-libvips/releases/download/"
    
    # Try installing Sharp directly
    if npm install sharp --timeout=300000 --fetch-timeout=300000 --fetch-retries=5; then
        echo -e "${GREEN}✓ Sharp installed successfully${NC}"
        return 0
    else
        echo -e "${RED}✗ Sharp installation failed${NC}"
        return 1
    fi
}

# Function to install Sharp using prebuild
install_sharp_prebuild() {
    echo -e "${YELLOW}Attempting to install Sharp using prebuild...${NC}"
    
    # Install prebuild-install first
    npm install prebuild-install --timeout=300000
    
    # Try installing Sharp with prebuild
    if npm install sharp --build-from-source=false --timeout=300000; then
        echo -e "${GREEN}✓ Sharp installed using prebuild${NC}"
        return 0
    else
        echo -e "${RED}✗ Sharp prebuild installation failed${NC}"
        return 1
    fi
}

# Function to disable Sharp and use fallback
disable_sharp() {
    echo -e "${YELLOW}Disabling Sharp and configuring fallback image optimization...${NC}"
    
    # Update Next.js config to explicitly disable Sharp
    cat > frontend/next.config.js << 'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Note: appDir is now stable in Next.js 13.4+, no longer experimental
  images: {
    domains: ['localhost', 'agoodmansview.co.za'],
    formats: ['image/webp', 'image/avif'],
    // Disable Sharp optimization due to installation issues
    // Use Next.js built-in image optimization instead
    loader: 'default',
    unoptimized: false,
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY || 'default-value',
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000',
    NEXT_PUBLIC_GRAPHQL_URL: process.env.NEXT_PUBLIC_GRAPHQL_URL || 'http://localhost:4000',
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/v1/:path*`,
      },
      {
        source: '/graphql',
        destination: `${process.env.NEXT_PUBLIC_GRAPHQL_URL || 'http://localhost:4000'}/graphql`,
      },
    ];
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
    ];
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }
    return config;
  },
};

module.exports = nextConfig;
EOF
    
    echo -e "${GREEN}✓ Next.js configured to use fallback image optimization${NC}"
}

# Main execution
echo -e "${YELLOW}Checking npm availability...${NC}"
if ! check_npm; then
    echo -e "${RED}Error: npm is required but not available.${NC}"
    echo -e "${YELLOW}Please install Node.js and npm, or use the project's npm wrapper scripts.${NC}"
    exit 1
fi

echo -e "${YELLOW}Attempting Sharp installation fixes...${NC}"

# Try method 1: Configured installation
if install_sharp_configured; then
    echo -e "${GREEN}✓ Sharp installation completed successfully!${NC}"
    exit 0
fi

# Try method 2: Prebuild installation
if install_sharp_prebuild; then
    echo -e "${GREEN}✓ Sharp installation completed using prebuild!${NC}"
    exit 0
fi

# Method 3: Disable Sharp and use fallback
echo -e "${YELLOW}Sharp installation failed. Configuring fallback image optimization...${NC}"
disable_sharp

echo -e "${GREEN}✓ Image optimization configured to work without Sharp${NC}"
echo -e "${BLUE}Note: Image optimization will use Next.js built-in fallback instead of Sharp${NC}"
echo -e "${BLUE}This may be slightly slower but will work reliably${NC}"

exit 0
