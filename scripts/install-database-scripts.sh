#!/bin/bash

# Database Scripts Installer
# This script downloads and installs the database setup scripts for any NestJS project

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "\n${BLUE}🔧 $1${NC}"
}

# Configuration
REPO_BASE_URL="https://raw.githubusercontent.com/your-username/your-repo/main/backend"
INSTALL_DIR="."
CREATE_DIRS=true

# Show usage
show_usage() {
    echo "Database Scripts Installer for NestJS + TypeORM + PostgreSQL"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -d, --dir DIRECTORY     Installation directory (default: current)"
    echo "  --no-dirs              Don't create directory structure"
    echo "  --help                 Show this help message"
    echo ""
    echo "This script will download and install:"
    echo "  - setup-database.sh     (Automated database setup)"
    echo "  - reset-database.sh     (Database reset script)"
    echo "  - test-connection.ts    (Connection testing)"
    echo "  - generate-project-template.sh (Project generator)"
    echo "  - Documentation files"
}

# Parse arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--dir)
                INSTALL_DIR="$2"
                shift 2
                ;;
            --no-dirs)
                CREATE_DIRS=false
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Create directory structure
create_directories() {
    if [[ "$CREATE_DIRS" != true ]]; then
        return
    fi

    log_step "Creating directory structure"

    mkdir -p "$INSTALL_DIR"/{scripts,docs}
    mkdir -p "$INSTALL_DIR"/src/database/{entities,migrations,seeds}

    log_success "Directories created"
}

# Download file with error handling
download_file() {
    local url="$1"
    local output="$2"
    local description="$3"

    log_info "Downloading $description..."
    
    if command -v curl &> /dev/null; then
        if curl -fsSL "$url" -o "$output"; then
            log_success "$description downloaded"
        else
            log_warning "Failed to download $description from $url"
            return 1
        fi
    elif command -v wget &> /dev/null; then
        if wget -q "$url" -O "$output"; then
            log_success "$description downloaded"
        else
            log_warning "Failed to download $description from $url"
            return 1
        fi
    else
        log_error "Neither curl nor wget is available"
        return 1
    fi
}

# Download scripts
download_scripts() {
    log_step "Downloading database scripts"

    # Create scripts array
    declare -A scripts=(
        ["setup-database.sh"]="Database setup script"
        ["reset-database.sh"]="Database reset script"
        ["test-connection.ts"]="Connection test script"
        ["generate-project-template.sh"]="Project template generator"
    )

    # Download each script
    for script in "${!scripts[@]}"; do
        download_file \
            "$REPO_BASE_URL/scripts/$script" \
            "$INSTALL_DIR/scripts/$script" \
            "${scripts[$script]}"
    done

    # Make scripts executable
    chmod +x "$INSTALL_DIR"/scripts/*.sh 2>/dev/null || true

    log_success "All scripts downloaded and made executable"
}

# Download documentation
download_docs() {
    log_step "Downloading documentation"

    declare -A docs=(
        ["DATABASE_SETUP_GUIDE.md"]="Complete setup guide"
        ["COMPLETE_SETUP_SUMMARY.md"]="Setup summary"
    )

    for doc in "${!docs[@]}"; do
        download_file \
            "$REPO_BASE_URL/docs/$doc" \
            "$INSTALL_DIR/docs/$doc" \
            "${docs[$doc]}"
    done

    log_success "Documentation downloaded"
}

# Create sample configuration files
create_sample_configs() {
    log_step "Creating sample configuration files"

    # Sample .env file
    if [[ ! -f "$INSTALL_DIR/.env.example" ]]; then
        cat > "$INSTALL_DIR/.env.example" << 'EOF'
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=your_username
DATABASE_PASSWORD=your_password
DATABASE_NAME=your_database
DATABASE_SYNCHRONIZE=false
DATABASE_LOGGING=false
SKIP_DATABASE=false

# Application
NODE_ENV=production
PORT=4000

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
EOF
        log_success "Sample .env.example created"
    fi

    # Sample TypeORM data source
    if [[ ! -f "$INSTALL_DIR/src/database/data-source.ts" ]]; then
        cat > "$INSTALL_DIR/src/database/data-source.ts" << 'EOF'
import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { config } from 'dotenv';

// Load environment variables
config();

const configService = new ConfigService();

const AppDataSource = new DataSource({
  type: 'postgres',
  url: configService.get('DATABASE_URL'),
  host: configService.get('DATABASE_HOST', 'localhost'),
  port: configService.get('DATABASE_PORT', 5432),
  username: configService.get('DATABASE_USERNAME'),
  password: configService.get('DATABASE_PASSWORD'),
  database: configService.get('DATABASE_NAME'),
  synchronize: configService.get('DATABASE_SYNCHRONIZE', 'false') === 'true',
  logging: configService.get('DATABASE_LOGGING', 'false') === 'true',
  entities: [__dirname + '/entities/*.entity{.ts,.js}'],
  migrations: [__dirname + '/migrations/*{.ts,.js}'],
  subscribers: [__dirname + '/subscribers/*{.ts,.js}'],
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

export default AppDataSource;
EOF
        log_success "Sample data-source.ts created"
    fi
}

# Update package.json
update_package_json() {
    if [[ ! -f "$INSTALL_DIR/package.json" ]]; then
        log_warning "package.json not found, skipping script updates"
        return
    fi

    log_step "Updating package.json scripts"

    # Check if jq is available
    if ! command -v jq &> /dev/null; then
        log_warning "jq not found, please manually add these scripts to package.json:"
        echo ""
        echo '"scripts": {'
        echo '  "migration:generate": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:generate -d src/database/data-source.ts",'
        echo '  "migration:run": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:run -d src/database/data-source.ts",'
        echo '  "migration:revert": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:revert -d src/database/data-source.ts",'
        echo '  "seed": "ts-node src/database/seeds/run-seeds.ts",'
        echo '  "db:setup": "./scripts/setup-database.sh",'
        echo '  "db:reset": "./scripts/reset-database.sh",'
        echo '  "db:test": "ts-node scripts/test-connection.ts"'
        echo '}'
        return
    fi

    # Add scripts using jq
    jq '.scripts += {
        "migration:generate": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:generate -d src/database/data-source.ts",
        "migration:run": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:run -d src/database/data-source.ts",
        "migration:revert": "ts-node -r tsconfig-paths/register ../node_modules/typeorm/cli.js migration:revert -d src/database/data-source.ts",
        "seed": "ts-node src/database/seeds/run-seeds.ts",
        "db:setup": "./scripts/setup-database.sh",
        "db:reset": "./scripts/reset-database.sh",
        "db:test": "ts-node scripts/test-connection.ts"
    }' "$INSTALL_DIR/package.json" > "$INSTALL_DIR/package.json.tmp" && mv "$INSTALL_DIR/package.json.tmp" "$INSTALL_DIR/package.json"

    log_success "package.json scripts updated"
}

# Show next steps
show_next_steps() {
    echo -e "\n${GREEN}🎉 Database scripts installed successfully!${NC}"
    echo -e "\n${BLUE}📁 Files installed:${NC}"
    echo -e "  scripts/setup-database.sh"
    echo -e "  scripts/reset-database.sh"
    echo -e "  scripts/test-connection.ts"
    echo -e "  scripts/generate-project-template.sh"
    echo -e "  docs/DATABASE_SETUP_GUIDE.md"
    echo -e "  docs/COMPLETE_SETUP_SUMMARY.md"
    
    echo -e "\n${BLUE}🎯 Next Steps:${NC}"
    echo -e "  1. Review the documentation in docs/"
    echo -e "  2. Copy .env.example to .env and configure"
    echo -e "  3. Run: ./scripts/setup-database.sh --name yourproject"
    echo -e "  4. Create your TypeORM entities"
    echo -e "  5. Generate and run migrations"
    
    echo -e "\n${BLUE}📚 Quick Commands:${NC}"
    echo -e "  ./scripts/setup-database.sh --help    # Show setup options"
    echo -e "  npm run db:test                       # Test connection"
    echo -e "  npm run db:reset                      # Reset database"
    
    echo -e "\n${BLUE}📖 Documentation:${NC}"
    echo -e "  docs/DATABASE_SETUP_GUIDE.md          # Complete guide"
    echo -e "  docs/COMPLETE_SETUP_SUMMARY.md        # Quick summary"
}

# Main execution
main() {
    echo -e "${GREEN}"
    echo "🚀 Database Scripts Installer for NestJS + TypeORM + PostgreSQL"
    echo "================================================================"
    echo -e "${NC}"

    parse_args "$@"
    create_directories
    download_scripts
    download_docs
    create_sample_configs
    update_package_json
    show_next_steps
}

# Run main function
main "$@"
