#!/bin/bash

# Kill processes on specific ports script

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default port
PORT=${1:-4000}

echo -e "${BLUE}🔍 Checking for processes on port $PORT...${NC}"

# Find processes using the port
PIDS=$(lsof -ti:$PORT 2>/dev/null)

if [ -z "$PIDS" ]; then
    echo -e "${GREEN}✅ Port $PORT is already free${NC}"
    exit 0
fi

echo -e "${YELLOW}⚠️  Found processes using port $PORT:${NC}"

# Show process details
lsof -i:$PORT

echo ""
echo -e "${YELLOW}🔄 Killing processes on port $PORT...${NC}"

# Kill the processes
for PID in $PIDS; do
    echo "Killing process $PID..."
    kill -9 $PID 2>/dev/null
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Successfully killed process $PID${NC}"
    else
        echo -e "${RED}❌ Failed to kill process $PID${NC}"
    fi
done

# Verify port is free
sleep 1
REMAINING=$(lsof -ti:$PORT 2>/dev/null)

if [ -z "$REMAINING" ]; then
    echo -e "${GREEN}🎉 Port $PORT is now free!${NC}"
else
    echo -e "${RED}❌ Some processes are still running on port $PORT${NC}"
    lsof -i:$PORT
    exit 1
fi
