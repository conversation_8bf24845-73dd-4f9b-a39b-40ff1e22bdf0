#!/bin/bash

# NPM Workaround Script
# This script works around the broken system npm by using local node_modules binaries
# and Node.js directly for package management tasks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}NPM Workaround Script${NC}"
echo -e "${YELLOW}=====================${NC}"

# Function to run commands with local binaries
run_local() {
    local cmd="$1"
    shift
    
    case "$cmd" in
        "dev")
            echo -e "${GREEN}Starting development servers...${NC}"
            ./node_modules/.bin/concurrently \
                "cd frontend && node ./node_modules/.bin/next dev" \
                "cd backend && node ./node_modules/.bin/nest start --watch"
            ;;
        "build")
            echo -e "${GREEN}Building all projects...${NC}"
            cd shared && node ../node_modules/.bin/tsc && cd ..
            cd frontend && node ../node_modules/.bin/next build && cd ..
            cd backend && node ../node_modules/.bin/nest build && cd ..
            cd blockchain && node ../node_modules/.bin/tsc && cd ..
            ;;
        "test")
            echo -e "${GREEN}Running tests...${NC}"
            cd frontend && node ../node_modules/.bin/jest && cd ..
            cd backend && node ../node_modules/.bin/jest && cd ..
            cd blockchain && node ../node_modules/.bin/jest && cd ..
            ;;
        "lint")
            echo -e "${GREEN}Running linters...${NC}"
            ./node_modules/.bin/eslint "**/*.{ts,tsx,js,jsx}" --fix
            ;;
        "format")
            echo -e "${GREEN}Formatting code...${NC}"
            ./node_modules/.bin/prettier --write "**/*.{ts,tsx,js,jsx,json,md}"
            ;;
        *)
            echo -e "${RED}Unknown command: $cmd${NC}"
            echo "Available commands: dev, build, test, lint, format"
            exit 1
            ;;
    esac
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}Error: package.json not found. Please run this script from the project root.${NC}"
    exit 1
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo -e "${RED}Error: node_modules not found. Dependencies may not be installed.${NC}"
    exit 1
fi

# Run the requested command
if [ $# -eq 0 ]; then
    echo "Usage: $0 <command>"
    echo "Available commands: dev, build, test, lint, format"
    exit 1
fi

run_local "$@"
