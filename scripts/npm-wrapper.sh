#!/bin/bash
# NPM Wrapper Script - uses working NVM npm instead of broken system npm

WORKING_NPM="$HOME/.nvm/versions/node/v22.14.0/bin/npm"

if [ ! -f "$WORKING_NPM" ]; then
    WORKING_NPM="$HOME/.nvm/versions/node/v18.20.8/bin/npm"
fi

if [ ! -f "$WORKING_NPM" ]; then
    echo "Error: No working npm found"
    exit 1
fi

# Use NODE_PATH to ensure proper module resolution
export NODE_PATH="$HOME/.nvm/versions/node/v22.14.0/lib/node_modules:$NODE_PATH"

exec "$WORKING_NPM" "$@"
