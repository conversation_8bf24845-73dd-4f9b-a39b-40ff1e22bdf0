#!/bin/bash

# Restart Backend Script - Kills existing processes and starts fresh

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 Restarting A Good Man's View Backend...${NC}"
echo ""

# Step 1: Kill existing processes on port 4000
echo -e "${YELLOW}Step 1: Cleaning up existing processes${NC}"
./scripts/kill-port.sh 4000

echo ""

# Step 2: Kill any node processes that might be related
echo -e "${YELLOW}Step 2: Cleaning up related Node.js processes${NC}"
pkill -f "nest start" 2>/dev/null || echo "No nest processes found"
pkill -f "backend" 2>/dev/null || echo "No backend processes found"

echo ""

# Step 3: Wait a moment for cleanup
echo -e "${YELLOW}Step 3: Waiting for cleanup...${NC}"
sleep 2

# Step 4: Start the backend
echo -e "${YELLOW}Step 4: Starting backend${NC}"
echo ""

cd backend

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Error: Not in backend directory or package.json not found${NC}"
    exit 1
fi

# Start the backend
echo -e "${GREEN}🚀 Starting A Good Man's View Backend...${NC}"
npm run dev
