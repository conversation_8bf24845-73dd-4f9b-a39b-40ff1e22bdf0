#!/bin/bash

# Authentication Setup Script
# This script helps set up the authentication system for the project

set -e

echo "🚀 Setting up Authentication System..."
echo "======================================"

# Change to project root
cd "$(dirname "$0")/.."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_status "Checking project structure..."

# Check if backend and frontend directories exist
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    print_error "Backend or frontend directory not found"
    exit 1
fi

print_success "Project structure verified"

# Step 1: Run database migration
print_status "Running database migration..."
cd backend

if [ -f "./scripts/run-auth-migration.sh" ]; then
    chmod +x ./scripts/run-auth-migration.sh
    ./scripts/run-auth-migration.sh
    print_success "Database migration completed"
else
    print_warning "Migration script not found, skipping..."
fi

# Step 2: Generate JWT secrets if .env doesn't exist or doesn't have JWT secrets
print_status "Setting up environment variables..."

if [ ! -f ".env" ]; then
    print_status "Creating .env file..."
    touch .env
fi

# Check if JWT secrets already exist
if ! grep -q "JWT_SECRET" .env; then
    print_status "Generating JWT secrets..."
    
    # Generate secure random secrets
    JWT_SECRET=$(openssl rand -base64 32)
    JWT_REFRESH_SECRET=$(openssl rand -base64 32)
    
    # Add to .env file
    echo "" >> .env
    echo "# JWT Configuration (Generated by setup script)" >> .env
    echo "JWT_SECRET=$JWT_SECRET" >> .env
    echo "JWT_REFRESH_SECRET=$JWT_REFRESH_SECRET" >> .env
    echo "JWT_EXPIRES_IN=15m" >> .env
    echo "JWT_REFRESH_EXPIRES_IN=7d" >> .env
    echo "" >> .env
    echo "# Security Configuration" >> .env
    echo "BCRYPT_SALT_ROUNDS=12" >> .env
    echo "MAX_LOGIN_ATTEMPTS=5" >> .env
    echo "ACCOUNT_LOCK_TIME=7200000" >> .env
    echo "" >> .env
    echo "# Rate Limiting" >> .env
    echo "THROTTLE_TTL=60000" >> .env
    echo "THROTTLE_LIMIT=5" >> .env
    
    print_success "JWT secrets generated and added to .env"
else
    print_warning "JWT secrets already exist in .env, skipping generation"
fi

cd ..

# Step 3: Install any missing dependencies
print_status "Checking dependencies..."

cd backend
if ! npm list @nestjs/jwt @nestjs/passport passport passport-jwt bcryptjs >/dev/null 2>&1; then
    print_status "Installing missing backend dependencies..."
    npm install @nestjs/jwt @nestjs/passport passport passport-jwt
    npm install --save-dev @types/passport-jwt
    print_success "Backend dependencies installed"
else
    print_success "Backend dependencies already installed"
fi

cd ../frontend
if ! npm list react-hook-form @hookform/resolvers zod >/dev/null 2>&1; then
    print_status "Installing missing frontend dependencies..."
    npm install react-hook-form @hookform/resolvers zod
    print_success "Frontend dependencies installed"
else
    print_success "Frontend dependencies already installed"
fi

cd ..

# Step 4: Display next steps
echo ""
echo "🎉 Authentication setup complete!"
echo "================================="
echo ""
echo "📋 Next Steps:"
echo "1. Start the development servers:"
echo "   ${BLUE}cd backend && npm run dev${NC}"
echo "   ${BLUE}cd frontend && npm run dev${NC}"
echo ""
echo "2. Begin implementation following the roadmap:"
echo "   ${BLUE}docs/AUTHENTICATION_IMPLEMENTATION_ROADMAP.md${NC}"
echo ""
echo "3. Track progress with the checklist:"
echo "   ${BLUE}docs/AUTHENTICATION_IMPLEMENTATION_CHECKLIST.md${NC}"
echo ""
echo "4. Start with these files (Day 1):"
echo "   - ${YELLOW}backend/src/modules/auth/dto/register.dto.ts${NC}"
echo "   - ${YELLOW}backend/src/modules/auth/dto/login.dto.ts${NC}"
echo "   - ${YELLOW}backend/src/modules/auth/dto/auth-response.dto.ts${NC}"
echo ""
echo "🔧 Useful commands:"
echo "   ${BLUE}npm run test${NC}                 # Run tests"
echo "   ${BLUE}npm run migration:show${NC}       # Check migration status"
echo "   ${BLUE}npm run db:docker:up${NC}         # Start database"
echo ""
echo "📚 Documentation:"
echo "   - Implementation guide: ${BLUE}docs/AUTHENTICATION_SETUP.md${NC}"
echo "   - Folder structure: ${BLUE}docs/AUTHENTICATION_FOLDER_SETUP_COMPLETE.md${NC}"
echo ""
print_success "Ready to implement authentication! 🚀"
