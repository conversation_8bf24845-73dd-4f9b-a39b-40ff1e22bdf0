#!/bin/bash

# Database Setup Script for A Good Man's View
# This script sets up PostgreSQL database and user for the project

set -e

echo "🗄️ Setting up PostgreSQL database for A Good Man's View..."

# Database configuration
DB_NAME="agoodmansview_db"
DB_USER="agoodmansview_user"
DB_PASSWORD="agoodmansview_password"

echo "📋 Database Configuration:"
echo "  Database: $DB_NAME"
echo "  User: $DB_USER"
echo "  Password: $DB_PASSWORD"
echo ""

# Function to run SQL commands as postgres user
run_sql() {
    sudo -u postgres psql -c "$1"
}

echo "🔧 Creating database and user..."

# Create database
echo "Creating database: $DB_NAME"
sudo -u postgres createdb "$DB_NAME" || echo "Database may already exist"

# Create user and grant privileges
echo "Creating user and setting up permissions..."
run_sql "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';" || echo "User may already exist"
run_sql "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"
run_sql "ALTER USER $DB_USER CREATEDB;"

echo ""
echo "✅ Database setup complete!"
echo ""
echo "📝 Update your backend/.env file with these settings:"
echo "DATABASE_HOST=localhost"
echo "DATABASE_PORT=5432"
echo "DATABASE_USERNAME=$DB_USER"
echo "DATABASE_PASSWORD=$DB_PASSWORD"
echo "DATABASE_NAME=$DB_NAME"
echo "SKIP_DATABASE=false"
echo ""
echo "🚀 You can now start your backend server!"
