#!/bin/bash

# A Good Man's View - Project Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up A Good Man's View development environment..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "⚠️  Docker is not installed. Some features may not work without Docker."
else
    echo "✅ Docker version: $(docker --version)"
fi

# Install root dependencies
echo "📦 Installing root dependencies..."
npm install

# Setup shared package
echo "📦 Setting up shared package..."
cd shared
npm install
npm run build
cd ..

# Setup frontend
echo "🎨 Setting up frontend (Next.js)..."
cd frontend
npm install
cd ..

# Setup backend
echo "🔧 Setting up backend (NestJS)..."
cd backend
npm install
cd ..

# Setup blockchain
echo "⛓️  Setting up blockchain..."
cd blockchain
npm install
cd ..

# Copy environment files
echo "📝 Setting up environment files..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
    echo "⚠️  Please update the .env file with your configuration"
fi

if [ ! -f frontend/.env.local ]; then
    cp frontend/.env.local.example frontend/.env.local 2>/dev/null || echo "⚠️  Frontend .env.local.example not found"
fi

if [ ! -f backend/.env ]; then
    cp backend/.env.example backend/.env 2>/dev/null || echo "⚠️  Backend .env.example not found"
fi

# Setup Git hooks
echo "🔗 Setting up Git hooks..."
npx husky install

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p logs
mkdir -p uploads
mkdir -p data/blockchain
mkdir -p data/postgres
mkdir -p data/redis

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Update the .env file with your configuration"
echo "2. Start the development environment:"
echo "   npm run dev"
echo ""
echo "🐳 Or use Docker:"
echo "   docker-compose up -d"
echo ""
echo "📚 Documentation:"
echo "   - Frontend: http://localhost:3000"
echo "   - Backend API: http://localhost:4000/api/docs"
echo "   - GraphQL Playground: http://localhost:4000/graphql"
echo "   - Grafana: http://localhost:3001 (admin/admin)"
echo ""
echo "Happy coding! 🚀"
