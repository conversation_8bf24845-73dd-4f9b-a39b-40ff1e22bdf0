#!/bin/bash

# Test Backend Without Database Script

echo "🧪 Testing Backend API without database..."

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if backend is running
echo "📡 Checking if backend is running..."

# Test root endpoint
if curl -s http://localhost:4000/ > /dev/null; then
    echo -e "${GREEN}✅ Backend is running on http://localhost:4000${NC}"
else
    echo -e "${RED}❌ Backend is not running. Starting it...${NC}"
    echo "Run: npm run dev:backend"
    exit 1
fi

echo ""
echo "🔍 Testing API endpoints..."

# Test root endpoint
echo "1. Testing root endpoint..."
RESPONSE=$(curl -s http://localhost:4000/)
if [[ $RESPONSE == *"A Good Man's View API"* ]]; then
    echo -e "${GREEN}✅ Root endpoint working${NC}"
else
    echo -e "${RED}❌ Root endpoint failed${NC}"
fi

# Test API endpoints
echo "3. Testing API endpoints..."

# Auth endpoint
AUTH_RESPONSE=$(curl -s -X POST http://localhost:4000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test"}')

if [[ $AUTH_RESPONSE == *"Login endpoint"* ]]; then
    echo -e "${GREEN}✅ Auth endpoint working${NC}"
else
    echo -e "${RED}❌ Auth endpoint failed${NC}"
fi

# Users endpoint
USERS_RESPONSE=$(curl -s http://localhost:4000/api/v1/users)
if [[ $USERS_RESPONSE == *"Users endpoint"* ]]; then
    echo -e "${GREEN}✅ Users endpoint working${NC}"
else
    echo -e "${RED}❌ Users endpoint failed${NC}"
fi

# Products endpoint
PRODUCTS_RESPONSE=$(curl -s http://localhost:4000/api/v1/products)
if [[ $PRODUCTS_RESPONSE == *"Products endpoint"* ]]; then
    echo -e "${GREEN}✅ Products endpoint working${NC}"
else
    echo -e "${RED}❌ Products endpoint failed${NC}"
fi

echo ""
echo "📚 Available endpoints:"
echo "• API Documentation: http://localhost:4000/api/docs"
echo "• GraphQL Playground: http://localhost:4000/graphql"
echo "• API Info: http://localhost:4000/"

echo ""
echo -e "${GREEN}🎉 Backend API is working without database!${NC}"
echo ""
echo "💡 To enable database:"
echo "1. Set up PostgreSQL (see DATABASE_SETUP_GUIDE.md)"
echo "2. Update backend/.env: SKIP_DATABASE=false"
echo "3. Restart backend: npm run dev:backend"
