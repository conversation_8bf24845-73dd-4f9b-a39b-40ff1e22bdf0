#!/bin/bash

# Database Connectivity Test Script for Issue #63

echo "🗄️ Testing PostgreSQL and TypeORM Configuration..."

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

TESTS_PASSED=0
TESTS_FAILED=0

# Function to test PostgreSQL connection directly
test_postgres_connection() {
    echo -n "Testing PostgreSQL Connection... "

    # Try to connect to PostgreSQL using psql or docker
    if command -v psql >/dev/null 2>&1; then
        # Test with psql if available
        if PGPASSWORD=password psql -h localhost -p 5432 -U postgres -d agoodmansview_db -c "SELECT 1;" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ CONNECTED${NC}"
            TESTS_PASSED=$((TESTS_PASSED + 1))
            return 0
        else
            echo -e "${RED}❌ FAILED${NC}"
            TESTS_FAILED=$((TESTS_FAILED + 1))
            return 1
        fi
    else
        # Test with docker if psql not available
        if docker exec -it $(docker ps -q -f name=postgres) psql -U postgres -d agoodmansview_db -c "SELECT 1;" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ CONNECTED${NC}"
            TESTS_PASSED=$((TESTS_PASSED + 1))
            return 0
        else
            echo -e "${RED}❌ FAILED${NC}"
            echo "  Make sure PostgreSQL is running: docker-compose up postgres -d"
            TESTS_FAILED=$((TESTS_FAILED + 1))
            return 1
        fi
    fi
}

# Function to test backend startup with database
test_backend_startup() {
    echo -n "Testing Backend Startup with Database... "

    # Check if backend is running
    if curl -s http://localhost:4000/ >/dev/null 2>&1; then
        echo -e "${GREEN}✅ RUNNING${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        echo -e "${RED}❌ NOT RUNNING${NC}"
        echo "  Start backend: cd backend && npm run start:dev"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Function to test TypeORM configuration
test_typeorm_config() {
    echo -n "Testing TypeORM Configuration... "

    # Check if backend logs show successful database connection
    # This is implicit - if backend starts successfully with SKIP_DATABASE=false, TypeORM is working
    if [ "$SKIP_DATABASE" != "true" ] && curl -s http://localhost:4000/ >/dev/null 2>&1; then
        echo -e "${GREEN}✅ CONFIGURED${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        echo -e "${YELLOW}⚠️  SKIPPED OR FAILED${NC}"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

echo ""
echo -e "${BLUE}🔍 Testing Database Connectivity Requirements${NC}"
echo "=================================================="

echo ""
echo -e "${BLUE}📋 Testing Acceptance Criteria${NC}"
echo "=================================="

# Test 1: PostgreSQL database is running
test_postgres_connection

# Test 2: Backend starts successfully with database
test_backend_startup

# Test 3: TypeORM connects successfully to database
test_typeorm_config

# Test 4: Environment configuration is working
echo -n "Testing Environment Configuration... "
if [ -f "backend/.env.example" ] && grep -q "DATABASE_HOST" backend/.env.example; then
    echo -e "${GREEN}✅ CONFIGURED${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}❌ NOT CONFIGURED${NC}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi

# Test 5: Connection pooling is configured
echo -n "Testing Connection Pooling Configuration... "
if grep -q "DATABASE_MAX_CONNECTIONS" backend/.env.example && grep -q "extra:" backend/src/database/database.module.ts; then
    echo -e "${GREEN}✅ CONFIGURED${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}❌ NOT CONFIGURED${NC}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi

echo ""
echo -e "${BLUE}📊 Test Results Summary${NC}"
echo "========================"

TOTAL_TESTS=$((TESTS_PASSED + TESTS_FAILED))
if [ $TOTAL_TESTS -gt 0 ]; then
    SUCCESS_RATE=$((TESTS_PASSED * 100 / TOTAL_TESTS))
else
    SUCCESS_RATE=0
fi

echo "Tests Passed: $TESTS_PASSED"
echo "Tests Failed: $TESTS_FAILED"
echo "Success Rate: $SUCCESS_RATE%"

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 All database connectivity tests passed!${NC}"
    echo -e "${GREEN}✅ Issue #63 requirements are fully met${NC}"
    echo ""
    echo "✅ PostgreSQL database is running"
    echo "✅ TypeORM connects successfully to database"
    echo "✅ Environment configuration is working"
    echo "✅ Connection pooling is configured"
    echo "✅ Database connection is stable"
    exit 0
else
    echo ""
    echo -e "${RED}❌ Some database connectivity tests failed${NC}"
    echo -e "${YELLOW}⚠️  Issue #63 requirements are not fully met${NC}"
    echo ""
    echo "Please check:"
    echo "  1. PostgreSQL is running (docker-compose up postgres -d)"
    echo "  2. Environment variables are set correctly"
    echo "  3. Database connection configuration is valid"
    echo "  4. Backend starts without errors"
    exit 1
fi
