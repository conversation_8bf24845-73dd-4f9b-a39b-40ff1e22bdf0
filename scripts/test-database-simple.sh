#!/bin/bash

# Simple Database Test Script for Issue #63
# Tests core database connectivity without health endpoints

echo "🗄️ Testing PostgreSQL and TypeORM Configuration (Issue #63)..."

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo ""
echo "📋 Checking Issue #63 Requirements:"
echo "=================================="

# 1. Check if PostgreSQL is configured in Docker
echo -n "1. PostgreSQL Docker Configuration... "
if grep -q "postgres:" docker-compose.yml && grep -q "POSTGRES_DB=agoodmansview_db" docker-compose.yml; then
    echo -e "${GREEN}✅ CONFIGURED${NC}"
else
    echo -e "${RED}❌ NOT CONFIGURED${NC}"
fi

# 2. Check TypeORM and PostgreSQL driver installation
echo -n "2. TypeORM and PostgreSQL Driver... "
if grep -q '"typeorm"' backend/package.json && grep -q '"pg"' backend/package.json && grep -q '"@nestjs/typeorm"' backend/package.json; then
    echo -e "${GREEN}✅ INSTALLED${NC}"
else
    echo -e "${RED}❌ NOT INSTALLED${NC}"
fi

# 3. Check database connection configuration
echo -n "3. Database Connection Configuration... "
if [ -f "backend/src/database/database.module.ts" ] && grep -q "TypeOrmModule.forRootAsync" backend/src/database/database.module.ts; then
    echo -e "${GREEN}✅ CONFIGURED${NC}"
else
    echo -e "${RED}❌ NOT CONFIGURED${NC}"
fi

# 4. Check environment variables
echo -n "4. Environment Variables Setup... "
if [ -f "backend/.env.example" ] && grep -q "DATABASE_HOST" backend/.env.example && grep -q "DATABASE_PORT" backend/.env.example; then
    echo -e "${GREEN}✅ CONFIGURED${NC}"
else
    echo -e "${RED}❌ NOT CONFIGURED${NC}"
fi

# 5. Check TypeORM module in app.module.ts
echo -n "5. TypeORM Module Integration... "
if grep -q "DatabaseModule" backend/src/app.module.ts; then
    echo -e "${GREEN}✅ INTEGRATED${NC}"
else
    echo -e "${RED}❌ NOT INTEGRATED${NC}"
fi

# 6. Check connection pooling configuration
echo -n "6. Connection Pooling Configuration... "
if grep -q "extra:" backend/src/database/database.module.ts && grep -q "DATABASE_MAX_CONNECTIONS" backend/.env.example; then
    echo -e "${GREEN}✅ CONFIGURED${NC}"
else
    echo -e "${RED}❌ NOT CONFIGURED${NC}"
fi

# 7. Check environment-specific configurations
echo -n "7. Environment-Specific Configs... "
if grep -q "NODE_ENV" backend/.env.example && grep -q "production" backend/src/database/database.module.ts; then
    echo -e "${GREEN}✅ CONFIGURED${NC}"
else
    echo -e "${RED}❌ NOT CONFIGURED${NC}"
fi

# 8. Check if PostgreSQL is actually running
echo -n "8. PostgreSQL Service Status... "
if docker ps | grep -q postgres || pgrep -x postgres >/dev/null; then
    echo -e "${GREEN}✅ RUNNING${NC}"
else
    echo -e "${YELLOW}⚠️  NOT RUNNING${NC}"
    echo "   Start with: docker-compose up postgres -d"
fi

echo ""
echo "🎯 Acceptance Criteria Check:"
echo "============================"

# Check each acceptance criteria
echo -n "✓ PostgreSQL database is running... "
if docker ps | grep -q postgres; then
    echo -e "${GREEN}✅ MET${NC}"
else
    echo -e "${RED}❌ NOT MET${NC}"
fi

echo -n "✓ TypeORM connects successfully... "
if [ -f "backend/src/database/database.module.ts" ] && grep -q "type: 'postgres'" backend/src/database/database.module.ts; then
    echo -e "${GREEN}✅ CONFIGURED${NC}"
else
    echo -e "${RED}❌ NOT CONFIGURED${NC}"
fi

echo -n "✓ Environment configuration working... "
if [ -f "backend/.env.example" ] && grep -q "DATABASE_" backend/.env.example; then
    echo -e "${GREEN}✅ MET${NC}"
else
    echo -e "${RED}❌ NOT MET${NC}"
fi

echo -n "✓ Connection pooling configured... "
if grep -q "extra:" backend/src/database/database.module.ts; then
    echo -e "${GREEN}✅ MET${NC}"
else
    echo -e "${RED}❌ NOT MET${NC}"
fi

echo -n "✓ Database connection is stable... "
if curl -s http://localhost:4000/ >/dev/null 2>&1; then
    echo -e "${GREEN}✅ BACKEND RUNNING${NC}"
else
    echo -e "${YELLOW}⚠️  TEST BACKEND${NC}"
    echo "   Start backend: cd backend && npm run start:dev"
fi

echo ""
echo "📝 Next Steps to Complete Issue #63:"
echo "===================================="
echo "1. Start PostgreSQL: docker-compose up postgres -d"
echo "2. Copy environment file: cp backend/.env.example backend/.env"
echo "3. Start backend: cd backend && npm run start:dev"
echo "4. Verify connection in backend logs"

echo ""
echo "🔍 Manual Testing Commands:"
echo "=========================="
echo "# Test PostgreSQL connection:"
echo "docker exec -it \$(docker ps -q -f name=postgres) psql -U postgres -d agoodmansview_db -c 'SELECT 1;'"
echo ""
echo "# Test backend startup:"
echo "cd backend && npm run start:dev"
echo ""
echo "# Check backend logs for database connection success"
