#!/bin/bash

# Test All Backend Endpoints Script

echo "🧪 Testing all backend endpoints..."

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

BASE_URL="http://localhost:4000"

# Function to test endpoint
test_endpoint() {
    local url=$1
    local description=$2
    local expected_content=$3
    
    echo -n "Testing $description... "
    
    response=$(curl -s "$url" 2>/dev/null)
    status=$?
    
    if [ $status -eq 0 ] && [[ $response == *"$expected_content"* ]]; then
        echo -e "${GREEN}✅ PASS${NC}"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC}"
        echo "  URL: $url"
        echo "  Response: $response"
        return 1
    fi
}

echo ""
echo -e "${BLUE}🔍 Testing Core Endpoints${NC}"
echo "=================================="

# Test root endpoint
test_endpoint "$BASE_URL/" "Root endpoint" "Welcome to A Good Man's View API"

# Test info endpoint
test_endpoint "$BASE_URL/info" "Info endpoint" "A Good Man's View API"

echo ""
echo -e "${BLUE}🔍 Testing REST API Endpoints${NC}"
echo "=================================="

# Test API endpoints
test_endpoint "$BASE_URL/api/v1/users" "Users API" "Users endpoint"
test_endpoint "$BASE_URL/api/v1/products" "Products API" "Products endpoint"
test_endpoint "$BASE_URL/api/v1/orders" "Orders API" "Orders endpoint"
test_endpoint "$BASE_URL/api/v1/wallet" "Wallet API" "Wallet endpoint"

echo ""
echo -e "${BLUE}🔍 Testing Authentication Endpoints${NC}"
echo "=================================="

# Test auth endpoints with POST
echo -n "Testing Auth Login... "
auth_response=$(curl -s -X POST "$BASE_URL/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test"}' 2>/dev/null)

if [[ $auth_response == *"Login endpoint"* ]]; then
    echo -e "${GREEN}✅ PASS${NC}"
else
    echo -e "${RED}❌ FAIL${NC}"
    echo "  Response: $auth_response"
fi

echo -n "Testing Auth Register... "
register_response=$(curl -s -X POST "$BASE_URL/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test"}' 2>/dev/null)

if [[ $register_response == *"Registration endpoint"* ]]; then
    echo -e "${GREEN}✅ PASS${NC}"
else
    echo -e "${RED}❌ FAIL${NC}"
    echo "  Response: $register_response"
fi

echo ""
echo -e "${BLUE}🔍 Testing Documentation Endpoints${NC}"
echo "=================================="

# Test documentation endpoints
test_endpoint "$BASE_URL/api/docs" "Swagger Documentation" "swagger"

echo ""
echo -e "${BLUE}🔍 Testing GraphQL Endpoint${NC}"
echo "=================================="

# Test GraphQL endpoint
echo -n "Testing GraphQL Playground... "
graphql_response=$(curl -s "$BASE_URL/graphql" 2>/dev/null)

if [[ $graphql_response == *"GraphQL"* ]] || [[ $graphql_response == *"playground"* ]]; then
    echo -e "${GREEN}✅ PASS${NC}"
else
    echo -e "${YELLOW}⚠️  GraphQL might be disabled${NC}"
fi

# Test GraphQL query
echo -n "Testing GraphQL Query... "
graphql_query_response=$(curl -s -X POST "$BASE_URL/graphql" \
  -H "Content-Type: application/json" \
  -d '{"query":"{ hello }"}' 2>/dev/null)

if [[ $graphql_query_response == *"Hello"* ]]; then
    echo -e "${GREEN}✅ PASS${NC}"
else
    echo -e "${YELLOW}⚠️  GraphQL query failed or disabled${NC}"
fi

echo ""
echo -e "${BLUE}📊 Summary${NC}"
echo "=================================="
echo "✅ Available endpoints:"
echo "  • Root: $BASE_URL/"
echo "  • API Docs: $BASE_URL/api/docs"
echo "  • GraphQL: $BASE_URL/graphql"
echo "  • REST API: $BASE_URL/api/v1/*"
echo ""
echo "🎯 Key URLs for development:"
echo "  • Frontend: http://localhost:3000"
echo "  • Backend API: $BASE_URL/api/docs"
echo "  • GraphQL Playground: $BASE_URL/graphql"
echo ""
echo -e "${GREEN}🎉 Backend endpoint testing complete!${NC}"
