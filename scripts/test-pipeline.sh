#!/bin/bash

# 🧪 Local Pipeline Testing Script
# 
# This script allows developers to run the complete CI pipeline locally
# before pushing changes, ensuring faster feedback and fewer CI failures.
#
# Usage: ./scripts/test-pipeline.sh [options]
# Options:
#   --quick     Run only essential tests (unit + lint)
#   --full      Run complete test suite including E2E
#   --coverage  Generate coverage reports
#   --fix       Auto-fix linting and formatting issues

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
QUICK_MODE=false
FULL_MODE=false
COVERAGE_MODE=false
FIX_MODE=false
START_TIME=$(date +%s)

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --quick)
      QUICK_MODE=true
      shift
      ;;
    --full)
      FULL_MODE=true
      shift
      ;;
    --coverage)
      COVERAGE_MODE=true
      shift
      ;;
    --fix)
      FIX_MODE=true
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--quick|--full] [--coverage] [--fix]"
      exit 1
      ;;
  esac
done

# Helper functions
print_header() {
  echo -e "\n${BLUE}========================================${NC}"
  echo -e "${BLUE} $1${NC}"
  echo -e "${BLUE}========================================${NC}\n"
}

print_success() {
  echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
  echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
  echo -e "${RED}❌ $1${NC}"
}

print_info() {
  echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
check_prerequisites() {
  print_header "🔍 Checking Prerequisites"
  
  # Check Node.js version
  if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    exit 1
  fi
  
  NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
  if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18 or higher is required (current: $(node --version))"
    exit 1
  fi
  print_success "Node.js version: $(node --version)"
  
  # Check npm version
  if ! command -v npm &> /dev/null; then
    print_error "npm is not installed"
    exit 1
  fi
  print_success "npm version: $(npm --version)"
  
  # Check Docker (for integration tests)
  if ! command -v docker &> /dev/null; then
    print_warning "Docker is not installed - integration tests will be skipped"
  else
    print_success "Docker version: $(docker --version | cut -d' ' -f3 | cut -d',' -f1)"
  fi
  
  # Check if we're in the right directory
  if [ ! -f "package.json" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
  fi
  
  print_success "All prerequisites met"
}

# Install dependencies
install_dependencies() {
  print_header "📦 Installing Dependencies"
  
  print_info "Installing root dependencies..."
  npm ci --silent
  
  print_info "Installing frontend dependencies..."
  cd frontend && npm ci --silent && cd ..
  
  print_info "Installing backend dependencies..."
  cd backend && npm ci --silent && cd ..
  
  print_info "Installing blockchain dependencies..."
  cd blockchain && npm ci --silent && cd ..
  
  print_info "Installing shared dependencies..."
  cd shared && npm ci --silent && cd ..
  
  print_success "All dependencies installed"
}

# Linting and formatting
lint_and_format() {
  print_header "🔍 Linting and Formatting"
  
  if [ "$FIX_MODE" = true ]; then
    print_info "Auto-fixing linting and formatting issues..."
    
    # Format code
    npm run format || print_warning "Formatting failed"
    
    # Fix linting issues
    npm run lint:frontend -- --fix || print_warning "Frontend linting fixes failed"
    npm run lint:backend -- --fix || print_warning "Backend linting fixes failed"
    npm run lint:blockchain -- --fix || print_warning "Blockchain linting fixes failed"
    
    print_success "Auto-fix completed"
  else
    print_info "Checking code formatting..."
    cd frontend && npm run format:check || print_error "Frontend formatting issues found"
    cd ..
    
    print_info "Running linting checks..."
    npm run lint:frontend || print_error "Frontend linting issues found"
    npm run lint:backend || print_error "Backend linting issues found"
    npm run lint:blockchain || print_error "Blockchain linting issues found"
    
    print_success "Linting and formatting checks passed"
  fi
}

# Type checking
type_check() {
  print_header "🔧 Type Checking"
  
  print_info "Checking TypeScript types..."
  cd frontend && npm run type-check && cd ..
  cd backend && npx tsc --noEmit && cd ..
  cd blockchain && npx tsc --noEmit && cd ..
  cd shared && npx tsc --noEmit && cd ..
  
  print_success "Type checking passed"
}

# Unit tests
run_unit_tests() {
  print_header "🧪 Running Unit Tests"
  
  if [ "$COVERAGE_MODE" = true ]; then
    print_info "Running unit tests with coverage..."
    cd frontend && npm run test:coverage && cd ..
    cd backend && npm run test:cov && cd ..
    cd blockchain && npm test -- --coverage && cd ..
    cd shared && npm test -- --coverage && cd ..
    
    print_success "Unit tests with coverage completed"
  else
    print_info "Running unit tests..."
    cd frontend && npm test -- --run && cd ..
    cd backend && npm test && cd ..
    cd blockchain && npm test && cd ..
    cd shared && npm test && cd ..
    
    print_success "Unit tests passed"
  fi
}

# Integration tests
run_integration_tests() {
  print_header "🔗 Running Integration Tests"
  
  # Check if Docker is available
  if ! command -v docker &> /dev/null; then
    print_warning "Docker not available - skipping integration tests"
    return
  fi
  
  print_info "Starting test services..."
  docker-compose -f docker-compose.test.yml up -d postgres redis
  
  # Wait for services to be ready
  print_info "Waiting for services to be ready..."
  sleep 10
  
  # Run database migrations
  print_info "Running database migrations..."
  cd backend && npm run migration:run && cd ..
  
  # Run integration tests
  print_info "Running integration tests..."
  cd frontend && npm run test -- --run tests/integration/ && cd ..
  cd backend && npm run test:e2e && cd ..
  
  # Cleanup
  print_info "Cleaning up test services..."
  docker-compose -f docker-compose.test.yml down
  
  print_success "Integration tests passed"
}

# E2E tests
run_e2e_tests() {
  print_header "🎭 Running End-to-End Tests"
  
  print_info "Installing Playwright browsers..."
  cd frontend && npx playwright install chromium --with-deps
  
  print_info "Building application..."
  npm run build:frontend
  
  print_info "Running E2E tests..."
  cd frontend && npm run test:e2e -- --project=chromium
  
  print_success "E2E tests passed"
}

# Build verification
build_verification() {
  print_header "🏗️ Build Verification"
  
  print_info "Building all workspaces..."
  npm run build:shared
  npm run build:frontend
  npm run build:backend
  npm run build:blockchain
  
  print_success "All builds completed successfully"
}

# Security checks
security_checks() {
  print_header "🔒 Security Checks"
  
  print_info "Running dependency audit..."
  npm audit --audit-level=moderate
  
  print_info "Checking for secrets in code..."
  if command -v git &> /dev/null; then
    # Simple secret detection (basic patterns)
    if git log --all --full-history -- | grep -i -E "(password|secret|key|token)" | grep -v "test" | head -5; then
      print_warning "Potential secrets found in git history - please review"
    else
      print_success "No obvious secrets found in git history"
    fi
  fi
  
  print_success "Security checks completed"
}

# Performance checks
performance_checks() {
  print_header "⚡ Performance Checks"
  
  print_info "Analyzing bundle size..."
  cd frontend && npm run build:analyze
  
  # Check bundle size limits (example thresholds)
  BUNDLE_SIZE=$(du -sh .next/static/chunks/*.js 2>/dev/null | awk '{sum+=$1} END {print sum}' || echo "0")
  print_info "Current bundle size: ${BUNDLE_SIZE}KB"
  
  print_success "Performance checks completed"
}

# Generate summary report
generate_summary() {
  END_TIME=$(date +%s)
  DURATION=$((END_TIME - START_TIME))
  
  print_header "📊 Test Summary"
  
  echo -e "${GREEN}✅ Pipeline completed successfully!${NC}"
  echo -e "${BLUE}📊 Execution time: ${DURATION} seconds${NC}"
  echo -e "${BLUE}🕐 Completed at: $(date)${NC}"
  
  if [ "$COVERAGE_MODE" = true ]; then
    echo -e "${BLUE}📈 Coverage reports generated in:${NC}"
    echo -e "   - frontend/coverage/"
    echo -e "   - backend/coverage/"
    echo -e "   - blockchain/coverage/"
    echo -e "   - shared/coverage/"
  fi
  
  echo -e "\n${GREEN}🚀 Ready to push to repository!${NC}"
}

# Main execution flow
main() {
  print_header "🚀 Local CI Pipeline Test"
  print_info "Mode: $([ "$QUICK_MODE" = true ] && echo "Quick" || ([ "$FULL_MODE" = true ] && echo "Full" || echo "Standard"))"
  print_info "Coverage: $([ "$COVERAGE_MODE" = true ] && echo "Enabled" || echo "Disabled")"
  print_info "Auto-fix: $([ "$FIX_MODE" = true ] && echo "Enabled" || echo "Disabled")"
  
  # Always run these checks
  check_prerequisites
  install_dependencies
  lint_and_format
  type_check
  
  if [ "$QUICK_MODE" = true ]; then
    # Quick mode: only essential tests
    run_unit_tests
    build_verification
  elif [ "$FULL_MODE" = true ]; then
    # Full mode: complete test suite
    run_unit_tests
    run_integration_tests
    run_e2e_tests
    build_verification
    security_checks
    performance_checks
  else
    # Standard mode: comprehensive but skip E2E
    run_unit_tests
    run_integration_tests
    build_verification
    security_checks
  fi
  
  generate_summary
}

# Error handling
trap 'print_error "Pipeline failed at line $LINENO. Exit code: $?"' ERR

# Run main function
main "$@"
