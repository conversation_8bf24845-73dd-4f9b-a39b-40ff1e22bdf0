#!/bin/bash

# A Good Man's View - Structure Test Script
# This script tests if the folder structure is properly configured

set -e

echo "🧪 Testing A Good Man's View project structure..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0

# Function to test if a file exists
test_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✅ $1${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ $1${NC}"
        ((TESTS_FAILED++))
    fi
}

# Function to test if a directory exists
test_dir() {
    if [ -d "$1" ]; then
        echo -e "${GREEN}✅ $1/${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ $1/${NC}"
        ((TESTS_FAILED++))
    fi
}

echo ""
echo "📁 Testing Root Structure..."
test_file "package.json"
test_file "docker-compose.yml"
test_file ".env.example"
test_file "PROJECT_STRUCTURE.md"
test_file "FOLDER_STRUCTURE_GENERATED.md"

echo ""
echo "🎨 Testing Frontend Structure..."
test_dir "frontend"
test_dir "frontend/src"
test_dir "frontend/src/app"
test_dir "frontend/src/components"
test_dir "frontend/src/lib"
test_file "frontend/src/app/layout.tsx"
test_file "frontend/src/app/page.tsx"
test_file "frontend/src/app/globals.css"
test_file "frontend/src/components/ui/button.tsx"
test_file "frontend/src/lib/utils.ts"

echo ""
echo "🔧 Testing Backend Structure..."
test_dir "backend"
test_dir "backend/src"
test_dir "backend/src/modules"
test_file "backend/package.json"
test_file "backend/src/main.ts"
test_file "backend/src/app.module.ts"
test_file "backend/src/app.controller.ts"
test_file "backend/src/app.service.ts"


echo ""
echo "⛓️ Testing Blockchain Structure..."
test_dir "blockchain"
test_dir "blockchain/src"
test_dir "blockchain/src/core"
test_file "blockchain/package.json"
test_file "blockchain/src/main.ts"
test_file "blockchain/src/core/blockchain.ts"
test_file "blockchain/src/core/block.ts"
test_file "blockchain/src/core/transaction.ts"
test_file "blockchain/src/utils/logger.ts"

echo ""
echo "🔗 Testing Shared Structure..."
test_dir "shared"
test_dir "shared/src"
test_file "shared/package.json"
test_file "shared/tsconfig.json"
test_file "shared/src/index.ts"
test_file "shared/src/types/common.types.ts"
test_file "shared/src/constants/app.constants.ts"

echo ""
echo "🏗️ Testing Infrastructure Structure..."
test_dir "infrastructure"
test_dir "infrastructure/docker"
test_dir "infrastructure/kubernetes"
test_file "infrastructure/docker/frontend.Dockerfile"
test_file "infrastructure/docker/backend.Dockerfile"
test_file "infrastructure/kubernetes/frontend/deployment.yaml"

echo ""
echo "📚 Testing Documentation Structure..."
test_dir "docs"
test_file "docs/README.md"
test_dir "docs/api"
test_dir "docs/architecture"
test_dir "docs/deployment"

echo ""
echo "🧪 Testing Test Structure..."
test_dir "frontend/tests"
test_dir "backend/test"
test_file "frontend/tests/components/button.test.tsx"


echo ""
echo "🔄 Testing CI/CD Structure..."
test_dir ".github"
test_dir ".github/workflows"
test_file ".github/workflows/ci.yml"
test_file ".github/PULL_REQUEST_TEMPLATE.md"

echo ""
echo "🛠️ Testing Scripts..."
test_dir "scripts"
test_file "scripts/setup.sh"
test_file "scripts/test-structure.sh"

echo ""
echo "📊 Test Results:"
echo -e "${GREEN}✅ Tests Passed: $TESTS_PASSED${NC}"
echo -e "${RED}❌ Tests Failed: $TESTS_FAILED${NC}"

TOTAL_TESTS=$((TESTS_PASSED + TESTS_FAILED))
SUCCESS_RATE=$((TESTS_PASSED * 100 / TOTAL_TESTS))

echo -e "${YELLOW}📈 Success Rate: $SUCCESS_RATE%${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 All tests passed! Project structure is properly configured.${NC}"
    echo ""
    echo "🚀 Next steps:"
    echo "1. Run: chmod +x scripts/setup.sh && ./scripts/setup.sh"
    echo "2. Start development: npm run dev"
    echo "3. Or use Docker: docker-compose up -d"
    exit 0
else
    echo ""
    echo -e "${RED}❌ Some tests failed. Please check the missing files/directories.${NC}"
    exit 1
fi
