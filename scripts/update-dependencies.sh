#!/bin/bash

# Dependency Update Script
# This script helps update dependencies after the deprecated ones have been replaced

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Dependency Update Script${NC}"
echo -e "${BLUE}======================${NC}"

# Function to check if npm wrapper exists
check_npm_wrapper() {
    if [ ! -f "./scripts/npm-wrapper.sh" ]; then
        echo -e "${RED}Error: npm-wrapper.sh not found. Please run fix-npm.sh first.${NC}"
        exit 1
    fi
}

# Function to update dependencies in a workspace
update_workspace() {
    local workspace=$1
    echo -e "${YELLOW}Updating dependencies in $workspace...${NC}"
    
    if [ -d "$workspace" ]; then
        cd "$workspace"
        ../scripts/npm-wrapper.sh install
        cd ..
        echo -e "${GREEN}✓ Updated $workspace dependencies${NC}"
    else
        echo -e "${YELLOW}⚠ Workspace $workspace not found, skipping...${NC}"
    fi
}

# Function to setup husky
setup_husky() {
    echo -e "${YELLOW}Setting up Husky...${NC}"
    ./scripts/npm-wrapper.sh run prepare 2>/dev/null || ./scripts/npm-wrapper.sh exec husky install
    echo -e "${GREEN}✓ Husky setup complete${NC}"
}

# Main execution
main() {
    echo -e "${BLUE}Starting dependency update process...${NC}"
    
    # Check prerequisites
    check_npm_wrapper
    
    # Update root dependencies
    echo -e "${YELLOW}Updating root dependencies...${NC}"
    ./scripts/npm-wrapper.sh install
    echo -e "${GREEN}✓ Updated root dependencies${NC}"
    
    # Update workspace dependencies
    update_workspace "frontend"
    update_workspace "backend"
    update_workspace "blockchain"
    update_workspace "shared"
    
    # Setup husky
    setup_husky
    
    # Run tests to verify everything works
    echo -e "${YELLOW}Running tests to verify updates...${NC}"
    if ./scripts/npm-wrapper.sh run test 2>/dev/null; then
        echo -e "${GREEN}✓ All tests passed${NC}"
    else
        echo -e "${YELLOW}⚠ Some tests failed. Please check the migration guide.${NC}"
    fi
    
    echo -e "${GREEN}✓ Dependency update completed!${NC}"
    echo -e "${BLUE}Next steps:${NC}"
    echo -e "  1. Review DEPENDENCY_MIGRATION_GUIDE.md"
    echo -e "  2. Update code for deprecated dependencies"
    echo -e "  3. Run tests: ${YELLOW}npm run test${NC}"
    echo -e "  4. Run build: ${YELLOW}npm run build${NC}"
}

# Run main function
main "$@"
