const fs = require("fs");
const path = require("path");

const packagePath = path.join(process.cwd(), "package.json");
const pkg = JSON.parse(fs.readFileSync(packagePath, "utf8"));

// Update scripts to use npm wrapper
const scriptsToUpdate = {
  setup:
    "./scripts/npm-wrapper.sh install && ./scripts/npm-wrapper.sh run setup:frontend && ./scripts/npm-wrapper.sh run setup:backend && ./scripts/npm-wrapper.sh run setup:blockchain",
  "setup:frontend": "cd frontend && ../scripts/npm-wrapper.sh install",
  "setup:backend": "cd backend && ../scripts/npm-wrapper.sh install",
  "setup:blockchain": "cd blockchain && ../scripts/npm-wrapper.sh install",
};

Object.assign(pkg.scripts, scriptsToUpdate);

fs.writeFileSync(packagePath, JSON.stringify(pkg, null, 2) + "\n");
console.log("✓ Updated package.json scripts");
