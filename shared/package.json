{"name": "@agoodmansview/shared", "version": "1.0.0", "description": "Shared types, constants, and utilities for A Good Man's View platform", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "files": ["dist"], "dependencies": {"class-validator": "^0.14.2", "class-transformer": "^0.5.1"}, "devDependencies": {"@types/node": "^24.0.3", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint": "^9.29.0", "jest": "^30.0.2", "@types/jest": "^30.0.0", "ts-jest": "^29.4.0", "typescript": "^5.8.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/**/*.interface.ts"]}}