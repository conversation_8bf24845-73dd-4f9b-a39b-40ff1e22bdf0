/**
 * Application-wide constants
 */

export const APP_CONFIG = {
  NAME: 'A Good Man\'s View',
  VERSION: '1.0.0',
  DESCRIPTION: 'Multi-vendor e-commerce platform with blockchain integration',
  AUTHOR: 'A Good Man\'s View Team',
  WEBSITE: 'https://agoodmansview.co.za',
  SUPPORT_EMAIL: '<EMAIL>',
} as const;

export const CURRENCIES = {
  ZAR: {
    code: 'ZAR',
    symbol: 'R',
    name: 'South African Rand',
    decimals: 2,
  },
  USD: {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    decimals: 2,
  },
} as const;

export const SOUTH_AFRICAN_PROVINCES = [
  'Eastern Cape',
  'Free State',
  'Gauteng',
  'KwaZulu-Natal',
  'Limpopo',
  'Mpumalanga',
  'Northern Cape',
  'North West',
  'Western Cape',
] as const;

export const USER_ROLES = {
  ADMIN: 'ADMIN',
  VENDOR: 'VENDOR',
  BUYER: 'BUYER',
  MODERATOR: 'MODERATOR',
} as const;

export const SUBSCRIPTION_TIERS = {
  BASIC: {
    name: 'Basic',
    price: 99,
    commission: 0.05,
    maxProducts: 50,
    features: ['Basic analytics', 'Email support'],
  },
  PREMIUM: {
    name: 'Premium',
    price: 299,
    commission: 0.03,
    maxProducts: 200,
    features: ['Advanced analytics', 'Priority support', 'Marketing tools'],
  },
  ENTERPRISE: {
    name: 'Enterprise',
    price: 599,
    commission: 0.02,
    maxProducts: -1, // Unlimited
    features: ['Full analytics', '24/7 support', 'Custom integrations', 'Dedicated account manager'],
  },
} as const;

export const ORDER_STATUSES = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  PROCESSING: 'PROCESSING',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED',
} as const;

export const PAYMENT_STATUSES = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED',
} as const;
