// Blockchain-related types
export interface BlockchainNetwork {
  id: string;
  name: string;
  chainId: number;
  rpcUrl: string;
  explorerUrl: string;
  nativeCurrency: BlockchainCurrency;
  isTestnet: boolean;
  isActive: boolean;
}

export interface BlockchainCurrency {
  name: string;
  symbol: string;
  decimals: number;
}

export interface SmartContract {
  id: string;
  name: string;
  address: string;
  networkId: string;
  abi: any[];
  bytecode?: string;
  isVerified: boolean;
  createdAt: Date;
}

export interface BlockchainTransaction {
  hash: string;
  blockNumber: number;
  blockHash: string;
  transactionIndex: number;
  from: string;
  to: string;
  value: string;
  gasLimit: string;
  gasUsed: string;
  gasPrice: string;
  nonce: number;
  status: number;
  timestamp: Date;
  confirmations: number;
}

export interface Block {
  number: number;
  hash: string;
  parentHash: string;
  timestamp: Date;
  gasLimit: string;
  gasUsed: string;
  miner: string;
  difficulty: string;
  totalDifficulty: string;
  size: number;
  transactionCount: number;
  transactions: string[];
}

export interface TokenInfo {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  totalSupply: string;
  logoUrl?: string;
}

export interface NFTMetadata {
  name: string;
  description: string;
  image: string;
  attributes: NFTAttribute[];
  external_url?: string;
}

export interface NFTAttribute {
  trait_type: string;
  value: string | number;
  display_type?: string;
}

export interface ContractEvent {
  address: string;
  topics: string[];
  data: string;
  blockNumber: number;
  transactionHash: string;
  transactionIndex: number;
  blockHash: string;
  logIndex: number;
  removed: boolean;
}
