// Product-related types
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  category: string;
  images: string[];
  inStock: boolean;
  stockQuantity: number;
  sku: string;
  weight?: number;
  dimensions?: ProductDimensions;
  tags: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductDimensions {
  length: number;
  width: number;
  height: number;
  unit: 'cm' | 'in';
}

export interface CreateProductRequest {
  name: string;
  description: string;
  price: number;
  currency: string;
  category: string;
  images?: string[];
  stockQuantity: number;
  sku: string;
  weight?: number;
  dimensions?: ProductDimensions;
  tags?: string[];
}

export interface UpdateProductRequest {
  name?: string;
  description?: string;
  price?: number;
  currency?: string;
  category?: string;
  images?: string[];
  inStock?: boolean;
  stockQuantity?: number;
  weight?: number;
  dimensions?: ProductDimensions;
  tags?: string[];
  isActive?: boolean;
}

export interface ProductFilter {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  tags?: string[];
  search?: string;
}

export interface ProductListResponse {
  products: Product[];
  total: number;
  page: number;
  limit: number;
}
