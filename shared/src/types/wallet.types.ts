// Wallet-related types
export interface Wallet {
  id: string;
  userId: string;
  address: string;
  balance: number;
  currency: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface WalletTransaction {
  id: string;
  walletId: string;
  type: TransactionType;
  amount: number;
  currency: string;
  fromAddress?: string;
  toAddress?: string;
  transactionHash?: string;
  blockNumber?: number;
  gasUsed?: number;
  gasPrice?: number;
  status: TransactionStatus;
  description?: string;
  createdAt: Date;
  confirmedAt?: Date;
}

export enum TransactionType {
  DEPOSIT = 'deposit',
  WITHDRAWAL = 'withdrawal',
  TRANSFER = 'transfer',
  PAYMENT = 'payment',
  REFUND = 'refund',
  FEE = 'fee',
}

export enum TransactionStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export interface CreateWalletRequest {
  userId: string;
  currency: string;
}

export interface WalletTransferRequest {
  fromWalletId: string;
  toAddress: string;
  amount: number;
  description?: string;
}

export interface WalletBalance {
  walletId: string;
  balance: number;
  currency: string;
  pendingBalance: number;
}

export interface WalletSummary {
  totalBalance: number;
  currency: string;
  totalTransactions: number;
  pendingTransactions: number;
  lastTransactionDate?: Date;
}
