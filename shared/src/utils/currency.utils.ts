// Currency utility functions
export interface CurrencyFormatOptions {
  locale?: string;
  currency?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
}

export const formatCurrency = (
  amount: number,
  options: CurrencyFormatOptions = {}
): string => {
  const {
    locale = 'en-US',
    currency = 'USD',
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
  } = options;

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits,
    maximumFractionDigits,
  }).format(amount);
};

export const parseCurrency = (currencyString: string): number => {
  // Remove currency symbols and spaces, then parse as float
  const cleanString = currencyString.replace(/[^\d.-]/g, '');
  return parseFloat(cleanString) || 0;
};

export const convertCurrency = (
  amount: number,
  fromCurrency: string,
  toCurrency: string,
  exchangeRate: number
): number => {
  if (fromCurrency === toCurrency) {
    return amount;
  }
  return amount * exchangeRate;
};

export const roundToDecimals = (amount: number, decimals: number = 2): number => {
  return Math.round(amount * Math.pow(10, decimals)) / Math.pow(10, decimals);
};

export const calculatePercentage = (amount: number, percentage: number): number => {
  return (amount * percentage) / 100;
};

export const calculateTax = (amount: number, taxRate: number): number => {
  return calculatePercentage(amount, taxRate);
};

export const calculateDiscount = (
  originalPrice: number,
  discountPercentage: number
): { discountAmount: number; finalPrice: number } => {
  const discountAmount = calculatePercentage(originalPrice, discountPercentage);
  const finalPrice = originalPrice - discountAmount;
  
  return {
    discountAmount: roundToDecimals(discountAmount),
    finalPrice: roundToDecimals(finalPrice),
  };
};

export const isValidCurrencyCode = (currencyCode: string): boolean => {
  // Basic validation for ISO 4217 currency codes
  return /^[A-Z]{3}$/.test(currencyCode);
};

export const formatCryptoCurrency = (
  amount: number,
  symbol: string,
  decimals: number = 8
): string => {
  return `${amount.toFixed(decimals)} ${symbol}`;
};

export const weiToEther = (wei: string | number): number => {
  const weiAmount = typeof wei === 'string' ? parseFloat(wei) : wei;
  return weiAmount / Math.pow(10, 18);
};

export const etherToWei = (ether: number): string => {
  return (ether * Math.pow(10, 18)).toString();
};
