// Validation utility functions
import { VALIDATION_RULES, ERROR_MESSAGES } from '../constants/validation.constants';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export const validateEmail = (email: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!email) {
    errors.push(ERROR_MESSAGES.REQUIRED);
  } else {
    if (email.length < VALIDATION_RULES.EMAIL.MIN_LENGTH || 
        email.length > VALIDATION_RULES.EMAIL.MAX_LENGTH) {
      errors.push(`Email must be between ${VALIDATION_RULES.EMAIL.MIN_LENGTH} and ${VALIDATION_RULES.EMAIL.MAX_LENGTH} characters`);
    }
    
    if (!VALIDATION_RULES.EMAIL.PATTERN.test(email)) {
      errors.push(ERROR_MESSAGES.INVALID_EMAIL);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const validatePassword = (password: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!password) {
    errors.push(ERROR_MESSAGES.REQUIRED);
  } else {
    if (password.length < VALIDATION_RULES.PASSWORD.MIN_LENGTH || 
        password.length > VALIDATION_RULES.PASSWORD.MAX_LENGTH) {
      errors.push(`Password must be between ${VALIDATION_RULES.PASSWORD.MIN_LENGTH} and ${VALIDATION_RULES.PASSWORD.MAX_LENGTH} characters`);
    }
    
    if (!VALIDATION_RULES.PASSWORD.PATTERN.test(password)) {
      errors.push(ERROR_MESSAGES.INVALID_PASSWORD);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const validateUsername = (username: string): ValidationResult => {
  const errors: string[] = [];
  
  if (!username) {
    errors.push(ERROR_MESSAGES.REQUIRED);
  } else {
    if (username.length < VALIDATION_RULES.USERNAME.MIN_LENGTH || 
        username.length > VALIDATION_RULES.USERNAME.MAX_LENGTH) {
      errors.push(`Username must be between ${VALIDATION_RULES.USERNAME.MIN_LENGTH} and ${VALIDATION_RULES.USERNAME.MAX_LENGTH} characters`);
    }
    
    if (!VALIDATION_RULES.USERNAME.PATTERN.test(username)) {
      errors.push(ERROR_MESSAGES.INVALID_USERNAME);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const validatePhone = (phone: string): ValidationResult => {
  const errors: string[] = [];
  
  if (phone && !VALIDATION_RULES.PHONE.PATTERN.test(phone)) {
    errors.push(ERROR_MESSAGES.INVALID_PHONE);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const validateRequired = (value: any, fieldName: string): ValidationResult => {
  const errors: string[] = [];
  
  if (value === null || value === undefined || value === '') {
    errors.push(`${fieldName} is required`);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};
