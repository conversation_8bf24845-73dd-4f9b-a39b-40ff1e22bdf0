{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "noEmit": true}, "exclude": ["node_modules", "dist", "build", ".next", "out", "**/*.md", "frontend/**/*", "backend/**/*", "shared/**/*", "blockchain/**/*"], "references": [{"path": "./frontend"}, {"path": "./backend"}, {"path": "./shared"}, {"path": "./blockchain"}]}